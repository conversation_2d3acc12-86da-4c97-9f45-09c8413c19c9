# RLS Policies Overview - FashionLab Platform

## User Roles

1. **platform_super** - Full system access, can do everything
2. **platform_admin** - Cross-organization support access
3. **brand_admin** - Organization administrator
4. **brand_member** - Regular organization member
5. **external_retoucher** - External contractor with limited write
6. **external_prompter** - External contractor with read-only

## Permissions by Table

### Organizations Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ All orgs | ✅ | ✅ All orgs | ✅ |
| platform_admin | ✅ All orgs | ✅ | ✅ All orgs | ❌ |
| brand_admin | ✅ Own org | ❌ | ✅ Own org | ❌ |
| brand_member | ✅ Own org | ❌ | ❌ | ❌ |
| external_retoucher | ✅ Own org | ❌ | ❌ | ❌ |
| external_prompter | ✅ Own org | ❌ | ❌ | ❌ |

**Policies:**
- `orgs_select_policy`: Platform users OR members can view their org
- `orgs_insert_policy`: Only platform_super/platform_admin can create
- `orgs_update_policy`: Platform users OR brand_admin of that org
- `orgs_delete_policy`: Only platform_super can delete

### Users Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| All authenticated | ✅ All profiles | N/A | ✅ Own profile only* | ❌ |

**Policies:**
- `users_select_policy`: Everyone can view all user profiles
- `users_update_policy`: Users can only update their own profile
  - *Note: Only platform admins can change user roles

### Organization Memberships Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ All | ✅ | ✅ | ✅ |
| platform_admin | ✅ All | ✅ | ✅ | ✅ |
| brand_admin | ✅ Own org | ✅ Own org | ✅ Own org | ✅ Own org |
| brand_member | ✅ Own membership | ❌ | ❌ | ❌ |
| external_retoucher | ✅ Own membership | ❌ | ❌ | ❌ |
| external_prompter | ✅ Own membership | ❌ | ❌ | ❌ |

**Policies:**
- `memberships_select_policy`: Platform users OR own membership OR members of same org
- `memberships_insert_policy`: Platform users OR brand_admin of that org
- `memberships_update_policy`: Platform users OR brand_admin of that org
- `memberships_delete_policy`: Platform users OR brand_admin of that org

### Collections Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ All | ✅ Any org | ✅ Any org | ✅ Any org |
| platform_admin | ✅ All | ✅ Any org | ✅ Any org | ✅ Any org |
| brand_admin | ✅ Own org | ✅ Own org | ✅ Own org | ✅ Own org |
| brand_member | ✅ Own org | ✅ Own org | ❌ | ❌ |
| external_retoucher | ✅ Own org | ✅ Own org | ❌ | ❌ |
| external_prompter | ✅ Own org | ❌ | ❌ | ❌ |

**Policies:**
- `collections_select_policy`: Platform users OR members of the org
- `collections_insert_policy`: Platform users (any org) OR members of the org
- `collections_update_policy`: Platform users OR brand_admin of that org
- `collections_delete_policy`: Platform users OR brand_admin of that org

### Products Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ All | ✅ | ✅ | ✅ |
| platform_admin | ✅ All | ✅ | ✅ | ✅ |
| brand_admin | ✅ Own org | ✅ | ✅ | ✅ Own org |
| brand_member | ✅ Own org | ✅ | ✅ | ❌ |
| external_retoucher | ✅ Own org | ✅ | ✅ | ❌ |
| external_prompter | ✅ Own org | ❌ | ❌ | ❌ |

**Policies:**
- `products_select_policy`: Can view if member of collection's org
- `products_insert_policy`: Any member of collection's org
- `products_update_policy`: Any member of collection's org
- `products_delete_policy`: Platform users OR brand_admin

### Assets Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ All | ✅ | ✅ | ✅ |
| platform_admin | ✅ All | ✅ | ✅ | ✅ |
| brand_admin | ✅ Own org | ✅ | ✅ | ✅ |
| brand_member | ✅ Own org | ✅ | ✅ | ✅ |
| external_retoucher | ✅ Own org | ✅ | ✅ | ✅ |
| external_prompter | ✅ Own org | ❌ | ❌ | ❌ |

**Policies:**
- `assets_select_policy`: Can view if member of collection's org
- `assets_insert_policy`: Any member of collection's org
- `assets_update_policy`: Any member of collection's org
- `assets_delete_policy`: Any member of collection's org

### Tags Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| All authenticated | ✅ All tags | ✅ | ❌ | ❌ |
| platform_super | ✅ | ✅ | ✅ | ✅ |
| platform_admin | ✅ | ✅ | ✅ | ✅ |

**Policies:**
- `tags_select_policy`: All authenticated users can view
- `tags_insert_policy`: Any authenticated user can create
- `tags_update_policy`: Only platform users
- `tags_delete_policy`: Only platform users

### Asset Tags Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ All | ✅ | N/A | ✅ |
| platform_admin | ✅ All | ✅ | N/A | ✅ |
| brand_admin | ✅ Own org | ✅ | N/A | ✅ |
| brand_member | ✅ Own org | ✅ | N/A | ✅ |
| external_retoucher | ✅ Own org | ✅ | N/A | ✅ |
| external_prompter | ✅ Own org | ❌ | N/A | ❌ |

**Policies:**
- `asset_tags_select_policy`: Can view if member of asset's org
- `asset_tags_insert_policy`: Any member of asset's org
- `asset_tags_delete_policy`: Any member of asset's org

### Comments Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ All | ✅ | ✅ Own | ✅ Own |
| platform_admin | ✅ All | ✅ | ✅ Own | ✅ Own |
| brand_admin | ✅ Own org | ✅ | ✅ Own | ✅ Own |
| brand_member | ✅ Own org | ✅ | ✅ Own | ✅ Own |
| external_retoucher | ✅ Own org | ✅ | ✅ Own | ✅ Own |
| external_prompter | ✅ Own org | ✅ | ✅ Own | ✅ Own |

**Policies:**
- `comments_select_policy`: Can view if member of asset's org
- `comments_insert_policy`: Must be own comment AND member of asset's org
- `comments_update_policy`: Can only update own comments
- `comments_delete_policy`: Can only delete own comments

### Comment Mentions Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| All with access to comment | ✅ | ✅ In own comments | N/A | N/A |

**Policies:**
- `mentions_select_policy`: Can view if can view the comment
- `mentions_insert_policy`: Can create in own comments only

### Pending Invitations Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ All | ✅ | ✅ | ✅ |
| platform_admin | ✅ All | ✅ | ✅ | ✅ |
| brand_admin | ✅ Own org | ✅ Own org | ✅ Own org | ✅ Own org |
| brand_member | ✅ Own email | ❌ | ✅ Own (accept) | ❌ |
| Anonymous | ✅ Valid by token | ❌ | ❌ | ❌ |

**Policies:**
- `Organization admins can manage invitations`: Full access for admins
- `Anyone can view their invitation by token`: Anonymous access for valid invites
- `Authenticated users can view their invitations`: By email match
- `Users can accept their invitations`: Update own invitation

### Security Activity Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ All | System only | N/A | N/A |
| platform_admin | ✅ All | System only | N/A | N/A |
| All users | ✅ Own activity | System only | N/A | N/A |

**Policies:**
- `security_select_policy`: Own activity OR platform users see all
- `security_insert_policy`: System can always insert

### Bulk Uploads Table

| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ All | ✅ | N/A | N/A |
| platform_admin | ✅ All | ✅ | N/A | N/A |
| brand_admin | ✅ Own org | ✅ | N/A | N/A |
| brand_member | ✅ Own org | ✅ | N/A | N/A |
| external_retoucher | ✅ Own org | ✅ | N/A | N/A |
| external_prompter | ✅ Own org | ❌ | N/A | N/A |

**Policies:**
- `bulk_uploads_select_policy`: Can view if member of collection's org
- `bulk_uploads_insert_policy`: Own upload AND member of collection's org

## Key Security Patterns

### 1. Multi-Tenant Isolation
All data is isolated by organization membership. Users can only see data from organizations they belong to.

### 2. Role-Based Access
- **Platform roles** (platform_super, platform_admin) bypass organization boundaries
- **Brand admin** has full control within their organization
- **Brand members** have read/write access but limited admin functions
- **External users** have restricted access (prompter is read-only)

### 3. Personal Data Protection
- Users can only update their own profiles
- Users can only update/delete their own comments
- Role changes require platform admin privileges

### 4. Invitation Security
- Only organization admins can create invitations
- Invitations are time-limited (7 days)
- Anonymous users can view invitations by token for acceptance flow
- Users can only accept invitations sent to their email

## Helper Functions

The RLS policies use these helper functions:

```sql
-- Check if user is in an organization
auth.user_in_organization(org_id UUID) -> BOOLEAN

-- Get current user's role
auth.user_role() -> user_role

-- Check if user is organization admin
auth.is_organization_admin(org_id UUID) -> BOOLEAN

-- Check if user is platform user
auth.is_platform_user() -> BOOLEAN
```

## Storage Buckets (Separate RLS System)

Storage buckets use Supabase Storage policies (not database RLS):

| Bucket | Public Read | Authenticated Upload | Organization Isolated |
|--------|------------|---------------------|----------------------|
| profiles | ✅ | ✅ | ✅ |
| assets | ❌ | ✅ | ✅ |
| thumbnails | ✅ | ✅ | ✅ |
| compressed | ✅ | ✅ | ✅ |
| general-uploads | ❌ | ✅ | ✅ |

## Testing RLS Policies

To test policies, use different user accounts with different roles:

```sql
-- Test as specific user
SET LOCAL role TO 'authenticated';
SET LOCAL request.jwt.claims TO 
  jsonb_build_object('sub', 'user-uuid-here');

-- Then run queries to verify access
SELECT * FROM organizations; -- Should only see own org
```

## Common Issues and Solutions

1. **"Permission denied" errors**
   - Check user's role in users table
   - Verify organization membership
   - Ensure auth.uid() is set (user is authenticated)

2. **Can't create/update/delete**
   - Verify user has correct role (brand_admin for admin functions)
   - Check if operation requires platform user role

3. **Can't see data**
   - Verify user is member of the organization
   - Check if data has correct foreign key relationships