// Paste this into your browser console when logged into Fashion Lab
// It will copy your access token to the clipboard

// Function to get the access token
function getAccessToken() {
  // Look for the Supabase auth token in localStorage
  const keys = Object.keys(localStorage);
  const authKey = keys.find(key => key.includes('auth-token'));
  
  if (!authKey) {
    console.error('No auth token found. Make sure you are logged in.');
    return null;
  }
  
  try {
    const authData = JSON.parse(localStorage.getItem(authKey));
    const accessToken = authData?.access_token;
    
    if (!accessToken) {
      console.error('Access token not found in auth data');
      return null;
    }
    
    return accessToken;
  } catch (error) {
    console.error('Error parsing auth data:', error);
    return null;
  }
}

// Get the token
const token = getAccessToken();

if (token) {
  // Copy to clipboard
  navigator.clipboard.writeText(token).then(() => {
    console.log('✅ Access token copied to clipboard!');
    console.log('Token preview:', token.substring(0, 20) + '...');
    console.log('');
    console.log('Now you can:');
    console.log('1. Paste this token in Postman as SUPABASE_ACCESS_TOKEN');
    console.log('2. Use it in the Authorization header as: Bearer ' + token.substring(0, 10) + '...');
  }).catch(err => {
    console.error('Failed to copy to clipboard:', err);
    console.log('Access token:', token);
  });
} else {
  console.error('Failed to retrieve access token');
}