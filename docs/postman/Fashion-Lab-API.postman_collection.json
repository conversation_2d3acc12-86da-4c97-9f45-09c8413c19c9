{"info": {"_postman_id": "fashion-lab-api-collection", "name": "Fashion Lab API", "description": "Collection for testing Fashion Lab image generation API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. Get JWT Token", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{SUPABASE_ACCESS_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{SUPABASE_URL}}/functions/v1/fashionlab-jwt", "host": ["{{SUPABASE_URL}}"], "path": ["functions", "v1", "fashionlab-jwt"]}, "description": "Get a JWT token for authenticating with the Fashion Lab API"}, "response": []}, {"name": "2. Generate Images", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{SUPABASE_ACCESS_TOKEN}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"prompt\": \"A scandinavian fashion model, blonde hair, young woman in her twenties, thin and petite build, natural features, full body shot, facing camera directly, standing pose, wearing sleek black leather jacket with silver zippers, fitted silhouette, cropped length, pure white seamless backdrop, studio lighting, high fashion editorial style, dramatic poses, artistic composition, ratio:9:16, format:jpeg, cfg:7.5\",\n  \"model_id\": \"S\",\n  \"lora_name\": \"bubbleroom_model_s_v1\",\n  \"lora_weight\": 1.0,\n  \"angle\": \"Full body face on\",\n  \"seed\": 42,\n  \"cfg\": 7.5,\n  \"flux_guidance\": 0.7,\n  \"num_images\": 1,\n  \"aspect_ratio\": \"9:16\",\n  \"format\": \"jpeg\",\n  \"collection_id\": \"{{COLLECTION_ID}}\",\n  \"product_id\": \"1\",\n  \"metadata\": {\n    \"modelName\": \"Model S - Scandinavian\",\n    \"angleName\": \"Full body face on\",\n    \"productSKU\": \"137240235\",\n    \"productName\": \"Sleek Leather Biker Jacket\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{SUPABASE_URL}}/functions/v1/generate-images", "host": ["{{SUPABASE_URL}}"], "path": ["functions", "v1", "generate-images"]}, "description": "Generate AI fashion images"}, "response": []}, {"name": "3. Check Queue Status (TODO)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{SUPABASE_ACCESS_TOKEN}}", "type": "text"}], "url": {"raw": "{{SUPABASE_URL}}/functions/v1/check-queue-status?queue_id={{QUEUE_ID}}", "host": ["{{SUPABASE_URL}}"], "path": ["functions", "v1", "check-queue-status"], "query": [{"key": "queue_id", "value": "{{QUEUE_ID}}"}]}, "description": "Check the status of a queued image generation job (Edge function needs to be created)"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Save JWT token from response", "if (pm.info.requestName === \"1. Get JWT Token\") {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set(\"JWT_TOKEN\", response.token);", "        console.log(\"JWT token saved to environment\");", "    }", "}", "", "// Save queue ID from image generation response", "if (pm.info.requestName === \"2. Generate Images\") {", "    const response = pm.response.json();", "    if (response.queue_id) {", "        pm.environment.set(\"QUEUE_ID\", response.queue_id);", "        console.log(\"Queue ID saved to environment:\", response.queue_id);", "    }", "}"]}}], "variable": [{"key": "SUPABASE_URL", "value": "http://localhost:54321", "type": "string"}, {"key": "SUPABASE_ACCESS_TOKEN", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nrtVMOY8Lsk39q45lS808KlDQSBypdFpNlkcZTF1gtY", "type": "string"}, {"key": "COLLECTION_ID", "value": "b2b18cf5-edca-418f-be28-2bc720a3fb4d", "type": "string"}]}