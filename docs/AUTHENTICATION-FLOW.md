# Fashion Lab API Authentication Flow

## Overview

This document explains the secure authentication flow for Fashion Lab API access, where clients never see the master API token.

## Architecture

```
Client App
    ↓ (1) Authenticate with Supabase
Supabase Auth
    ↓ (2) Request JWT with Supabase token
/fashionlab-jwt Edge Function
    ↓ (3) Returns client-specific JWT
Client App
    ↓ (4) Use JWT for API calls
/generate-images Edge Function
    ↓ (5) Edge function uses master token
Fashion Lab API
```

## Authentication Flow

### Step 1: Client Authentication
First, the client authenticates with Supabase:
```javascript
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
})
```

### Step 2: Get Fashion Lab JWT
The authenticated client requests a JWT from your edge function:
```javascript
const response = await fetch('/functions/v1/fashionlab-jwt', {
  headers: {
    'Authorization': `Bearer ${supabase.auth.session.access_token}`
  }
})
const { token } = await response.json()
```

### Step 3: Use JWT for API Calls
The client uses the JWT for Fashion Lab API operations:
```javascript
const generateResponse = await fetch('/functions/v1/generate-images', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    prompt: 'elegant dress',
    // ... other params
  })
})
```

### Step 4: Edge Function Handles API Call
The edge function validates the JWT and uses the master token to call Fashion Lab API.

## Environment Variables

```env
# Master API token (server-side only)
FASHIONLAB_API_TOKEN=2ms4LQBtkbvJ8RwFmBht

# JWT signing secret
FASHIONLAB_JWT_SECRET=your-secret-key-here
```

## Security Benefits

1. **Token Isolation**: Master API token never leaves server
2. **User Tracking**: JWTs can include user/org info
3. **Expiration**: JWTs expire after 1 hour
4. **Revocation**: Can invalidate JWTs if needed
5. **Rate Limiting**: Can implement per-user limits

## Testing the Flow

### 1. Generate JWT (as authenticated user)
```bash
# First, get a Supabase session token, then:
curl -X POST http://localhost:54321/functions/v1/fashionlab-jwt \
  -H "Authorization: Bearer YOUR_SUPABASE_TOKEN"
```

Response:
```json
{
  "token": "eyJhbGci...",
  "expires_in": 3600,
  "token_type": "Bearer"
}
```

### 2. Use JWT for Image Generation
```bash
curl -X POST http://localhost:54321/functions/v1/generate-images \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "elegant dress",
    "model_id": "Laila",
    "lora_name": "fashion_model_12345",
    "collection_id": "your-collection-id"
  }'
```

## Alternative: Direct Proxy Approach

Use the `/fashion-lab-proxy` function to proxy any Fashion Lab API endpoint:

```bash
# Instead of calling Fashion Lab directly:
# https://fashionlab.notfirst.rodeo/generate-image-v2

# Call through proxy with JWT:
curl -X POST http://localhost:54321/functions/v1/fashion-lab-proxy/generate-image-v2 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "model=@model.jpg" \
  -F "garment=@garment.jpg" \
  -F "pose=@pose.jpg" \
  -F "background=@background.jpg" \
  -F "prompt=your prompt"
```

## JWT Structure

The JWT contains:
```json
{
  "iss": "fashionlab-app",
  "iat": 1234567890,
  "exp": 1234571490,
  "scope": "image:generate image:refine"
}
```

You can extend this with:
- `sub`: User ID
- `org`: Organization ID
- `limits`: Rate limits
- `permissions`: Specific permissions