# 🚀 FashionLab Getting Started Guide

**Welcome!** This guide will help you set up and work with the FashionLab platform, even if you're not a technical developer. Everything is explained in simple terms with clear steps.

## 📖 What This Guide Covers

- How to set up your development environment
- How to check if everything is working
- How to deploy changes safely
- How to solve common problems
- Where to get help

## 🎯 Quick Start (5 Minutes)

If you just want to get started quickly:

```bash
# 1. Check if your development environment is ready
npm run dev:check

# 2. Check your environment variables
npm run env:check

# 3. Start developing
npm run dev
```

If any of these steps show errors, follow the detailed setup below.

## 🛠️ Initial Setup

### Step 1: Make Sure You Have the Basics

You need these installed on your computer:

- **Node.js** (version 18 or newer) - [Download here](https://nodejs.org/)
- **Git** - [Download here](https://git-scm.com/)
- **Supabase CLI** - Install with: `npm install -g @supabase/cli`

### Step 2: Get Your Environment Variables

Environment variables are like settings that tell the application how to connect to databases and services.

1. **Copy the template file:**
   ```bash
   cp .env.example .env.local
   ```

2. **Edit the .env.local file** with your actual values:
   - Open `.env.local` in any text editor
   - Follow the comments in the file to fill in the correct values
   - Ask a team member for the actual keys and URLs if needed

3. **Verify your setup:**
   ```bash
   npm run env:check
   ```

### Step 3: Start Local Services

```bash
# Start the local database
npm run supabase:start

# In another terminal, start the development server
npm run dev
```

## 🔍 Checking Your Setup

We've created helpful tools to check if everything is working:

### Check Development Environment
```bash
npm run dev:check
```
This tells you:
- ✅ What's working correctly
- ❌ What needs to be fixed
- 💡 Exactly how to fix any problems

### Check Environment Variables
```bash
npm run env:check
```
This verifies:
- All required settings are configured
- No conflicts between different configuration files
- Specific guidance for missing or incorrect values

### Check All Environments
```bash
npm run status
```
This shows you:
- Status of local development
- Status of staging environment
- Status of production environment
- Current Git branch and any uncommitted changes

## 🚀 Making Changes

### Daily Development Workflow

1. **Start your day:**
   ```bash
   npm run dev:check
   ```

2. **Make your changes** in the code

3. **Test locally:**
   ```bash
   npm run dev
   ```

4. **When ready, commit your changes:**
   ```bash
   git add .
   git commit -m "Description of what you changed"
   git push
   ```

### Deploying to Staging

When you push to the `main` branch, the frontend automatically deploys to staging. But you need to deploy database changes manually:

1. **Push your code to main branch** (triggers automatic frontend deployment)

2. **Deploy database migrations:**
   ```bash
   npm run migrate:staging
   ```
   This script will:
   - Guide you through each step
   - Show you exactly what's happening
   - Ask for confirmation before making changes
   - Tell you if anything goes wrong

3. **Test on staging:**
   Visit https://staging.fashionlab.tech and make sure everything works

### Deploying to Production

**⚠️ Production affects real users, so be extra careful!**

1. **Make sure staging is working perfectly**

2. **Create a pull request from main to production**

3. **After the PR is merged, deploy database migrations:**
   ```bash
   npm run migrate:production
   ```
   This script has extra safety checks and requires you to type "DEPLOY TO PRODUCTION" exactly.

4. **Monitor production for 30 minutes** after deployment

## 🆘 When Things Go Wrong

### "My local environment isn't working"

```bash
npm run dev:check
```
This will tell you exactly what's wrong and how to fix it.

### "Environment variables are confusing"

```bash
npm run env:check
```
This explains which variables you need and where to get them.

### "I don't know what's happening with deployments"

```bash
npm run status
```
This shows you the status of all environments and what needs attention.

### "Database migration failed"

1. **Don't panic!** The script will tell you what went wrong
2. **Read the error message carefully** - it usually explains the problem
3. **Common fixes:**
   - Check your internet connection
   - Make sure your SUPABASE_ACCESS_TOKEN is set correctly
   - Verify you're linked to the right project with `supabase status`

### "I broke something"

1. **Check if it's just your local environment:**
   ```bash
   npm run supabase:reset
   ```

2. **If staging is broken:**
   - Create a fix in a new branch
   - Test locally first
   - Deploy the fix to staging

3. **If production is broken:**
   - **Stay calm** and assess the situation
   - Create a hotfix branch from production
   - Deploy the minimum fix needed
   - Consider rolling back if necessary

## 📚 Understanding the Scripts

Here's what each script does in simple terms:

### Setup and Checking
- `npm run dev:check` - "Is my development environment ready?"
- `npm run env:check` - "Are my environment variables correct?"
- `npm run status` - "What's the status of all my environments?"
- `npm run setup` - "Run all the checks at once"

### Development
- `npm run dev` - "Start the development server"
- `npm run build` - "Build the application for deployment"
- `npm run test` - "Run tests to make sure code works"

### Database
- `npm run supabase:start` - "Start local database"
- `npm run supabase:reset` - "Reset local database to clean state"
- `npm run migrate:staging` - "Deploy database changes to staging"
- `npm run migrate:production` - "Deploy database changes to production"

### Supabase (Advanced)
- `npm run supabase:migration:new` - "Create a new database migration"
- `npm run supabase:status` - "Show status of local Supabase"

## 🌍 Environment Cheat Sheet

| Environment | Purpose | URL | When to Use |
|-------------|---------|-----|-------------|
| **Development** | Your local computer | http://localhost:8080 | Daily coding and testing |
| **Staging** | Safe testing environment | https://staging.fashionlab.tech | Testing before production |
| **Production** | Live user-facing site | https://app.fashionlab.tech | Only for final releases |

## 🔑 Environment Variables Cheat Sheet

| Variable | What It Does | Where to Get It |
|----------|--------------|-----------------|
| `VITE_ENVIRONMENT` | Tells app which environment | Set to: development, staging, or production |
| `VITE_SUPABASE_URL` | Database connection URL | Supabase project dashboard > Settings > API |
| `VITE_SUPABASE_ANON_KEY` | Database public key | Supabase project dashboard > Settings > API |
| `SUPABASE_ACCESS_TOKEN` | Personal access for CLI | https://app.supabase.com/account/tokens |

## 💡 Tips for Success

### Before You Start Working
1. Run `npm run dev:check` to make sure everything is set up
2. Check your current Git branch with `git branch`
3. Make sure you're on the right branch for your work

### During Development
1. Save your work frequently
2. Test changes locally before pushing
3. Use descriptive commit messages
4. Ask questions if something is unclear

### Before Deploying
1. Test everything thoroughly on staging
2. Make sure all tests pass
3. Review your changes one more time
4. Have a rollback plan if something goes wrong

### Daily Habits
1. Start with `npm run dev:check`
2. End with `git status` to see what you've changed
3. Keep your `.env.local` file backed up safely
4. Update documentation when you learn something new

## 🆘 Getting Help

1. **Check the error message first** - most errors explain what's wrong
2. **Run the diagnostic scripts:**
   - `npm run dev:check`
   - `npm run env:check`
   - `npm run status`
3. **Check the documentation:**
   - `docs/DEPLOYMENT-SIMPLE.md` - Simple deployment guide
   - `docs/DEPLOYMENT.md` - Detailed deployment guide
4. **Ask the team** - Post in Linear or Slack with:
   - What you were trying to do
   - What error you got
   - What the diagnostic scripts showed

## 🎉 You're Ready!

You now have everything you need to work effectively with FashionLab:

- ✅ Scripts that check your setup automatically
- ✅ Clear instructions for common tasks
- ✅ Safety checks for deployments
- ✅ Help when things go wrong

Remember: **When in doubt, run the checking scripts first!** They'll tell you exactly what's happening and how to fix it.

Happy coding! 🚀