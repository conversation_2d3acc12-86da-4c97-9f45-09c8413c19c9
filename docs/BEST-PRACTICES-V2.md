# Fashion Lab V2 API Components - Best Practices Guide

## Table of Contents

1. [Security Best Practices](#security-best-practices)
2. [Architecture Best Practices](#architecture-best-practices)
3. [React/TypeScript Best Practices](#reacttypescript-best-practices)
4. [Testing Strategy](#testing-strategy)
5. [Deployment Best Practices](#deployment-best-practices)

## Security Best Practices

### 1.1 Input Validation for Image Uploads

```typescript
// utils/validation/imageValidator.ts
export interface ImageValidationRules {
  maxSize: number; // in bytes
  allowedTypes: string[];
  maxDimensions: { width: number; height: number };
  minDimensions: { width: number; height: number };
}

export const DEFAULT_IMAGE_RULES: ImageValidationRules = {
  maxSize: 50 * 1024 * 1024, // 50MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  maxDimensions: { width: 10000, height: 10000 },
  minDimensions: { width: 100, height: 100 }
};

export async function validateImage(
  file: File,
  rules: ImageValidationRules = DEFAULT_IMAGE_RULES
): Promise<{ valid: boolean; errors: string[] }> {
  const errors: string[] = [];

  // File size validation
  if (file.size > rules.maxSize) {
    errors.push(`File size exceeds ${rules.maxSize / 1024 / 1024}MB limit`);
  }

  // File type validation
  if (!rules.allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }

  // Validate actual file content matches MIME type
  const buffer = await file.arrayBuffer();
  const actualType = await detectFileType(buffer);
  if (actualType !== file.type) {
    errors.push('File content does not match declared type');
  }

  // Dimension validation
  const dimensions = await getImageDimensions(file);
  if (dimensions.width > rules.maxDimensions.width ||
      dimensions.height > rules.maxDimensions.height) {
    errors.push('Image dimensions exceed maximum allowed');
  }

  return { valid: errors.length === 0, errors };
}

// Prevent malicious file names
export function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/\.{2,}/g, '.')
    .substring(0, 255);
}
```

### 1.2 Secure Storage Patterns

```typescript
// utils/storage/secureStorage.ts
export interface StoragePolicy {
  bucket: string;
  isPublic: boolean;
  allowedOperations: ('read' | 'write' | 'delete')[];
  pathPattern: RegExp;
}

export const STORAGE_POLICIES: Record<string, StoragePolicy> = {
  ORIGINALS: {
    bucket: 'media-originals',
    isPublic: false,
    allowedOperations: ['read', 'write'],
    pathPattern: /^collections\/[a-f0-9-]+\/[a-f0-9-]+\.(jpg|png|webp)$/
  },
  THUMBNAILS: {
    bucket: 'media-thumbnails',
    isPublic: true,
    allowedOperations: ['read', 'write'],
    pathPattern: /^collections\/[a-f0-9-]+\/[a-f0-9-]+\.webp$/
  },
  PROFILES: {
    bucket: 'profiles',
    isPublic: true,
    allowedOperations: ['read', 'write', 'delete'],
    pathPattern: /^(users|organizations)\/[a-f0-9-]+\/(user-avatar|org-logo)\.webp$/
  }
};

export async function secureUpload(
  file: File,
  path: string,
  policy: StoragePolicy
): Promise<{ path: string; url: string }> {
  // Validate path against policy
  if (!policy.pathPattern.test(path)) {
    throw new Error('Invalid storage path');
  }

  // Validate file
  const validation = await validateImage(file);
  if (!validation.valid) {
    throw new Error(`Invalid file: ${validation.errors.join(', ')}`);
  }

  // Upload with security headers
  const { data, error } = await supabase.storage
    .from(policy.bucket)
    .upload(path, file, {
      cacheControl: '3600',
      upsert: false, // Prevent overwriting
      contentType: file.type
    });

  if (error) throw error;

  return {
    path: data.path,
    url: policy.isPublic ? getPublicUrl(policy.bucket, path) : ''
  };
}
```

### 1.3 Authentication and Authorization Flows

```typescript
// hooks/useAuth.ts
export interface AuthContext {
  user: User | null;
  role: UserRole | null;
  permissions: Permission[];
  isLoading: boolean;
}

export function useAuth(): AuthContext {
  const { user } = useSupabase();
  const [authData, setAuthData] = useState<AuthContext>({
    user: null,
    role: null,
    permissions: [],
    isLoading: true
  });

  useEffect(() => {
    if (!user) {
      setAuthData({ user: null, role: null, permissions: [], isLoading: false });
      return;
    }

    // Fetch user role and permissions with proper error handling
    fetchUserPermissions(user.id)
      .then(({ role, permissions }) => {
        setAuthData({ user, role, permissions, isLoading: false });
      })
      .catch((error) => {
        console.error('Failed to fetch permissions:', error);
        setAuthData({ user, role: null, permissions: [], isLoading: false });
      });
  }, [user]);

  return authData;
}

// components/auth/PermissionGate.tsx
export function PermissionGate({ 
  children, 
  require,
  fallback = null 
}: {
  children: React.ReactNode;
  require: Permission | Permission[];
  fallback?: React.ReactNode;
}) {
  const { permissions } = useAuth();
  const required = Array.isArray(require) ? require : [require];
  const hasPermission = required.every(p => permissions.includes(p));

  if (!hasPermission) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
```

### 1.4 Rate Limiting and Abuse Prevention

```typescript
// middleware/rateLimiter.ts
export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  keyGenerator: (req: Request) => string;
}

export class RateLimiter {
  private requests = new Map<string, number[]>();

  constructor(private config: RateLimitConfig) {}

  async checkLimit(req: Request): Promise<{ allowed: boolean; retryAfter?: number }> {
    const key = this.config.keyGenerator(req);
    const now = Date.now();
    const windowStart = now - this.config.windowMs;

    // Get existing requests for this key
    const existingRequests = this.requests.get(key) || [];
    const recentRequests = existingRequests.filter(time => time > windowStart);

    if (recentRequests.length >= this.config.maxRequests) {
      const oldestRequest = Math.min(...recentRequests);
      const retryAfter = Math.ceil((oldestRequest + this.config.windowMs - now) / 1000);
      return { allowed: false, retryAfter };
    }

    // Add current request
    recentRequests.push(now);
    this.requests.set(key, recentRequests);

    // Cleanup old entries periodically
    if (Math.random() < 0.01) {
      this.cleanup();
    }

    return { allowed: true };
  }

  private cleanup() {
    const windowStart = Date.now() - this.config.windowMs;
    for (const [key, requests] of this.requests.entries()) {
      const recent = requests.filter(time => time > windowStart);
      if (recent.length === 0) {
        this.requests.delete(key);
      } else {
        this.requests.set(key, recent);
      }
    }
  }
}

// Usage in edge function
const uploadLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 10,
  keyGenerator: (req) => req.headers.get('x-user-id') || 'anonymous'
});
```

### 1.5 CORS and CSP Policies

```typescript
// config/security.ts
export const CORS_CONFIG = {
  allowedOrigins: [
    'https://app.fashionlab.tech',
    'https://staging.fashionlab.tech',
    process.env.NODE_ENV === 'development' ? 'http://localhost:8080' : ''
  ].filter(Boolean),
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID'],
  credentials: true,
  maxAge: 86400
};

export const CSP_POLICY = {
  'default-src': ["'self'"],
  'img-src': ["'self'", 'data:', 'https://*.supabase.co', 'https://fashionlab.notfirst.rodeo'],
  'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
  'style-src': ["'self'", "'unsafe-inline'"],
  'connect-src': ["'self'", 'https://*.supabase.co', 'wss://*.supabase.co'],
  'font-src': ["'self'"],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
  'frame-ancestors': ["'none'"],
  'upgrade-insecure-requests': []
};

export function generateCSPHeader(policy: typeof CSP_POLICY): string {
  return Object.entries(policy)
    .map(([directive, values]) => `${directive} ${values.join(' ')}`)
    .join('; ');
}
```

### 1.6 SQL Injection Prevention

```typescript
// utils/database/queryBuilder.ts
export class SafeQueryBuilder {
  private params: any[] = [];
  private query: string = '';

  select(table: string, columns: string[] = ['*']): this {
    const safeTable = this.escapeIdentifier(table);
    const safeCols = columns.map(col => this.escapeIdentifier(col));
    this.query = `SELECT ${safeCols.join(', ')} FROM ${safeTable}`;
    return this;
  }

  where(column: string, operator: string, value: any): this {
    const safeColumn = this.escapeIdentifier(column);
    const safeOperator = this.validateOperator(operator);
    this.params.push(value);
    const condition = `${safeColumn} ${safeOperator} $${this.params.length}`;
    
    if (this.query.includes('WHERE')) {
      this.query += ` AND ${condition}`;
    } else {
      this.query += ` WHERE ${condition}`;
    }
    
    return this;
  }

  private escapeIdentifier(identifier: string): string {
    // Only allow alphanumeric, underscore, and dot for schema.table
    if (!/^[a-zA-Z0-9_.]+$/.test(identifier)) {
      throw new Error('Invalid identifier');
    }
    return `"${identifier.replace(/"/g, '""')}"`;
  }

  private validateOperator(operator: string): string {
    const allowed = ['=', '!=', '<', '>', '<=', '>=', 'LIKE', 'IN', 'IS', 'IS NOT'];
    if (!allowed.includes(operator.toUpperCase())) {
      throw new Error('Invalid operator');
    }
    return operator;
  }

  build(): { text: string; values: any[] } {
    return { text: this.query, values: this.params };
  }
}

// Usage
const query = new SafeQueryBuilder()
  .select('assets', ['id', 'name', 'created_at'])
  .where('collection_id', '=', collectionId)
  .where('workflow_stage', '=', 'final')
  .build();

const { data, error } = await supabase.rpc('execute_query', query);
```

## Architecture Best Practices

### 2.1 Component Decomposition Strategy

```typescript
// Follow the Single Responsibility Principle
// Bad: Monolithic component
export function AssetManager() {
  // Handles uploads, display, filtering, sorting, etc.
  // 500+ lines of code
}

// Good: Decomposed components
// components/assets/AssetManager/index.tsx
export function AssetManager() {
  return (
    <AssetProvider>
      <AssetFilters />
      <AssetGrid />
      <AssetUploader />
    </AssetProvider>
  );
}

// components/assets/AssetManager/AssetProvider.tsx
export function AssetProvider({ children }: { children: ReactNode }) {
  const assets = useAssets();
  const operations = useAssetOperations();
  
  return (
    <AssetContext.Provider value={{ assets, operations }}>
      {children}
    </AssetContext.Provider>
  );
}

// components/assets/AssetManager/AssetGrid.tsx
export function AssetGrid() {
  const { assets } = useAssetContext();
  return (
    <div className="grid grid-cols-4 gap-4">
      {assets.map(asset => <AssetCard key={asset.id} asset={asset} />)}
    </div>
  );
}
```

### 2.2 Clear Separation of Concerns

```typescript
// services/api/assets.ts - Data access layer
export class AssetService {
  async getAssets(filters: AssetFilters): Promise<Asset[]> {
    const query = this.buildQuery(filters);
    const { data, error } = await supabase
      .from('assets')
      .select('*')
      .match(query);
    
    if (error) throw new ApiError(error);
    return data;
  }

  async uploadAsset(file: File, metadata: AssetMetadata): Promise<Asset> {
    // Upload logic
  }
}

// hooks/useAssets.ts - Business logic layer
export function useAssets(filters: AssetFilters) {
  const assetService = useAssetService();
  
  return useQuery({
    queryKey: ['assets', filters],
    queryFn: () => assetService.getAssets(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// components/AssetList.tsx - Presentation layer
export function AssetList({ filters }: { filters: AssetFilters }) {
  const { data: assets, isLoading, error } = useAssets(filters);
  
  if (isLoading) return <AssetListSkeleton />;
  if (error) return <ErrorBoundary error={error} />;
  
  return <AssetGrid assets={assets} />;
}
```

### 2.3 Proper Abstraction Layers

```typescript
// abstractions/storage.ts
export interface StorageProvider {
  upload(path: string, file: File): Promise<{ path: string; url: string }>;
  download(path: string): Promise<Blob>;
  delete(path: string): Promise<void>;
  getUrl(path: string): string;
}

// implementations/supabaseStorage.ts
export class SupabaseStorage implements StorageProvider {
  constructor(private bucket: string) {}

  async upload(path: string, file: File) {
    const { data, error } = await supabase.storage
      .from(this.bucket)
      .upload(path, file);
    
    if (error) throw error;
    return {
      path: data.path,
      url: this.getUrl(data.path)
    };
  }

  // Other methods...
}

// Usage with dependency injection
export function useStorage(bucket: string): StorageProvider {
  return useMemo(() => new SupabaseStorage(bucket), [bucket]);
}
```

### 2.4 Dependency Injection Patterns

```typescript
// contexts/ServicesContext.tsx
interface Services {
  auth: AuthService;
  storage: StorageService;
  api: ApiService;
  analytics: AnalyticsService;
}

const ServicesContext = createContext<Services | null>(null);

export function ServicesProvider({ children }: { children: ReactNode }) {
  const services = useMemo(() => ({
    auth: new AuthService(supabase),
    storage: new StorageService(supabase),
    api: new ApiService(supabase),
    analytics: new AnalyticsService(config.analytics)
  }), []);

  return (
    <ServicesContext.Provider value={services}>
      {children}
    </ServicesContext.Provider>
  );
}

export function useServices() {
  const services = useContext(ServicesContext);
  if (!services) {
    throw new Error('useServices must be used within ServicesProvider');
  }
  return services;
}
```

### 2.5 Configuration Management

```typescript
// config/index.ts
export interface AppConfig {
  api: {
    baseUrl: string;
    timeout: number;
    retries: number;
  };
  storage: {
    maxFileSize: number;
    allowedTypes: string[];
  };
  features: {
    aiGeneration: boolean;
    bulkUpload: boolean;
    advancedFilters: boolean;
  };
}

const configs: Record<string, AppConfig> = {
  development: {
    api: {
      baseUrl: 'http://localhost:54321',
      timeout: 30000,
      retries: 3
    },
    storage: {
      maxFileSize: 100 * 1024 * 1024, // 100MB
      allowedTypes: ['image/jpeg', 'image/png', 'image/webp']
    },
    features: {
      aiGeneration: true,
      bulkUpload: true,
      advancedFilters: true
    }
  },
  production: {
    // Production config
  }
};

export const config = configs[import.meta.env.VITE_ENVIRONMENT || 'development'];

// Feature flags hook
export function useFeature(feature: keyof AppConfig['features']): boolean {
  return config.features[feature] ?? false;
}
```

## React/TypeScript Best Practices

### 3.1 Proper Typing for Components

```typescript
// types/components.ts
export interface AssetCardProps {
  asset: Asset;
  selected?: boolean;
  onSelect?: (assetId: string) => void;
  onAction?: (action: AssetAction, assetId: string) => void;
  variant?: 'grid' | 'list';
  className?: string;
}

// components/AssetCard.tsx
export const AssetCard = memo<AssetCardProps>(({ 
  asset, 
  selected = false,
  onSelect,
  onAction,
  variant = 'grid',
  className
}) => {
  // Component implementation
});

// Use discriminated unions for complex props
type NotificationProps = 
  | { type: 'success'; message: string }
  | { type: 'error'; message: string; retry?: () => void }
  | { type: 'info'; message: string; action?: { label: string; onClick: () => void } };

export function Notification(props: NotificationProps) {
  switch (props.type) {
    case 'error':
      return <ErrorNotification message={props.message} retry={props.retry} />;
    case 'success':
      return <SuccessNotification message={props.message} />;
    case 'info':
      return <InfoNotification message={props.message} action={props.action} />;
  }
}
```

### 3.2 Custom Hooks for Reusable Logic

```typescript
// hooks/useDebounce.ts
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => clearTimeout(handler);
  }, [value, delay]);

  return debouncedValue;
}

// hooks/useInfiniteScroll.ts
export function useInfiniteScroll<T>({
  queryFn,
  queryKey,
  getNextPageParam
}: {
  queryFn: (params: any) => Promise<T[]>;
  queryKey: QueryKey;
  getNextPageParam: (lastPage: T[], pages: T[][]) => any;
}) {
  const observerRef = useRef<IntersectionObserver>();
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const query = useInfiniteQuery({
    queryKey,
    queryFn,
    getNextPageParam,
    staleTime: 5 * 60 * 1000
  });

  useEffect(() => {
    if (!loadMoreRef.current) return;

    observerRef.current = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && query.hasNextPage && !query.isFetchingNextPage) {
          query.fetchNextPage();
        }
      },
      { threshold: 0.1 }
    );

    observerRef.current.observe(loadMoreRef.current);

    return () => observerRef.current?.disconnect();
  }, [query.hasNextPage, query.isFetchingNextPage, query.fetchNextPage]);

  return {
    ...query,
    loadMoreRef
  };
}
```

### 3.3 Error Boundaries

```typescript
// components/ErrorBoundary.tsx
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends Component<
  { children: ReactNode; fallback?: ComponentType<{ error: Error; reset: () => void }> },
  ErrorBoundaryState
> {
  state: ErrorBoundaryState = { hasError: false, error: null };

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    
    // Log to monitoring service
    if (import.meta.env.PROD) {
      logErrorToService(error, errorInfo);
    }
  }

  reset = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const Fallback = this.props.fallback || DefaultErrorFallback;
      return <Fallback error={this.state.error} reset={this.reset} />;
    }

    return this.props.children;
  }
}

// Usage
export function App() {
  return (
    <ErrorBoundary fallback={AppErrorFallback}>
      <Router>
        <Routes>
          {/* Your routes */}
        </Routes>
      </Router>
    </ErrorBoundary>
  );
}
```

### 3.4 Performance Optimization

```typescript
// Use React.memo with custom comparison
export const AssetGrid = memo(
  ({ assets, onSelect }: AssetGridProps) => {
    return (
      <div className="grid grid-cols-4 gap-4">
        {assets.map(asset => (
          <AssetCard key={asset.id} asset={asset} onSelect={onSelect} />
        ))}
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison logic
    return (
      prevProps.assets.length === nextProps.assets.length &&
      prevProps.assets.every((asset, index) => 
        asset.id === nextProps.assets[index].id &&
        asset.updated_at === nextProps.assets[index].updated_at
      )
    );
  }
);

// Lazy loading for code splitting
const ImageGenerator = lazy(() => import('./components/ImageGenerator'));

export function ImageGeneratorRoute() {
  return (
    <Suspense fallback={<PageLoader />}>
      <ImageGenerator />
    </Suspense>
  );
}

// Virtualization for large lists
import { FixedSizeList } from 'react-window';

export function VirtualAssetList({ assets }: { assets: Asset[] }) {
  const Row = ({ index, style }: { index: number; style: CSSProperties }) => (
    <div style={style}>
      <AssetCard asset={assets[index]} />
    </div>
  );

  return (
    <FixedSizeList
      height={600}
      itemCount={assets.length}
      itemSize={200}
      width="100%"
    >
      {Row}
    </FixedSizeList>
  );
}
```

### 3.5 Accessibility Considerations

```typescript
// components/ui/Button.tsx
interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = 'primary', size = 'md', loading, disabled, children, ...props }, ref) => {
    return (
      <button
        ref={ref}
        disabled={disabled || loading}
        aria-busy={loading}
        aria-disabled={disabled || loading}
        className={cn(
          'focus:outline-none focus:ring-2 focus:ring-offset-2',
          'transition-colors duration-200',
          buttonVariants[variant],
          buttonSizes[size],
          {
            'opacity-50 cursor-not-allowed': disabled || loading,
            'cursor-pointer': !disabled && !loading
          }
        )}
        {...props}
      >
        {loading && (
          <span className="sr-only">Loading...</span>
        )}
        {children}
      </button>
    );
  }
);

// Keyboard navigation hook
export function useKeyboardNavigation(items: any[], onSelect: (item: any) => void) {
  const [focusedIndex, setFocusedIndex] = useState(0);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setFocusedIndex(prev => Math.min(prev + 1, items.length - 1));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setFocusedIndex(prev => Math.max(prev - 1, 0));
          break;
        case 'Enter':
        case ' ':
          e.preventDefault();
          if (items[focusedIndex]) {
            onSelect(items[focusedIndex]);
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [items, focusedIndex, onSelect]);

  return { focusedIndex };
}
```

## Testing Strategy

### 4.1 Unit Tests for Components

```typescript
// __tests__/components/AssetCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { AssetCard } from '../AssetCard';
import { mockAsset } from '../__mocks__/assets';

describe('AssetCard', () => {
  it('renders asset information correctly', () => {
    render(<AssetCard asset={mockAsset} />);
    
    expect(screen.getByText(mockAsset.name)).toBeInTheDocument();
    expect(screen.getByRole('img')).toHaveAttribute('src', mockAsset.thumbnail_url);
  });

  it('handles selection when clicked', () => {
    const onSelect = jest.fn();
    render(<AssetCard asset={mockAsset} onSelect={onSelect} />);
    
    fireEvent.click(screen.getByRole('article'));
    expect(onSelect).toHaveBeenCalledWith(mockAsset.id);
  });

  it('shows selected state correctly', () => {
    const { rerender } = render(<AssetCard asset={mockAsset} selected={false} />);
    expect(screen.getByRole('article')).not.toHaveClass('ring-2');
    
    rerender(<AssetCard asset={mockAsset} selected={true} />);
    expect(screen.getByRole('article')).toHaveClass('ring-2');
  });
});
```

### 4.2 Integration Tests for API Flows

```typescript
// __tests__/api/assetUpload.test.ts
import { createMockSupabaseClient } from '../utils/mockSupabase';
import { AssetService } from '../../services/AssetService';

describe('Asset Upload Flow', () => {
  let assetService: AssetService;
  let mockSupabase: ReturnType<typeof createMockSupabaseClient>;

  beforeEach(() => {
    mockSupabase = createMockSupabaseClient();
    assetService = new AssetService(mockSupabase);
  });

  it('uploads asset with proper validation and storage', async () => {
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const metadata = { collectionId: 'test-collection', name: 'Test Asset' };

    // Mock storage upload
    mockSupabase.storage.from.mockReturnValue({
      upload: jest.fn().mockResolvedValue({ 
        data: { path: 'test-path' }, 
        error: null 
      })
    });

    // Mock database insert
    mockSupabase.from.mockReturnValue({
      insert: jest.fn().mockReturnValue({
        select: jest.fn().mockResolvedValue({
          data: [{ id: 'test-id', ...metadata }],
          error: null
        })
      })
    });

    const result = await assetService.uploadAsset(file, metadata);

    expect(result).toHaveProperty('id', 'test-id');
    expect(mockSupabase.storage.from).toHaveBeenCalledWith('media-originals');
  });

  it('handles upload failure gracefully', async () => {
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const metadata = { collectionId: 'test-collection', name: 'Test Asset' };

    mockSupabase.storage.from.mockReturnValue({
      upload: jest.fn().mockResolvedValue({ 
        data: null, 
        error: new Error('Upload failed') 
      })
    });

    await expect(assetService.uploadAsset(file, metadata))
      .rejects.toThrow('Upload failed');
  });
});
```

### 4.3 E2E Tests for Critical User Journeys

```typescript
// e2e/assetManagement.spec.ts
import { test, expect } from '@playwright/test';
import { loginAs } from './helpers/auth';
import { uploadFile } from './helpers/upload';

test.describe('Asset Management', () => {
  test.beforeEach(async ({ page }) => {
    await loginAs(page, '<EMAIL>', 'password');
    await page.goto('/collections/test-collection');
  });

  test('upload, view, and delete asset', async ({ page }) => {
    // Upload asset
    await page.click('[data-testid="upload-button"]');
    await uploadFile(page, 'test-image.jpg');
    await expect(page.locator('[data-testid="upload-success"]')).toBeVisible();

    // View asset details
    await page.click('[data-testid="asset-card"]:first-child');
    await expect(page.locator('[data-testid="asset-detail-modal"]')).toBeVisible();
    await expect(page.locator('h2')).toContainText('test-image.jpg');

    // Delete asset
    await page.click('[data-testid="asset-actions-menu"]');
    await page.click('[data-testid="delete-asset"]');
    await page.click('[data-testid="confirm-delete"]');
    
    await expect(page.locator('[data-testid="delete-success"]')).toBeVisible();
    await expect(page.locator('[data-testid="asset-card"]')).toHaveCount(0);
  });

  test('bulk operations', async ({ page }) => {
    // Select multiple assets
    await page.click('[data-testid="select-all-checkbox"]');
    await expect(page.locator('[data-testid="bulk-actions-bar"]')).toBeVisible();

    // Perform bulk download
    await page.click('[data-testid="bulk-download"]');
    const download = await page.waitForEvent('download');
    expect(download.suggestedFilename()).toContain('assets-');
  });
});
```

### 4.4 Mock Strategies

```typescript
// __mocks__/supabase.ts
export const createMockSupabaseClient = () => {
  return {
    auth: {
      getUser: jest.fn(),
      signIn: jest.fn(),
      signOut: jest.fn(),
      onAuthStateChange: jest.fn()
    },
    from: jest.fn((table: string) => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      single: jest.fn()
    })),
    storage: {
      from: jest.fn((bucket: string) => ({
        upload: jest.fn(),
        download: jest.fn(),
        remove: jest.fn(),
        getPublicUrl: jest.fn()
      }))
    },
    rpc: jest.fn()
  };
};

// __mocks__/externalApi.ts
export class MockFashionLabAPI {
  generateImage = jest.fn().mockResolvedValue({
    id: 'mock-id',
    url: 'https://mock-url.com/image.jpg',
    status: 'completed'
  });

  getQueueStatus = jest.fn().mockResolvedValue({
    position: 0,
    estimatedTime: 0
  });
}
```

## Deployment Best Practices

### 5.1 Environment-Specific Configurations

```typescript
// config/environments.ts
export interface Environment {
  name: 'development' | 'staging' | 'production';
  apiUrl: string;
  supabaseUrl: string;
  supabaseAnonKey: string;
  features: Record<string, boolean>;
  monitoring: {
    enabled: boolean;
    sampleRate: number;
  };
}

export const environments: Record<string, Environment> = {
  development: {
    name: 'development',
    apiUrl: 'http://localhost:54321',
    supabaseUrl: process.env.VITE_SUPABASE_URL!,
    supabaseAnonKey: process.env.VITE_SUPABASE_ANON_KEY!,
    features: {
      aiGeneration: true,
      debugMode: true,
      mockData: true
    },
    monitoring: {
      enabled: false,
      sampleRate: 1.0
    }
  },
  staging: {
    name: 'staging',
    apiUrl: 'https://api-staging.fashionlab.tech',
    supabaseUrl: process.env.VITE_SUPABASE_URL!,
    supabaseAnonKey: process.env.VITE_SUPABASE_ANON_KEY!,
    features: {
      aiGeneration: true,
      debugMode: false,
      mockData: false
    },
    monitoring: {
      enabled: true,
      sampleRate: 0.1
    }
  },
  production: {
    name: 'production',
    apiUrl: 'https://api.fashionlab.tech',
    supabaseUrl: process.env.VITE_SUPABASE_URL!,
    supabaseAnonKey: process.env.VITE_SUPABASE_ANON_KEY!,
    features: {
      aiGeneration: true,
      debugMode: false,
      mockData: false
    },
    monitoring: {
      enabled: true,
      sampleRate: 0.01
    }
  }
};

export const currentEnvironment = environments[import.meta.env.VITE_ENVIRONMENT || 'development'];
```

### 5.2 Feature Flags

```typescript
// services/featureFlags.ts
export class FeatureFlagService {
  private flags: Map<string, boolean> = new Map();
  
  constructor(private environment: Environment) {
    this.loadFlags();
  }

  private async loadFlags() {
    // Load from environment config
    Object.entries(this.environment.features).forEach(([key, value]) => {
      this.flags.set(key, value);
    });

    // Override with remote flags if available
    if (this.environment.name !== 'development') {
      try {
        const remoteFlags = await this.fetchRemoteFlags();
        Object.entries(remoteFlags).forEach(([key, value]) => {
          this.flags.set(key, value as boolean);
        });
      } catch (error) {
        console.error('Failed to load remote feature flags:', error);
      }
    }
  }

  isEnabled(flag: string): boolean {
    return this.flags.get(flag) ?? false;
  }

  async toggle(flag: string, enabled: boolean) {
    this.flags.set(flag, enabled);
    
    // Persist to remote if in production
    if (this.environment.name === 'production') {
      await this.updateRemoteFlag(flag, enabled);
    }
  }

  private async fetchRemoteFlags(): Promise<Record<string, boolean>> {
    // Implement remote flag fetching
    return {};
  }

  private async updateRemoteFlag(flag: string, enabled: boolean): Promise<void> {
    // Implement remote flag updating
  }
}

// React hook
export function useFeatureFlag(flag: string): boolean {
  const [enabled, setEnabled] = useState(false);
  const { featureFlags } = useServices();

  useEffect(() => {
    setEnabled(featureFlags.isEnabled(flag));
    
    // Listen for flag changes
    const unsubscribe = featureFlags.subscribe(flag, setEnabled);
    return unsubscribe;
  }, [flag, featureFlags]);

  return enabled;
}
```

### 5.3 Monitoring and Logging

```typescript
// services/monitoring.ts
export interface LogLevel {
  DEBUG: 'debug';
  INFO: 'info';
  WARN: 'warn';
  ERROR: 'error';
}

export class MonitoringService {
  constructor(
    private config: Environment['monitoring'],
    private userId?: string
  ) {}

  log(level: keyof LogLevel, message: string, data?: any) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      userId: this.userId,
      environment: currentEnvironment.name,
      sessionId: this.getSessionId()
    };

    // Always log to console in development
    if (currentEnvironment.name === 'development') {
      console[level.toLowerCase()](message, data);
    }

    // Send to monitoring service in production
    if (this.config.enabled && this.shouldSample()) {
      this.sendToMonitoring(logEntry);
    }
  }

  captureError(error: Error, context?: any) {
    this.log('ERROR', error.message, {
      stack: error.stack,
      context,
      ...this.getErrorMetadata()
    });

    // Additional error reporting
    if (window.Sentry && this.config.enabled) {
      window.Sentry.captureException(error, {
        contexts: { custom: context }
      });
    }
  }

  trackEvent(event: string, properties?: any) {
    if (!this.config.enabled) return;

    const eventData = {
      event,
      properties,
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.getSessionId()
    };

    this.sendToAnalytics(eventData);
  }

  private shouldSample(): boolean {
    return Math.random() < this.config.sampleRate;
  }

  private getSessionId(): string {
    // Implement session ID logic
    return 'session-id';
  }

  private getErrorMetadata() {
    return {
      url: window.location.href,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    };
  }

  private async sendToMonitoring(data: any) {
    // Implement monitoring service integration
  }

  private async sendToAnalytics(data: any) {
    // Implement analytics service integration
  }
}

// React error logging hook
export function useErrorHandler() {
  const { monitoring } = useServices();

  return useCallback((error: Error, errorInfo?: ErrorInfo) => {
    monitoring.captureError(error, errorInfo);
  }, [monitoring]);
}
```

### 5.4 Rollback Procedures

```typescript
// deployment/rollback.ts
export interface DeploymentConfig {
  version: string;
  timestamp: string;
  commitHash: string;
  features: Record<string, boolean>;
}

export class RollbackManager {
  private deploymentHistory: DeploymentConfig[] = [];

  async getCurrentDeployment(): Promise<DeploymentConfig> {
    // Fetch current deployment info from API
    return {
      version: process.env.VITE_APP_VERSION || 'unknown',
      timestamp: new Date().toISOString(),
      commitHash: process.env.VITE_COMMIT_HASH || 'unknown',
      features: currentEnvironment.features
    };
  }

  async rollback(toVersion: string) {
    const targetDeployment = this.deploymentHistory.find(
      d => d.version === toVersion
    );

    if (!targetDeployment) {
      throw new Error(`Version ${toVersion} not found in history`);
    }

    // 1. Switch feature flags to previous state
    await this.revertFeatureFlags(targetDeployment.features);

    // 2. Update CDN to serve previous version
    await this.updateCDN(targetDeployment.version);

    // 3. Clear caches
    await this.clearCaches();

    // 4. Verify rollback
    await this.verifyRollback(targetDeployment);
  }

  private async revertFeatureFlags(features: Record<string, boolean>) {
    // Implement feature flag reversion
  }

  private async updateCDN(version: string) {
    // Implement CDN update logic
  }

  private async clearCaches() {
    // Clear service worker caches
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
    }

    // Clear local storage items that might cause issues
    const keysToPreserve = ['auth-token', 'user-preferences'];
    Object.keys(localStorage).forEach(key => {
      if (!keysToPreserve.includes(key)) {
        localStorage.removeItem(key);
      }
    });
  }

  private async verifyRollback(deployment: DeploymentConfig) {
    // Implement rollback verification
  }
}

// Health check endpoint
export async function healthCheck(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  version: string;
  services: Record<string, boolean>;
}> {
  const checks = await Promise.allSettled([
    checkDatabase(),
    checkStorage(),
    checkAuth(),
    checkExternalAPI()
  ]);

  const services = {
    database: checks[0].status === 'fulfilled' && checks[0].value,
    storage: checks[1].status === 'fulfilled' && checks[1].value,
    auth: checks[2].status === 'fulfilled' && checks[2].value,
    externalAPI: checks[3].status === 'fulfilled' && checks[3].value
  };

  const healthyCount = Object.values(services).filter(Boolean).length;
  const status = healthyCount === 4 ? 'healthy' : 
                 healthyCount >= 2 ? 'degraded' : 'unhealthy';

  return {
    status,
    version: process.env.VITE_APP_VERSION || 'unknown',
    services
  };
}
```

## Summary

This comprehensive guide provides a solid foundation for building Fashion Lab V2 API components with:

1. **Security-first approach** with proper input validation, secure storage, and protection against common vulnerabilities
2. **Clean architecture** following SOLID principles with clear separation of concerns
3. **Type-safe React/TypeScript** patterns with performance optimizations
4. **Comprehensive testing** strategy covering unit, integration, and E2E tests
5. **Production-ready deployment** practices with monitoring, feature flags, and rollback procedures

Each section includes practical, implementable code examples that can be adapted to your specific needs while maintaining consistency and best practices throughout the application.