# Size Filtering Implementation

## Overview

This document describes the implementation of the standardized size filtering system across the FashionLab platform. The system uses a fixed set of standard sizes (XS, S, M, L, XL) for all assets.

## Features Implemented

### 1. Standardized Size System
- Fixed size options: **XS, S, M, L, XL**
- Sizes stored in asset metadata field: `metadata.size`
- Consistent across all platform features

### 2. Bulk Size Management
- **Component**: `BulkSizeManager`
- Accessible via "Size" button in floating action bar
- Shows current size distribution as percentages
- Allows bulk assignment of sizes to multiple assets

### 3. Filter Sidebar Integration
- Size filter section with fixed options
- Visual size selection buttons
- Select All/Clear All functionality
- Shows count of selected sizes
- **Updates workflow stage counts based on size filter**

### 4. Asset Compare View
- Size filter in left sidebar
- Visual size badges on asset cards
- Multi-size selection support
- Filters assets across all workflow stages

### 5. Collection View Integration
- Size filtering via FilterSidebar
- Integrates with existing filter system
- Works alongside product, tag, and time filters

## Technical Implementation

### Database
- All assets have `metadata.size` field populated
- Migration created: `20250113_add_asset_sizes.sql`
- SQL script for bulk updates: `scripts/update-asset-sizes.sql`

### Components Updated
1. **BulkSizeManager** - Uses fixed sizes instead of dynamic
2. **FilterSidebar** - Shows fixed size options
3. **AssetCompareView** - Uses fixed sizes for filtering
4. **FloatingActionBar** - Added Size button
5. **useAssetCounts** - Supports size filtering for counts
6. **useAssetFiltering** - Filters assets by size metadata

### Type Definitions
- Fixed Asset type export issue in `assetTypes.ts`
- Proper typing for metadata fields

## Usage

### Assigning Sizes
1. Select assets in collection view
2. Click "Size" in floating action bar
3. Choose size from standard options
4. Click "Assign Size"

### Filtering by Size
1. Open filter sidebar
2. Navigate to "Filter by Size" section
3. Select one or more sizes
4. Assets and workflow counts update automatically

### In Compare View
1. Use size filter in left sidebar
2. View size badges on assets
3. Compare only specific sizes across stages

## Database Query Examples

```sql
-- Get size distribution
SELECT 
  metadata->>'size' as size, 
  COUNT(*) as count 
FROM assets 
WHERE metadata->>'size' IS NOT NULL 
GROUP BY metadata->>'size';

-- Update asset sizes
UPDATE assets 
SET metadata = jsonb_set(
  COALESCE(metadata, '{}'::jsonb),
  '{size}',
  '"M"'::jsonb
)
WHERE id = 'asset-id';
```

## Future Enhancements
- Size validation during upload
- Size-based analytics
- Size recommendations based on product type
- Bulk size import from CSV