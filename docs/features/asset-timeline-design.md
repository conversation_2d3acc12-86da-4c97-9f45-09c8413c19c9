# Asset Timeline Feature - Visual Design & Data Flow

## Overview
The Asset Timeline feature tracks the evolution of fashion assets through workflow stages, maintaining relationships between source materials and their AI-generated derivatives.

## Visual Design Concepts

### 1. Timeline View - Main Interface

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Collection: Summer 2024 > Product: T-Shirt Basic > Size: M > View: Front    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐ │
│  │ UPLOAD  │───▶│   RAW   │───▶│SELECTED │───▶│ REFINED │───▶│  FINAL  │ │
│  │ (Input) │    │   AI    │    │         │    │         │    │         │ │
│  └─────────┘    └─────────┘    └─────────┘    └─────────┘    └─────────┘ │
│      │               │              │              │              │        │
│      ▼               ▼              ▼              ▼              ▼        │
│  ┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐ │
│  │ [IMG1]  │    │ [IMG3]  │    │ [IMG5]  │    │ [IMG7]  │    │ [IMG9]  │ │
│  │ 3 💬    │    │ 5 💬    │    │ 2 💬    │    │ 4 💬    │    │ ✓ 1 💬  │ │
│  ├─────────┤    ├─────────┤    ├─────────┤    ├─────────┤    ├─────────┤ │
│  │ [IMG2]  │    │ [IMG4]  │    │ [IMG6]  │    │ [IMG8]  │    │         │ │
│  │ 1 💬    │    │ 2 💬    │    │ 0 💬    │    │ 1 💬    │    │         │ │
│  └─────────┘    └─────────┘    └─────────┘    └─────────┘    └─────────┘ │
│                                                                             │
│  Legend: 💬 = Comments  ✓ = Approved                                       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2. Data Flow Visualization

```
DATABASE RELATIONSHIPS
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

asset_lineage Table                     assets Table
┌─────────────────────┐                ┌──────────────────────┐
│ lineage_id: uuid-1  │◄───────────────│ id: asset-1         │
│ asset_id: asset-1   │                │ workflow_stage:      │
│ parent_id: null     │                │   "upload"          │
│ stage: "upload"     │                │ metadata: {         │
└─────────────────────┘                │   size: "M"         │
           ▲                           │ }                   │
           │                           └──────────────────────┘
           │                                      ▲
┌─────────────────────┐                          │
│ lineage_id: uuid-1  │                          │ product_id
│ asset_id: asset-3   │                          │
│ parent_id: asset-1  │                ┌──────────────────────┐
│ stage: "raw_ai"     │                │ products Table       │
└─────────────────────┘                │ id: prod-1          │
                                       │ name: "T-Shirt"      │
                                       │ sizes: ["S","M","L"] │
                                       └──────────────────────┘

asset_variant_groups Table
┌────────────────────────────┐
│ id: group-1                │
│ product_id: prod-1         │
│ size: "M"                  │
│ view_type: "front"         │
│ → Groups all M/Front       │
│   variants together        │
└────────────────────────────┘
```

### 3. Component Architecture

```typescript
// Timeline Component Structure
<AssetTimeline>
  <TimelineHeader>
    <FilterContext /> {/* Shows: Collection > Product > Size > View */}
    <TimelineControls /> {/* Zoom, View Options, Export */}
  </TimelineHeader>
  
  <TimelineStages>
    {stages.map(stage => (
      <StageColumn key={stage}>
        <StageHeader>{stage.name}</StageHeader>
        <AssetGrid>
          {stage.assets.map(asset => (
            <AssetCard>
              <AssetThumbnail />
              <CommentBadge count={asset.comments} />
              <AssetMetadata />
            </AssetCard>
          ))}
        </AssetGrid>
      </StageColumn>
    ))}
  </TimelineStages>
  
  <TimelineComments>
    {/* Aggregated comments from all assets in lineage */}
  </TimelineComments>
</AssetTimeline>
```

## Use Cases & User Flows

### Use Case 1: Following a Single Product Variant
**Scenario**: Track a Medium Front-view T-shirt from upload to final

```
User Flow:
1. Filter: Product="T-Shirt", Size="M", View="Front"
2. System queries all assets matching these criteria
3. Groups them by lineage_id
4. Displays timeline for each lineage group

Data Query:
SELECT DISTINCT l.lineage_id, 
       a.*, 
       vg.size, 
       vg.view_type,
       p.name as product_name
FROM asset_lineage l
JOIN assets a ON l.asset_id = a.id
JOIN asset_variant_groups vg ON a.variant_group_id = vg.id
LEFT JOIN products p ON vg.product_id = p.id
WHERE vg.product_id = ? 
  AND vg.size = 'M' 
  AND vg.view_type = 'front'
ORDER BY l.created_at, a.workflow_stage
```

### Use Case 2: Bulk AI Generation
**Scenario**: Upload 10 input images, AI generates 3 variations each

```
Timeline View:
┌─────────────────────────────────────────────────────┐
│ UPLOAD (10 assets)  →  RAW AI (30 assets)          │
│                                                     │
│ Each input branches into 3 AI variations:          │
│ Input-1 ──┬─→ AI-1A                                │
│           ├─→ AI-1B                                │
│           └─→ AI-1C                                │
└─────────────────────────────────────────────────────┘

Data Structure:
- 10 different lineage_ids (one per input)
- Each lineage has 4 assets (1 input + 3 AI)
- Comments on Input-1 are visible when viewing any AI-1X
```

### Use Case 3: Comment Threading
**Scenario**: Designer comments on issues that persist across stages

```
Comment Flow Visualization:
┌────────────────────────────────────────────────────┐
│ UPLOAD     →    RAW AI    →    REFINED           │
│   │              │              │                 │
│   └─ "Color     └─ "Better,    └─ "Perfect!"    │
│      too dark"      but still                     │
│                     needs work"                   │
│                                                   │
│ Thread View: Shows all 3 comments in sequence    │
└────────────────────────────────────────────────────┘
```

## Edge Cases & Solutions

### Edge Case 1: Multiple Inputs → Single Output
**Example**: Combining front and back views to generate a 360° view

```
Solution: Multi-parent lineage
┌─────────────┐     ┌─────────────┐
│ Front Input │     │ Back Input  │
└──────┬──────┘     └──────┬──────┘
       └─────────┬──────────┘
              ┌──▼──┐
              │360° │
              │View │
              └─────┘

Database:
- asset_lineage allows multiple entries with same lineage_id
- Different parent_asset_ids for multi-source generation
```

### Edge Case 2: Branching Workflows
**Example**: One selected image goes to both Upscale and Retouch paths

```
Visual Representation:
        ┌─→ UPSCALE → FINAL (Print)
SELECTED┤
        └─→ RETOUCH → FINAL (Web)

Solution: 
- Fork the lineage_id at branch point
- Maintain reference to common ancestor
```

### Edge Case 3: Retroactive Grouping
**Example**: Assets uploaded separately but should be grouped

```
UI Solution: "Link Assets" Feature
┌──────────────────────────────────┐
│ 🔗 Link Related Assets           │
│                                  │
│ Select assets to group:          │
│ ☑ IMG_1234.jpg (Front, M)       │
│ ☑ IMG_1235.jpg (Front, M)       │
│ ☑ IMG_1236.jpg (Front, M)       │
│                                  │
│ [Create Timeline Group]          │
└──────────────────────────────────┘
```

## UI Components Detail

### 1. Asset Card in Timeline
```
┌─────────────────┐
│   [Thumbnail]   │ ← Lazy loaded, 200x200
├─────────────────┤
│ 💬 5  👁 3  ⚡2  │ ← Comments, Views, Actions
├─────────────────┤
│ #2341 • 2 days  │ ← Asset ID, Age
└─────────────────┘

Hover State:
- Show full resolution preview
- Quick actions (Download, Comment, Tag)
- Metadata overlay (Size, Dimensions, Creator)
```

### 2. Stage Transition Arrows
```
→ Solid Arrow: Direct progression
⇢ Dashed Arrow: AI generation
⇉ Double Arrow: Multiple outputs
↗ Branch Arrow: Workflow split
```

### 3. Filter Breadcrumb
```
All Collections > Summer 2024 > T-Shirts > Basic Tee > Size M > Front View
                                                      ↑         ↑
                                            [Dropdown]  [Dropdown]
```

## Timeline View Modes

### 1. Compact Mode
- Show only final asset per stage
- Indicate count of alternatives
- Best for overview

### 2. Expanded Mode
- Show all assets per stage
- Full comment counts
- Best for detailed review

### 3. Comparison Mode
- Side-by-side stages
- Synchronized scrolling
- Best for quality checks

## Data Queries for Timeline

### Main Timeline Query
```sql
WITH lineage_timeline AS (
  SELECT 
    l.lineage_id,
    l.asset_id,
    l.parent_asset_id,
    a.workflow_stage,
    a.file_path,
    a.metadata,
    a.created_at,
    COUNT(c.id) as comment_count,
    u.full_name as created_by_name,
    vg.size,
    vg.view_type,
    p.name as product_name
  FROM asset_lineage l
  JOIN assets a ON l.asset_id = a.id
  LEFT JOIN comments c ON a.id = c.asset_id
  LEFT JOIN users u ON a.created_by = u.id
  LEFT JOIN asset_variant_groups vg ON a.variant_group_id = vg.id
  LEFT JOIN products p ON vg.product_id = p.id
  WHERE 
    l.lineage_id IN (
      -- Get lineages matching filters
      SELECT DISTINCT l2.lineage_id
      FROM asset_lineage l2
      JOIN assets a2 ON l2.asset_id = a2.id
      JOIN asset_variant_groups vg2 ON a2.variant_group_id = vg2.id
      WHERE vg2.product_id = $1
        AND vg2.size = $2
        AND vg2.view_type = $3
    )
  GROUP BY l.lineage_id, l.asset_id, l.parent_asset_id, 
           a.workflow_stage, a.file_path, a.metadata, 
           a.created_at, u.full_name, vg.size, 
           vg.view_type, p.name
)
SELECT * FROM lineage_timeline
ORDER BY lineage_id, workflow_stage, created_at;
```

## Performance Considerations

### 1. Lazy Loading
- Load stage columns as user scrolls horizontally
- Load asset thumbnails on viewport entry
- Paginate within stages for 50+ assets

### 2. Caching Strategy
- Cache lineage relationships in React Query
- Invalidate on new asset uploads
- Prefetch adjacent stages

### 3. Real-time Updates
- WebSocket subscription to lineage changes
- Optimistic updates for comments
- Background refresh for new assets

This design provides a comprehensive solution for tracking assets through their lifecycle while maintaining clear visual relationships and supporting complex workflows.