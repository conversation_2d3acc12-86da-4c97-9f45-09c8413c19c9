# Asset Timeline - Edge Cases & Data Model Solutions

## Edge Case Scenarios with Visual Examples

### 1. Multiple Inputs → Multiple Outputs (N:M Relationship)

**Scenario**: Client uploads 3 angle views (front, side, back) to generate a 360° rotating view, plus individual enhanced versions.

```
DATA FLOW:
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│Front Upload │     │Side Upload  │     │Back Upload  │
│ asset-001   │     │ asset-002   │     │ asset-003   │
└──────┬──────┘     └──────┬──────┘     └──────┬──────┘
       │                   │                   │
       └───────────────────┼───────────────────┘
                           │
                    ┌──────┴──────┐
                    │ AI Process  │
                    └──────┬──────┘
       ┌───────────────────┼───────────────────┐
       ▼                   ▼                   ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│360° View    │     │Front Refined│     │Back Refined │
│ asset-004   │     │ asset-005   │     │ asset-006   │
└─────────────┘     └─────────────┘     └─────────────┘

DATABASE STRUCTURE:
asset_lineage:
- lineage_id: uuid-360, asset_id: asset-001, parent_id: null
- lineage_id: uuid-360, asset_id: asset-002, parent_id: null  
- lineage_id: uuid-360, asset_id: asset-003, parent_id: null
- lineage_id: uuid-360, asset_id: asset-004, parent_id: multi
- lineage_id: uuid-front, asset_id: asset-005, parent_id: asset-001
- lineage_id: uuid-back, asset_id: asset-006, parent_id: asset-003

lineage_metadata for multi-parent:
{
  "parent_assets": ["asset-001", "asset-002", "asset-003"],
  "generation_type": "composite_360",
  "ai_model": "fashion-360-v2"
}
```

### 2. Workflow Branching & Merging

**Scenario**: One asset goes through parallel processing paths that later merge.

```
VISUAL FLOW:
                    ┌─→ [UPSCALE] ─→ [4K VERSION]
                    │                      │
[SELECTED ASSET] ───┤                      ├─→ [FINAL COMPOSITE]
                    │                      │
                    └─→ [RETOUCH] ─→ [RETOUCHED]

DATA MODEL:
asset_lineage:
- lineage_id: uuid-main, asset_id: selected-001, parent_id: null
- lineage_id: uuid-main, asset_id: upscale-001, parent_id: selected-001
- lineage_id: uuid-main, asset_id: retouch-001, parent_id: selected-001
- lineage_id: uuid-main, asset_id: final-001, parent_id: multi

lineage_metadata for final-001:
{
  "parent_assets": ["upscale-001", "retouch-001"],
  "merge_type": "quality_composite",
  "merge_settings": {
    "upscale_weight": 0.6,
    "retouch_weight": 0.4
  }
}
```

### 3. Retroactive Grouping (Assets Uploaded at Different Times)

**Scenario**: User uploads assets over several days that should be grouped together.

```
PROBLEM:
Day 1: Upload front view → Creates lineage-001
Day 3: Upload back view → Creates lineage-002  
Day 5: User realizes these should be linked

SOLUTION UI:
┌────────────────────────────────────────────────┐
│         🔗 Link Related Assets                 │
├────────────────────────────────────────────────┤
│ Select assets to group into timeline:          │
│                                                │
│ ☑ White T-Shirt Front (M) - Uploaded Jan 1    │
│ ☑ White T-Shirt Back (M) - Uploaded Jan 3     │
│ ☐ White T-Shirt Front (L) - Uploaded Jan 1    │
│                                                │
│ Group Name: [White T-Shirt - Size M - Complete]│
│                                                │
│ Merge Strategy:                                │
│ ○ Create new unified timeline                  │
│ ● Keep as separate timelines with links        │
│                                                │
│ [Cancel]                    [Create Timeline]  │
└────────────────────────────────────────────────┘

DATABASE OPERATION:
-- Create linking record
INSERT INTO asset_timeline_links (
  id, 
  primary_lineage_id,
  linked_lineage_id,
  link_type,
  metadata
) VALUES (
  gen_random_uuid(),
  'lineage-001',
  'lineage-002',
  'retroactive_grouping',
  '{"reason": "same_product_variant", "linked_by": "user-123"}'
);
```

### 4. Version Conflicts & Overrides

**Scenario**: Multiple team members work on same asset lineage simultaneously.

```
CONFLICT SCENARIO:
                    [ORIGINAL v1]
                         │
            ┌────────────┴────────────┐
            ▼                         ▼
     [REFINED v2a]              [REFINED v2b]
     by Designer A              by Designer B
            │                         │
            ▼                         ▼
     [UPSCALE v3a]              [UPSCALE v3b]

RESOLUTION UI:
┌────────────────────────────────────────────────┐
│      ⚠️  Timeline Conflict Detected            │
├────────────────────────────────────────────────┤
│ Two versions exist for this timeline:          │
│                                                │
│ Version A (by Sarah):          Version B:      │
│ ┌──────────┐                  ┌──────────┐    │
│ │ [IMG-A]  │                  │ [IMG-B]  │    │
│ └──────────┘                  └──────────┘    │
│ Modified: 2hr ago             Modified: 1hr ago│
│ 3 comments                    5 comments       │
│                                                │
│ Choose resolution:                             │
│ ○ Keep Version A as primary                    │
│ ○ Keep Version B as primary                    │
│ ● Keep both as alternate versions              │
│                                                │
│ [Cancel]                           [Resolve]   │
└────────────────────────────────────────────────┘

DATABASE:
-- Mark primary version
UPDATE asset_lineage 
SET metadata = jsonb_set(
  metadata, 
  '{version_status}', 
  '"primary"'
)
WHERE lineage_id = ? AND asset_id = ?;

-- Mark alternate version
UPDATE asset_lineage 
SET metadata = jsonb_set(
  metadata, 
  '{version_status}', 
  '"alternate"'
)
WHERE lineage_id = ? AND asset_id = ?;
```

### 5. Bulk Operations Maintaining Relationships

**Scenario**: Apply same operation to 50 product variants while preserving lineage.

```
BULK OPERATION FLOW:
User selects: All "T-Shirt" + All Sizes + "Front View"
Result: 50 assets across 10 lineages

┌────────────────────────────────────────────────┐
│     📦 Bulk AI Enhancement                     │
├────────────────────────────────────────────────┤
│ Selected: 50 assets from 10 lineages          │
│                                                │
│ AI Model: [Fashion Enhance v3  ▼]             │
│ Settings: [High Quality        ▼]             │
│                                                │
│ ☑ Maintain lineage relationships               │
│ ☑ Group outputs by original lineage            │
│ ☐ Create new merged timeline                   │
│                                                │
│ This will create:                              │
│ • 50 new AI-generated assets                   │
│ • Linked to their parent assets                │
│ • Organized in 10 timeline groups              │
│                                                │
│ [Cancel]                    [Generate All]     │
└────────────────────────────────────────────────┘

BATCH PROCESSING:
-- Pseudo-code for bulk operation
FOR EACH asset IN selected_assets:
  new_asset = ai_generate(asset)
  INSERT INTO asset_lineage (
    lineage_id = asset.lineage_id,  -- Preserve lineage
    asset_id = new_asset.id,
    parent_asset_id = asset.id
  )
END FOR
```

### 6. Cross-Collection References

**Scenario**: Reuse successful asset from previous collection as reference.

```
CROSS-REFERENCE FLOW:
Collection: "Summer 2023"          Collection: "Summer 2024"
┌─────────────┐                   ┌─────────────┐
│ Final Asset │ ←─────────────────│ Reference   │
│ (Original)  │   Cross-Reference │ (New)       │
└─────────────┘                   └─────────────┘

UI IMPLEMENTATION:
┌────────────────────────────────────────────────┐
│     📎 Add Reference Asset                     │
├────────────────────────────────────────────────┤
│ Import from previous collections:              │
│                                                │
│ Search: [White T-Shirt Front    🔍]           │
│                                                │
│ Results from "Summer 2023":                    │
│ ┌──────────┐ ┌──────────┐ ┌──────────┐       │
│ │ [IMG-1]  │ │ [IMG-2]  │ │ [IMG-3]  │       │
│ │ ⭐ Final  │ │ Refined  │ │ Raw      │       │
│ └──────────┘ └──────────┘ └──────────┘       │
│                                                │
│ Reference Type:                                │
│ ○ Style reference only (read-only)             │
│ ● Create working copy (new lineage)            │
│                                                │
│ [Cancel]                    [Add Reference]    │
└────────────────────────────────────────────────┘

DATABASE:
-- For style reference
INSERT INTO asset_references (
  source_asset_id,
  target_collection_id,
  reference_type,
  metadata
) VALUES (
  'asset-from-2023',
  'collection-2024',
  'style_reference',
  '{"purpose": "color_matching", "read_only": true}'
);
```

### 7. Timeline Filtering & Display Modes

**Scenario**: User needs different views of same timeline data.

```
FILTER OPTIONS:
┌────────────────────────────────────────────────┐
│ Timeline View Options                          │
├────────────────────────────────────────────────┤
│ Display Mode:                                  │
│ ○ Full Timeline (all assets)                   │
│ ○ Primary Path Only (hide alternates)          │
│ ● Key Milestones (first/last per stage)        │
│                                                │
│ Group By:                                      │
│ ○ Workflow Stage                               │
│ ● Creation Date                                │
│ ○ Creator                                      │
│                                                │
│ Show:                                          │
│ ☑ Comments      ☑ Version Numbers              │
│ ☑ Timestamps    ☐ Technical Metadata           │
└────────────────────────────────────────────────┘

QUERY VARIATIONS:
-- Key Milestones View
SELECT DISTINCT ON (workflow_stage) *
FROM timeline_view
ORDER BY workflow_stage, created_at DESC;

-- Primary Path Only
SELECT * FROM timeline_view
WHERE metadata->>'version_status' != 'alternate';

-- Grouped by Creator
SELECT *, created_by_name
FROM timeline_view
ORDER BY created_by, workflow_stage;
```

## Implementation Considerations

### 1. Performance Optimization
```sql
-- Indexes for timeline queries
CREATE INDEX idx_lineage_lookup ON asset_lineage(lineage_id, created_at);
CREATE INDEX idx_variant_group ON assets(variant_group_id);
CREATE INDEX idx_workflow_stage ON assets(workflow_stage);
CREATE INDEX idx_metadata_size ON assets((metadata->>'size'));
```

### 2. Data Integrity Rules
```sql
-- Ensure lineage consistency
ALTER TABLE asset_lineage
ADD CONSTRAINT check_valid_parent
CHECK (
  parent_asset_id IS NULL -- Root asset
  OR 
  EXISTS (
    SELECT 1 FROM asset_lineage l2 
    WHERE l2.asset_id = parent_asset_id 
    AND l2.lineage_id = lineage_id
  )
);
```

### 3. Real-time Updates
```typescript
// Subscribe to lineage changes
const subscription = supabase
  .channel('lineage-changes')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'asset_lineage',
    filter: `lineage_id=eq.${lineageId}`
  }, handleTimelineUpdate)
  .subscribe();
```

### 4. Comment Aggregation
```sql
-- View for timeline comments
CREATE VIEW timeline_comments AS
SELECT 
  c.*,
  l.lineage_id,
  a.workflow_stage,
  u.full_name as author_name,
  u.avatar_url
FROM comments c
JOIN assets a ON c.asset_id = a.id
JOIN asset_lineage l ON a.id = l.asset_id
JOIN users u ON c.user_id = u.id
ORDER BY c.created_at;
```

This comprehensive design handles complex real-world scenarios while maintaining data integrity and providing intuitive user experiences.