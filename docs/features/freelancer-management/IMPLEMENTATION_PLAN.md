# Freelancer Management & Platform Admin Account Creation - Implementation Plan

## Overview

This document outlines the implementation plan for two related features:
1. **FAS-80**: User profile for freelancers - External contractors with brand-level admin access
2. **Platform Admin Account Creation**: Enabling platform admins to directly create user accounts

## Related Linear Issues

- **FAS-80**: User profile freelancers
  - Create a user profile with access to all Admin folders, but only on brand level
  - Platform Admins should be able to assign/remove freelancers to/from brands
  - Freelancers should not be able to delete Brands

## Current System Analysis

### User Role Architecture
- **Single role system** with roles stored in `users.role`
- **Role types**: 
  - `platform_super` - Can delete organizations
  - `platform_admin` - Can manage platform, create organizations
  - `brand_admin` - Admin within specific organization(s)
  - `brand_member` - Regular member within organization(s)
  - `external_retoucher` - Currently unused
  - `external_prompter` - Currently unused

### Key Findings
1. **Brand admins already cannot delete organizations** - Only `platform_super` can
2. **Current invitation system** is overly permissive in RLS policies
3. **No direct account creation** - All users must sign up via invitation email

## Solution Design

### Part 1: Freelancer User Profile (FAS-80)

#### Approach: Reuse `brand_admin` Role with Freelancer Flag

1. **Database Changes**:
   ```sql
   -- Add freelancer flag to users table
   ALTER TABLE public.users 
   ADD COLUMN is_freelancer BOOLEAN DEFAULT false;
   
   -- Create view for freelancer management
   CREATE VIEW freelancer_assignments AS
   SELECT 
       u.id,
       u.email,
       u.display_name,
       u.is_freelancer,
       array_agg(o.name ORDER BY o.name) as assigned_brands,
       array_agg(om.organization_id ORDER BY o.name) as organization_ids,
       COUNT(om.organization_id) as brand_count,
       u.created_at,
       u.updated_at
   FROM users u
   JOIN organization_memberships om ON u.id = om.user_id
   JOIN organizations o ON om.organization_id = o.id
   WHERE u.role = 'brand_admin' AND u.is_freelancer = true
   GROUP BY u.id, u.email, u.display_name, u.is_freelancer, u.created_at, u.updated_at;
   ```

2. **RLS Policy Updates**:
   ```sql
   -- Restrict freelancers from creating invitations
   DROP POLICY IF EXISTS "allow_insert_invitations" ON pending_invitations;
   
   CREATE POLICY "restrict_freelancer_invitations" ON pending_invitations
   FOR INSERT TO authenticated
   WITH CHECK (
       auth.is_platform_user() OR 
       (auth.is_organization_admin(organization_id) AND 
        NOT COALESCE((SELECT is_freelancer FROM users WHERE id = auth.uid()), false))
   );
   ```

3. **UI Components**:
   - New `FreelancerManagement.tsx` component for platform admins
   - Modify organization member tables to show freelancer badge
   - Add freelancer filter to user lists

### Part 2: Platform Admin Account Creation

#### Enhanced Invitation System with Direct Creation

1. **Database Changes**:
   ```sql
   -- Extend pending_invitations table
   ALTER TABLE pending_invitations 
   ADD COLUMN intended_role user_role DEFAULT 'brand_member',
   ADD COLUMN is_freelancer BOOLEAN DEFAULT false,
   ADD COLUMN direct_creation BOOLEAN DEFAULT false,
   ADD COLUMN created_user_id UUID REFERENCES auth.users(id);
   
   -- Add multiple organization support for invitations
   CREATE TABLE invitation_organizations (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       invitation_id UUID REFERENCES pending_invitations(id) ON DELETE CASCADE,
       organization_id UUID REFERENCES organizations(id),
       created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
       UNIQUE(invitation_id, organization_id)
   );
   ```

2. **Edge Function for Direct User Creation**:
   ```typescript
   // supabase/functions/create-user-direct/index.ts
   interface CreateUserRequest {
     email: string;
     firstName: string;
     lastName: string;
     role: string;
     isFreelancer?: boolean;
     organizationIds: string[];
     sendWelcomeEmail?: boolean;
   }
   ```

3. **Updated User Creation Flow**:
   ```mermaid
   graph TD
       A[Platform Admin] --> B{Create User}
       B --> C[Select Role]
       C --> D{Is Freelancer?}
       D -->|Yes| E[Select Multiple Orgs]
       D -->|No| F[Select Single Org]
       E --> G{Creation Method}
       F --> G
       G -->|Direct| H[Create Auth User]
       G -->|Invite| I[Send Invitation]
       H --> J[Set Temp Password]
       J --> K[Create Profile]
       K --> L[Send Welcome Email]
       I --> M[User Signs Up]
   ```

## Implementation Phases

### Phase 1: Database & Backend
1. Create migration for `is_freelancer` column
2. Update RLS policies for freelancer restrictions
3. Create freelancer management views
4. Fix overly permissive invitation policies
5. Create edge function for direct user creation

### Phase 2: Frontend - Freelancer Management
1. Create `FreelancerManagement` page for platform admins
2. Add freelancer badge to user displays
3. Implement multi-organization assignment UI
4. Add freelancer filtering to existing user lists

### Phase 3: Frontend - Account Creation
1. Enhance `InviteMemberForm` with role selection
2. Add direct creation vs invitation toggle
3. Implement multi-organization selector for freelancers
4. Create success/error handling for direct creation

### Phase 4: Testing & Documentation
1. E2E tests for freelancer workflows
2. E2E tests for direct account creation
3. Update user documentation
4. Security audit of new features

## Security Considerations

1. **Fix RLS Policies**: Current policies are too permissive
2. **Role Validation**: Ensure only platform admins can set roles
3. **Audit Trail**: Log all account creations and role changes
4. **Password Security**: Force password reset for directly created accounts
5. **Rate Limiting**: Implement limits on account creation

## Edge Cases to Handle

1. **Freelancer Restrictions**:
   - Cannot invite new users
   - Cannot modify organization settings
   - Cannot see users from other organizations they're not assigned to

2. **Account Creation**:
   - Email already exists scenarios
   - Failed email delivery handling
   - Bulk creation support
   - Organization deletion with active freelancers

3. **Migration Path**:
   - Converting existing brand_admins to freelancers
   - Handling existing external_* roles

## Success Metrics

1. Platform admins can create accounts without manual database access
2. Freelancers have appropriate access to assigned brands only
3. Clear distinction between internal brand_admins and external freelancers
4. Audit trail for all user management actions
5. No security vulnerabilities in new features

## Next Steps

1. Review and approve this plan
2. Create feature branch: `feature/fas-80-freelancer-management`
3. Begin Phase 1 implementation
4. Set up staging environment for testing

## Questions for Clarification

1. Should freelancers be able to see other members within their assigned organizations?
2. Do we need a freelancer contract end date or deactivation feature?
3. Should there be a limit on how many organizations a freelancer can be assigned to?
4. Do freelancers need any special branding or UI customization?
5. Should platform admins be able to bulk create multiple accounts at once?