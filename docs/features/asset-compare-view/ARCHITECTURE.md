# Asset Compare View - Technical Architecture

**Feature ID:** FAS-78, FAS-79
**Document Version:** 2.0
**Last Updated:** 2025-01-27
**Implementation Status:** Phase 1 Complete - Core UI Implemented

## 1. Architecture Overview

### 1.1 System Context ✅ IMPLEMENTED
The Asset Compare View integrates with the existing FashionLab platform architecture, leveraging:
- **Supabase**: ✅ Database, authentication, and storage integration
- **React Query**: ✅ Data fetching and caching with useCompareData hook
- **React Router**: ✅ Navigation and routing setup
- **Existing Context Providers**: ✅ User roles, organization, and filters
- **Enhanced UI Components**: ✅ Professional design system with shadcn/ui
- **State Management**: ✅ CompareContext for centralized state management

### 1.2 High-Level Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (React)                         │
├─────────────────────────────────────────────────────────────┤
│ AssetCompareView → CompareContext → React Query Hooks      │
│                                   ↓                         │
│ Component Tree → State Management → API Layer              │
└─────────────────────────────────────────────────────────────┘
                                   ↓
┌─────────────────────────────────────────────────────────────┐
│                  Supabase Backend                           │
├─────────────────────────────────────────────────────────────┤
│ PostgreSQL → RLS Policies → Storage Buckets               │
│ (Enhanced Products Table + Existing Asset/Collection Data) │
└─────────────────────────────────────────────────────────────┘
```

## 2. Database Architecture

### 2.1 Schema Changes

#### 2.1.1 Products Table Enhancement
```sql
-- Existing products table structure
CREATE TABLE public.products (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    collection_id UUID REFERENCES public.collections(id),
    name TEXT NOT NULL,
    description TEXT,
    sku TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    -- NEW: Add sizes field for compare filtering
    sizes JSONB DEFAULT '[]'::jsonb
);

-- Index for efficient size filtering
CREATE INDEX idx_products_sizes ON public.products USING GIN (sizes);
```

#### 2.1.2 Data Relationships
```
Organizations (1) ──→ (N) Collections (1) ──→ (N) Assets
                                        ↓
                                    (N) Products (optional grouping)
                                        ↓
                                   Sizes (JSONB Array)
                                        ↓
                                ["XS", "S", "M", "L", "XL"]

Assets belong directly to Collections:
- collection_id: NOT NULL (required)
- product_id: NULL allowed (optional grouping)
```

### 2.2 Query Patterns

#### 2.2.1 Products with Asset Counts
```sql
-- Get products that have assets, with workflow stage counts for filtering/selection
SELECT
    p.id as product_id,
    p.name as product_name,
    p.sku,
    p.sizes,
    COUNT(CASE WHEN a.workflow_stage = 'upload' THEN 1 END) as upload_count,
    COUNT(CASE WHEN a.workflow_stage = 'raw_ai_images' THEN 1 END) as raw_ai_count,
    COUNT(CASE WHEN a.workflow_stage = 'selected' THEN 1 END) as selected_count,
    COUNT(CASE WHEN a.workflow_stage = 'upscale' THEN 1 END) as upscale_count,
    COUNT(CASE WHEN a.workflow_stage = 'retouch' THEN 1 END) as retouch_count,
    COUNT(CASE WHEN a.workflow_stage = 'final' THEN 1 END) as final_count,
    COUNT(a.id) as total_assets
FROM products p
JOIN assets a ON a.product_id = p.id
WHERE a.collection_id = $1 AND p.collection_id = $1
GROUP BY p.id, p.name, p.sku, p.sizes
HAVING COUNT(a.id) > 0  -- Only products with assets
ORDER BY p.name;
```

#### 2.2.2 Assets for Selected Product
```sql
-- Get all assets for a specific product, grouped by workflow stage
SELECT
    a.*,
    p.name as product_name,
    p.sku as product_sku,
    p.sizes as product_sizes
FROM assets a
JOIN products p ON a.product_id = p.id
WHERE a.collection_id = $1 AND a.product_id = $2 -- Single product ID
ORDER BY a.workflow_stage, a.created_at;
```

## 3. Frontend Architecture

### 3.1 Component Structure

#### 3.1.1 Directory Organization ✅ IMPLEMENTED
```
src/components/asset-compare/
├── AssetCompareView.tsx           # ✅ Main container with enhanced UI
├── CompareContext.tsx             # ✅ State management context
├── WorkflowStageGrid.tsx          # ✅ Stage grid layout with responsive design
├── AssetStageCard.tsx             # ✅ Individual stage card with status indicators
├── CompareModal.tsx               # ✅ Full-screen comparison modal
├── ProductNavigator.tsx           # ✅ Product navigation with metadata
├── hooks/
│   ├── useCompareData.ts          # ✅ Data fetching and management
│   ├── useCompareFilters.ts       # ⏳ Filter state management (partial)
│   └── useProductNavigation.ts    # ⏳ Navigation logic (planned)
├── types/
│   └── compare.types.ts           # ✅ TypeScript definitions
└── utils/
    └── compare.utils.ts           # ✅ Utility functions
```

#### 3.1.2 Enhanced UI Architecture ✅ IMPLEMENTED
```
Enhanced AssetCompareView Structure:
├── Professional Header
│   ├── Navigation Breadcrumb
│   ├── Product Navigation Controls
│   ├── Action Buttons (Share, Export, More)
│   └── Asset Count Display
├── Enhanced Sidebar (w-72)
│   ├── Workflow Progress Section
│   ├── Status-Driven Stage Cards
│   ├── Tags Management
│   └── Metadata Display
├── Main Content Area
│   ├── Stages Header with Actions
│   ├── Dynamic Grid Layout
│   ├── Professional Stage Cards
│   └── Thumbnail Navigation
└── Collaborative Comments Section
    ├── User Avatars & Timestamps
    ├── Comment Composer
    └── Markdown Support
```

#### 3.1.3 Component Hierarchy ✅ IMPLEMENTED
```
AssetCompareView
├── CompareProvider (Context) ✅
│   ├── Enhanced Header ✅
│   │   ├── Navigation Breadcrumb ✅
│   │   ├── Product Navigation Controls ✅
│   │   ├── Action Buttons (Share/Export) ✅
│   │   └── Asset Count Display ✅
│   ├── Enhanced Sidebar ✅
│   │   ├── Workflow Progress Section ✅
│   │   ├── Status-Driven Stage Cards ✅
│   │   ├── Tags Management ✅
│   │   └── Metadata Display ✅
│   ├── Main Content Area ✅
│   │   ├── Stages Header with Actions ✅
│   │   ├── Dynamic Grid Layout ✅
│   │   ├── Professional Stage Cards ✅
│   │   │   ├── Status Icons ✅
│   │   │   ├── Asset Thumbnails ✅
│   │   │   ├── Navigation Controls ✅
│   │   │   └── Thumbnail Strips ✅
│   │   └── Enhanced Empty States ✅
│   ├── Comments Section ✅
│   │   ├── User Avatars ✅
│   │   ├── Timestamps ✅
│   │   ├── Comment Composer ✅
│   │   └── Markdown Support ✅
│   └── CompareModal (conditional) ✅
│       ├── Modal Structure ✅
│       └── Modal Actions ✅
```

### 3.2 State Management ✅ IMPLEMENTED

#### 3.2.1 Compare Context ✅ IMPLEMENTED
```typescript
interface CompareState {
  // Current selection ✅
  selectedProductId: string | null; // Required - must select a product
  selectedAssetIds: string[];

  // Filters (for product selection) ✅
  productFilters: ProductFilters;

  // UI state ✅
  isModalOpen: boolean;
  modalAssets: Asset[];

  // Data ✅
  availableProducts: ProductWithAssets[];
  selectedProductAssets: AssetsByStage;

  // Loading states ✅
  isLoadingProducts: boolean;
  isLoadingAssets: boolean;
}

interface ProductFilters {
  skuSearch: string; // ⏳ Filter products by SKU (planned)
  tagIds: string[]; // ✅ Filter products by tags
  sizes: string[]; // ⏳ Filter products by sizes (planned)
}

interface AssetsByStage {
  upload: Asset[]; // ✅ Implemented
  processing: Asset[]; // ✅ Implemented (renamed from raw_ai_images)
  review: Asset[]; // ✅ Implemented (renamed from selected)
  final: Asset[]; // ✅ Implemented
  // Note: Simplified to 4 main stages for better UX
}
```

#### 3.2.2 Enhanced State Features ✅ IMPLEMENTED
```typescript
// Additional state for enhanced UI
interface EnhancedCompareState extends CompareState {
  // Stage selection for comparison ✅
  selectedStages: string[];

  // Image navigation state ✅
  currentImageIndexes: Record<string, number>;

  // Tag filtering state ✅
  selectedTags: string[];

  // UI enhancement state ✅
  workflowProgress: number; // Progress percentage
  stageStatuses: Record<string, 'completed' | 'in_progress' | 'pending' | 'empty'>;
}
```

#### 3.2.2 Data Flow
```
User Action → Component → Context Action → React Query Hook → Supabase → UI Update
     ↓              ↓           ↓              ↓              ↓         ↓
Product Filter → CompareFilters → updateFilters → useProductsWithAssets → Database → Product List Update
Product Select → ProductNavigator → selectProduct → useProductAssets → Database → Asset Grid Update
```

### 3.3 Custom Hooks

#### 3.3.1 Data Fetching Hooks
```typescript
// Main data fetching hook for compare view
export function useCompareData(collectionId: string, selectedProductId: string | null, filters: ProductFilters) {
  const products = useProductsWithAssets(collectionId, filters);
  const assets = useProductAssets(selectedProductId, collectionId);

  return {
    availableProducts: products.data || [],
    selectedProductAssets: assets.data || {},
    isLoadingProducts: products.isLoading,
    isLoadingAssets: assets.isLoading,
    error: products.error || assets.error
  };
}

// Product navigation hook
export function useProductNavigation(products: ProductWithAssets[], currentProductId: string | null) {
  const currentIndex = products.findIndex(p => p.id === currentProductId);

  const navigateNext = useCallback(() => {
    if (currentIndex < products.length - 1) {
      return products[currentIndex + 1].id;
    }
    return null;
  }, [products, currentIndex]);

  const navigatePrev = useCallback(() => {
    if (currentIndex > 0) {
      return products[currentIndex - 1].id;
    }
    return null;
  }, [products, currentIndex]);

  const canNavigateNext = currentIndex < products.length - 1;
  const canNavigatePrev = currentIndex > 0;

  return {
    navigateNext,
    navigatePrev,
    canNavigateNext,
    canNavigatePrev,
    currentIndex,
    totalProducts: products.length
  };
}
```

#### 3.3.2 Filter Management Hook
```typescript
export function useCompareFilters(initialFilters: CompareFilters) {
  const [filters, setFilters] = useState<CompareFilters>(initialFilters);
  
  const updateFilter = useCallback(<K extends keyof CompareFilters>(
    key: K,
    value: CompareFilters[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);
  
  const clearFilters = useCallback(() => {
    setFilters(initialFilters);
  }, [initialFilters]);
  
  const hasActiveFilters = useMemo(() => {
    return filters.skuSearch !== '' ||
           filters.tagIds.length > 0 ||
           filters.sizes.length > 0 ||
           filters.workflowStages.length > 0;
  }, [filters]);
  
  return { filters, updateFilter, clearFilters, hasActiveFilters };
}
```

## 4. Performance Considerations

### 4.1 Data Loading Strategy

#### 4.1.1 Progressive Loading
```typescript
// Load products first, then assets for visible product
const { data: products } = useProductsWithAssetCounts(collectionId, filters);
const { data: assets } = useAssetsForCompare(
  currentProductId ? [currentProductId] : [],
  { enabled: !!currentProductId }
);
```

#### 4.1.2 Caching Strategy
```typescript
// React Query configuration for compare data
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

// Prefetch adjacent products
const prefetchAdjacentProducts = useCallback((productIds: string[]) => {
  productIds.forEach(id => {
    queryClient.prefetchQuery(['assets-for-compare', id], () =>
      fetchAssetsForProduct(id)
    );
  });
}, [queryClient]);
```

### 4.2 Image Loading Optimization

#### 4.2.1 Lazy Loading
```typescript
// Use intersection observer for lazy loading
const AssetThumbnail = ({ asset }: { asset: Asset }) => {
  const [isVisible, setIsVisible] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => setIsVisible(entry.isIntersecting),
      { threshold: 0.1 }
    );
    
    if (imgRef.current) observer.observe(imgRef.current);
    return () => observer.disconnect();
  }, []);
  
  return (
    <img
      ref={imgRef}
      src={isVisible ? getAssetUrl(asset, 'thumbnail') : undefined}
      loading="lazy"
      alt={asset.file_name}
    />
  );
};
```

#### 4.2.2 Progressive Image Enhancement
```typescript
// Load thumbnail first, then full resolution on demand
const useProgressiveImage = (asset: Asset) => {
  const [currentSrc, setCurrentSrc] = useState(getAssetUrl(asset, 'thumbnail'));
  const [isLoading, setIsLoading] = useState(false);
  
  const loadFullResolution = useCallback(() => {
    setIsLoading(true);
    const img = new Image();
    img.onload = () => {
      setCurrentSrc(getAssetUrl(asset, 'compressed'));
      setIsLoading(false);
    };
    img.src = getAssetUrl(asset, 'compressed');
  }, [asset]);
  
  return { currentSrc, isLoading, loadFullResolution };
};
```

## 5. Security & Access Control

### 5.1 RLS Integration
The compare view respects existing Row Level Security policies:

```sql
-- Products are filtered by organization membership
-- Assets are filtered by collection access
-- No additional RLS policies needed
```

### 5.2 Role-Based Features
```typescript
// Different features based on user role
const CompareFeatures = () => {
  const { userRole } = useUserRole();
  
  return (
    <>
      {/* All users can view and navigate */}
      <ProductNavigator />
      <WorkflowStageGrid />
      
      {/* Only admins can see all workflow stages */}
      {(userRole === 'platform_super' || userRole === 'platform_admin') && (
        <AllWorkflowStages />
      )}
      
      {/* Brand users see limited stages */}
      {(userRole === 'brand_admin' || userRole === 'brand_member') && (
        <BrandWorkflowStages />
      )}
      
      {/* External users see specific stages */}
      {userRole === 'external_retoucher' && (
        <RetoucherWorkflowStages />
      )}
    </>
  );
};
```

## 6. Testing Architecture

### 6.1 Unit Testing Strategy
```typescript
// Component testing with React Testing Library
describe('AssetCompareView', () => {
  it('renders product navigation correctly', () => {
    render(<AssetCompareView collectionId="test-id" />);
    expect(screen.getByRole('navigation')).toBeInTheDocument();
  });
  
  it('filters products by SKU search', async () => {
    const { user } = setup(<AssetCompareView collectionId="test-id" />);
    await user.type(screen.getByPlaceholderText('Search by SKU'), 'TEST-001');
    expect(mockUseCompareData).toHaveBeenCalledWith(
      'test-id',
      expect.objectContaining({ skuSearch: 'TEST-001' })
    );
  });
});
```

### 6.2 Integration Testing
```typescript
// Test data flow and API integration
describe('Compare Data Integration', () => {
  it('loads products and assets correctly', async () => {
    const { result } = renderHook(() => 
      useCompareData('collection-id', defaultFilters)
    );
    
    await waitFor(() => {
      expect(result.current.products).toHaveLength(3);
      expect(result.current.assets).toHaveProperty('product-1');
    });
  });
});
```

### 6.3 E2E Testing
```typescript
// Playwright E2E tests
test('complete compare workflow', async ({ page }) => {
  await page.goto('/organizations/org-id/collections/collection-id/compare');
  
  // Test filtering
  await page.fill('[data-testid="sku-search"]', 'TEST-001');
  await expect(page.locator('[data-testid="product-card"]')).toHaveCount(1);
  
  // Test navigation
  await page.click('[data-testid="next-product"]');
  await expect(page.locator('[data-testid="product-name"]')).toContainText('Product 2');
  
  // Test comparison modal
  await page.click('[data-testid="stage-card-upload"]');
  await page.click('[data-testid="stage-card-final"]');
  await page.click('[data-testid="compare-selected"]');
  await expect(page.locator('[data-testid="compare-modal"]')).toBeVisible();
});
```

## 7. Deployment Strategy

### 7.1 Database Migration
```sql
-- Migration will be applied in development first
-- Then staging for testing
-- Finally production with rollback plan
```

### 7.2 Feature Flag Implementation
```typescript
// Use feature flag for gradual rollout
const AssetCompareRoute = () => {
  const { isFeatureEnabled } = useFeatureFlags();
  
  if (!isFeatureEnabled('asset-compare-view')) {
    return <Navigate to="/collections/:id" replace />;
  }
  
  return <AssetCompareView />;
};
```

### 7.3 Monitoring & Analytics
```typescript
// Track usage and performance
const useCompareAnalytics = () => {
  const trackEvent = useCallback((event: string, data: any) => {
    // Send to analytics service
    analytics.track('compare_view_' + event, data);
  }, []);
  
  return { trackEvent };
};
```

## 8. Implementation Status Summary

### 8.1 ✅ Completed Features
- **Core UI Architecture**: Professional interface with enhanced visual hierarchy
- **State Management**: CompareContext with comprehensive state handling
- **Data Integration**: useCompareData hook with React Query integration
- **Workflow Progress**: Visual progress tracking with completion indicators
- **Stage Management**: Status-driven design with color-coded indicators
- **Image Navigation**: Thumbnail strips and carousel controls
- **Collaborative Features**: Comments system with user management
- **Responsive Design**: Adaptive layout for different screen sizes
- **Professional Polish**: Consistent design language and accessibility

### 8.2 ⏳ Pending Features (Next Phase)
- **Advanced Filtering**: SKU search and size-based filtering
- **Bulk Operations**: Multi-asset selection and batch actions
- **Performance Optimization**: Image lazy loading and caching
- **Keyboard Navigation**: Shortcuts for power users
- **Export Functionality**: PDF reports and comparison exports
- **Testing Suite**: Unit, integration, and E2E tests

### 8.3 🔧 Technical Debt & Improvements
- **Database Migration**: Product sizes field implementation
- **API Optimization**: Efficient query patterns for large datasets
- **Error Handling**: Comprehensive error states and recovery
- **Accessibility Audit**: WCAG 2.1 AA compliance verification
- **Performance Monitoring**: Metrics and optimization tracking

### 8.4 📋 Next Development Priorities
1. **Implement advanced filtering** (SKU search, size filtering)
2. **Add bulk operations** for multi-asset management
3. **Optimize performance** with lazy loading and caching
4. **Create comprehensive test suite**
5. **Conduct accessibility audit**
6. **Add keyboard navigation support**
7. **Implement export functionality**

---

**Document Version:** 2.0
**Last Updated:** 2025-01-27
**Implementation Status:** Phase 1 Complete - Core UI Implemented
**Next Review:** 2025-02-03
