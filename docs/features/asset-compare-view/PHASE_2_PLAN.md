# Asset Compare View - Phase 2 Implementation Plan

**Feature ID:** FAS-79 (Continuation)
**Phase:** 2 - Enhanced Features & Performance
**Created:** 2025-01-27
**Status:** Planning
**Priority:** High
**Assignee:** <PERSON><PERSON>

## 1. Phase 2 Overview

### 1.1 Current Status
✅ **Phase 1 Complete**: Core UI with professional design, workflow progress tracking, stage comparison, and collaborative commenting system implemented.

### 1.2 Phase 2 Goals
Enhance the Asset Compare View with advanced filtering, performance optimizations, and improved user experience features.

## 2. Priority 1: Enhanced Filtering & Search

### 2.1 SKU Search Implementation
**Objective**: Real-time product filtering by SKU

#### 2.1.1 Component Updates
```typescript
// Update ProductFilters.tsx
interface ProductFiltersProps {
  onFiltersChange: (filters: ProductFilters) => void;
  currentFilters: ProductFilters;
  availableProducts: ProductWithAssets[];
}

// Add SKU search input
<Input
  placeholder="Search by SKU..."
  value={currentFilters.skuSearch}
  onChange={(e) => onFiltersChange({ 
    ...currentFilters, 
    skuSearch: e.target.value 
  })}
  className="w-full"
/>
```

#### 2.1.2 Hook Enhancement
```typescript
// Update useCompareData.ts
export function useProductsWithAssets(
  collectionId: string,
  filters: ProductFilters = { skuSearch: '', tagIds: [], sizes: [] }
) {
  return useQuery<ProductWithAssets[], Error>({
    queryKey: ['products-with-assets', collectionId, filters],
    queryFn: async () => {
      let query = supabase.from('products').select(/* ... */);
      
      // Apply SKU filter
      if (filters.skuSearch) {
        query = query.ilike('sku', `%${filters.skuSearch}%`);
      }
      
      // Apply other filters...
      return processResults(query);
    }
  });
}
```

### 2.2 Size-based Filtering
**Objective**: Filter products by available sizes

#### 2.2.1 Database Query Enhancement
```sql
-- Query products with specific sizes
SELECT * FROM products 
WHERE collection_id = $1 
AND sizes @> $2::jsonb  -- Contains any of the selected sizes
```

#### 2.2.2 UI Component
```typescript
// Size filter component
<MultiSelect
  options={availableSizes}
  value={currentFilters.sizes}
  onChange={(sizes) => onFiltersChange({ 
    ...currentFilters, 
    sizes 
  })}
  placeholder="Filter by sizes..."
/>
```

### 2.3 Advanced Tag Filtering
**Objective**: Multi-tag selection with AND/OR logic

#### 2.3.1 Enhanced Tag Interface
```typescript
interface TagFilter {
  tagIds: string[];
  logic: 'AND' | 'OR'; // How to combine multiple tags
}

// Update ProductFilters interface
interface ProductFilters {
  skuSearch: string;
  tagFilter: TagFilter;
  sizes: string[];
}
```

## 3. Priority 2: Performance & UX Improvements

### 3.1 Image Lazy Loading
**Objective**: Progressive image loading for better performance

#### 3.1.1 Lazy Loading Hook
```typescript
// Create hooks/useLazyImage.ts
export function useLazyImage(src: string, threshold = 0.1) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => setIsInView(entry.isIntersecting),
      { threshold }
    );
    
    if (imgRef.current) observer.observe(imgRef.current);
    return () => observer.disconnect();
  }, [threshold]);

  useEffect(() => {
    if (isInView && !isLoaded) {
      const img = new Image();
      img.onload = () => setIsLoaded(true);
      img.src = src;
    }
  }, [isInView, isLoaded, src]);

  return { imgRef, shouldLoad: isInView, isLoaded };
}
```

#### 3.1.2 Enhanced AssetStageCard
```typescript
// Update AssetStageCard.tsx with lazy loading
const AssetThumbnail = ({ asset }: { asset: Asset }) => {
  const thumbnailUrl = getAssetUrl(asset, 'thumbnail');
  const { imgRef, shouldLoad, isLoaded } = useLazyImage(thumbnailUrl);

  return (
    <div ref={imgRef} className="relative aspect-square">
      {shouldLoad ? (
        <img
          src={thumbnailUrl}
          alt={asset.file_name}
          className={`w-full h-full object-cover transition-opacity ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
        />
      ) : (
        <Skeleton className="w-full h-full" />
      )}
    </div>
  );
};
```

### 3.2 Keyboard Navigation
**Objective**: Arrow keys for product navigation, shortcuts for actions

#### 3.2.1 Keyboard Hook
```typescript
// Create hooks/useKeyboardNavigation.ts
export function useKeyboardNavigation(
  products: ProductWithAssets[],
  selectedProductId: string | null,
  onProductChange: (productId: string) => void
) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.target instanceof HTMLInputElement) return; // Skip if typing
      
      const currentIndex = products.findIndex(p => p.id === selectedProductId);
      
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          if (currentIndex > 0) {
            onProductChange(products[currentIndex - 1].id);
          }
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (currentIndex < products.length - 1) {
            onProductChange(products[currentIndex + 1].id);
          }
          break;
        case 'Escape':
          event.preventDefault();
          // Close modals or clear selection
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [products, selectedProductId, onProductChange]);
}
```

### 3.3 Bulk Operations
**Objective**: Multi-asset selection and batch download

#### 3.3.1 Selection State Enhancement
```typescript
// Update CompareContext.tsx
interface CompareState {
  // ... existing state
  selectedAssetIds: string[];
  selectionMode: 'single' | 'multi';
  bulkActions: {
    isDownloading: boolean;
    downloadProgress: number;
  };
}

// Add bulk action creators
const toggleSelectionMode = useCallback(() => {
  dispatch({ type: 'TOGGLE_SELECTION_MODE' });
}, []);

const selectAllAssetsInStage = useCallback((stageKey: string) => {
  dispatch({ type: 'SELECT_ALL_IN_STAGE', payload: stageKey });
}, []);

const downloadSelectedAssets = useCallback(async () => {
  dispatch({ type: 'START_BULK_DOWNLOAD' });
  // Implement bulk download logic
}, []);
```

## 4. Priority 3: Advanced Features

### 4.1 Zoom & Pan
**Objective**: Detailed image inspection capabilities

#### 4.1.1 Enhanced CompareModal
```typescript
// Update CompareModal.tsx with zoom functionality
const ZoomableImage = ({ asset }: { asset: Asset }) => {
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  
  return (
    <div className="relative overflow-hidden">
      <img
        src={getAssetUrl(asset, 'compressed')}
        style={{
          transform: `scale(${zoom}) translate(${pan.x}px, ${pan.y}px)`,
          cursor: zoom > 1 ? 'grab' : 'zoom-in'
        }}
        onClick={() => setZoom(zoom === 1 ? 2 : 1)}
        // Add pan handlers
      />
      <div className="absolute bottom-4 right-4 flex gap-2">
        <Button size="sm" onClick={() => setZoom(Math.max(0.5, zoom - 0.5))}>
          -
        </Button>
        <Button size="sm" onClick={() => setZoom(Math.min(3, zoom + 0.5))}>
          +
        </Button>
      </div>
    </div>
  );
};
```

### 4.2 Export Functionality
**Objective**: PDF reports and comparison exports

#### 4.2.1 Export Hook
```typescript
// Create hooks/useExport.ts
export function useExport() {
  const exportToPDF = useCallback(async (
    product: ProductWithAssets,
    assets: AssetsByStage
  ) => {
    // Use jsPDF or similar library
    const pdf = new jsPDF();
    
    // Add product information
    pdf.text(`Product: ${product.name}`, 20, 20);
    pdf.text(`SKU: ${product.sku}`, 20, 30);
    
    // Add workflow stages and images
    // Implementation details...
    
    pdf.save(`${product.sku}-comparison.pdf`);
  }, []);

  const exportAsImages = useCallback(async (
    selectedAssets: Asset[]
  ) => {
    // Create ZIP file with selected assets
    // Implementation details...
  }, []);

  return { exportToPDF, exportAsImages };
}
```

## 5. Implementation Timeline

### 5.1 Week 1: Enhanced Filtering
- [ ] Implement SKU search functionality
- [ ] Add size-based filtering
- [ ] Enhance tag filtering with AND/OR logic
- [ ] Update ProductFilters component
- [ ] Test filtering performance

### 5.2 Week 2: Performance Improvements
- [ ] Implement image lazy loading
- [ ] Add keyboard navigation
- [ ] Create enhanced loading states
- [ ] Optimize React Query caching
- [ ] Performance testing and optimization

### 5.3 Week 3: Bulk Operations
- [ ] Implement multi-asset selection
- [ ] Add bulk download functionality
- [ ] Create selection UI components
- [ ] Add progress indicators
- [ ] Test bulk operations

### 5.4 Week 4: Advanced Features
- [ ] Implement zoom & pan in modal
- [ ] Add export functionality (PDF/ZIP)
- [ ] Enhanced asset metadata display
- [ ] Final testing and polish
- [ ] Documentation updates

## 6. Success Criteria

### 6.1 Performance Metrics
- [ ] Page load time < 2 seconds with 50+ products
- [ ] Image loading < 500ms for thumbnails
- [ ] Smooth navigation (< 100ms response time)
- [ ] Bulk operations handle 20+ assets efficiently

### 6.2 User Experience
- [ ] Intuitive filtering with real-time results
- [ ] Keyboard navigation works seamlessly
- [ ] Bulk operations are discoverable and efficient
- [ ] Export functionality produces high-quality outputs

### 6.3 Technical Quality
- [ ] No performance regressions
- [ ] Comprehensive error handling
- [ ] Accessibility compliance maintained
- [ ] Mobile responsiveness preserved

---

**Document Version:** 1.0
**Last Updated:** 2025-01-27
**Next Review:** 2025-02-03
**Implementation Status:** Planning Phase
