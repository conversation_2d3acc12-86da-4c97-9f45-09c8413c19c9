# Asset Compare View - Product Requirements Document

**Feature ID:** FAS-78, FAS-79
**Created:** 2025-01-07
**Updated:** 2025-01-27
**Status:** Phase 1 Complete - Core UI Implemented
**Priority:** Medium
**Assignee:** <PERSON><PERSON>

## 1. Overview

### 1.1 Feature Summary
The Asset Compare View is a professional interface that allows users to select a specific product and view all its workflow stages side by side. This feature enables efficient review of how a product's assets progress through the AI workflow pipeline (upload → processing → review → final) with enhanced collaboration features.

**✅ IMPLEMENTED**: Core UI with workflow progress tracking, stage comparison, and collaborative commenting system.

### 1.2 Problem Statement
Currently, users must navigate between different workflow stage filters to see how a product's assets progress through the pipeline. This makes it difficult to:
- Compare quality improvements between stages for a specific product
- Review the complete workflow progression for a product in one view
- Make informed decisions about which assets to advance to the next stage
- Efficiently review and navigate between different products' workflow status
- Collaborate effectively with team members on asset feedback

### 1.3 Success Metrics
- **User Engagement**: 70% of users with access try the compare view within first week
- **Workflow Efficiency**: 30% reduction in time spent reviewing asset progression
- **User Satisfaction**: 4.5+ rating in user feedback surveys
- **Feature Adoption**: 50% of active users use compare view monthly

## 2. User Stories & Requirements

### 2.1 Primary User Stories

**As a Brand Admin, I want to:**
- Select a specific product and see all its workflow stages side by side
- Filter the product list by SKU, tags, and sizes to find the product I want to review
- Navigate between different products while maintaining the compare view
- See all available workflow stages for the selected product in one view
- Comment on specific stages to provide feedback

**As a Platform Admin, I want to:**
- Review workflow progression for specific products across all organizations
- Identify bottlenecks in the AI workflow pipeline by examining product workflows
- Compare quality improvements between stages for individual products
- Provide feedback on specific workflow stages

**As an External Retoucher, I want to:**
- Select a product and see the progression from raw AI images to current retouch stage
- Compare my retouched versions with previous stages for that product
- Understand the quality expectations by seeing final approved assets for similar products

### 2.2 Functional Requirements

#### 2.2.1 Core Features ✅ IMPLEMENTED
- **FR-1**: ✅ Require product selection - user must choose a specific product to view
- **FR-2**: ✅ Display all workflow stages for the selected product horizontally
- **FR-3**: ✅ Filter the product list by SKU, tags, and sizes to find desired product
- **FR-4**: ✅ Navigate between products using arrow controls while maintaining compare view
- **FR-5**: ✅ Show asset thumbnails and counts for each workflow stage of the selected product
- **FR-6**: ✅ Click to view full-size assets with image navigation controls
- **FR-7**: ✅ Display selected product information (name, SKU, sizes)
- **FR-8**: ✅ Show workflow stage labels and status indicators with progress tracking

#### 2.2.2 Advanced Features
- **FR-9**: ✅ Comment and annotation functionality on individual stages
- **FR-10**: ⏳ Bulk operations (select multiple assets from current product for download)
- **FR-11**: ⏳ Zoom and pan functionality for detailed comparison
- **FR-12**: ⏳ Side-by-side full-screen comparison mode
- **FR-13**: ⏳ Export comparison views as PDF reports
- **FR-14**: ⏳ Keyboard shortcuts for product navigation

#### 2.2.3 Enhanced UI Features ✅ IMPLEMENTED
- **FR-15**: ✅ Workflow progress indicator with completion percentage
- **FR-16**: ✅ Professional stage cards with status-driven design
- **FR-17**: ✅ Thumbnail navigation strips for multi-asset stages
- **FR-18**: ✅ Enhanced empty states with actionable CTAs
- **FR-19**: ✅ Collaborative comments system with user avatars and timestamps
- **FR-20**: ✅ Share and export functionality in header
- **FR-21**: ✅ Metadata display (creation date, modification, assignee)
- **FR-22**: ✅ Console toggle for additional functionality

### 2.3 Non-Functional Requirements

#### 2.3.1 Performance
- **NFR-1**: Page load time < 2 seconds for up to 50 products
- **NFR-2**: Smooth navigation between products (< 500ms transition)
- **NFR-3**: Efficient image loading with progressive enhancement
- **NFR-4**: Responsive design for desktop and tablet (mobile optional)

#### 2.3.2 Security & Access Control
- **NFR-5**: Respect existing RLS policies for multi-tenant isolation
- **NFR-6**: Role-based access to workflow stages
- **NFR-7**: Audit logging for compare view usage

## 3. Data Model & Scope

### 3.1 Asset-Collection Relationship
**Important**: Assets belong directly to Collections, NOT to Products:
- `assets.collection_id` is NOT NULL (required)
- `assets.product_id` is NULL allowed (optional)
- Products are optional grouping mechanisms that sit alongside assets
- When a product is deleted, assets remain (they just lose their product association)

### 3.2 Phase 1 Scope: Product-Required Compare View
- **Product selection is REQUIRED** - users must choose a specific product
- **Only show assets that have a product_id** (grouped assets)
- **Filter products** by SKU, tags, and sizes to help users find the right product
- **Navigate between products** while maintaining the compare view
- **Future enhancement**: Support for ungrouped assets and multi-product comparison

### 3.3 Compare View Logic
- User filters the product list to find desired product
- User selects a specific product (required step)
- View shows all workflow stages for that product's assets
- Navigation allows moving between different products
- All filtering is applied to the product selection, not individual assets

## 4. Technical Specifications

### 4.1 Database Changes

#### 4.1.1 Products Table Enhancement
```sql
-- Add sizes field to products table
ALTER TABLE public.products 
ADD COLUMN IF NOT EXISTS sizes JSONB DEFAULT '[]'::jsonb;

-- Add index for efficient querying
CREATE INDEX IF NOT EXISTS idx_products_sizes ON public.products USING GIN (sizes);
```

#### 4.1.2 New Tables (Optional)
```sql
-- Track compare view usage for analytics
CREATE TABLE IF NOT EXISTS public.compare_view_sessions (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id),
    collection_id UUID REFERENCES public.collections(id),
    products_viewed JSONB, -- Array of product IDs viewed
    session_duration INTEGER, -- Duration in seconds
    created_at TIMESTAMPTZ DEFAULT now()
);
```

### 4.2 API Endpoints

#### 4.2.1 New Hooks
```typescript
// Get products with asset counts by workflow stage (only products with assets)
useProductsWithAssets(collectionId: string, filters: ProductFilters)

// Get all assets for a specific product, grouped by workflow stage
useProductAssets(productId: string, collectionId: string)

// Get product sizes for filtering (from products that have associated assets)
useProductSizes(collectionId: string)

// Combined hook for compare view data
useCompareViewData(collectionId: string, selectedProductId: string | null, filters: ProductFilters)
```

#### 4.2.2 Enhanced Existing Hooks
```typescript
// Extend useProducts to include sizes and asset counts
interface ProductWithAssets {
  id: string;
  name: string;
  sku: string;
  sizes: string[]; // New field
  collection_id: string;
  asset_counts: {
    upload: number;
    raw_ai_images: number;
    selected: number;
    upscale: number;
    retouch: number;
    final: number;
  };
  // ... existing fields
}
```

### 4.3 Component Architecture

#### 4.3.1 Main Components ✅ IMPLEMENTED
```
src/components/asset-compare/
├── AssetCompareView.tsx           # ✅ Main container component with enhanced UI
├── CompareContext.tsx             # ✅ State management context
├── WorkflowStageGrid.tsx          # ✅ Grid showing stages horizontally
├── AssetStageCard.tsx             # ✅ Individual stage card with thumbnail
├── CompareModal.tsx               # ✅ Full-screen comparison modal
├── ProductNavigator.tsx           # ✅ Product selection and navigation
├── hooks/
│   ├── useCompareData.ts          # ✅ Data fetching and management
│   └── useCompareFilters.ts       # ⏳ Filter state management
├── types/
│   └── compare.types.ts           # ✅ TypeScript definitions
└── utils/
    └── compare.utils.ts           # ✅ Utility functions
```

#### 4.3.2 Enhanced UI Components ✅ IMPLEMENTED
- **Workflow Progress Section**: Visual progress indicator with completion percentage
- **Enhanced Stage Cards**: Status-driven design with color-coded indicators
- **Professional Header**: Integrated navigation and action buttons
- **Sidebar Layout**: Organized workflow stages, tags, and metadata
- **Comments System**: Collaborative annotation with user management
- **Responsive Grid**: Dynamic layout adapting to selected stages

#### 3.3.2 Component Hierarchy
```
AssetCompareView
├── CompareFilters
├── ProductNavigator
├── ProductInfo
├── WorkflowStageGrid
│   └── AssetStageCard (multiple)
└── CompareModal (conditional)
```

### 3.4 State Management

#### 3.4.1 Compare Context
```typescript
interface CompareContextType {
  // Current state
  currentProductId: string | null;
  selectedStages: string[];
  filters: CompareFilters;
  
  // Actions
  setCurrentProduct: (productId: string) => void;
  toggleStageSelection: (stageId: string) => void;
  updateFilters: (filters: Partial<CompareFilters>) => void;
  navigateToProduct: (direction: 'next' | 'prev') => void;
}

interface CompareFilters {
  skuSearch: string;
  tagIds: string[];
  sizes: string[];
  workflowStages: WorkflowStage[];
}
```

## 4. User Interface Design

### 4.1 Layout Structure

#### 4.1.1 Main Layout
```
┌─────────────────────────────────────────────────────────────┐
│ Header: Collection Name > Compare View                      │
├─────────────────────────────────────────────────────────────┤
│ Product Filters: [SKU Search] [Tags ▼] [Sizes ▼]          │
├─────────────────────────────────────────────────────────────┤
│ Product Selection: [Select Product ▼] [◀] [▶]             │
├─────────────────────────────────────────────────────────────┤
│ Selected Product: Dr Denim Moxy Straight Pants (SKU-001)   │
│                   Sizes: S, M, L                           │
├─────────────────────────────────────────────────────────────┤
│ Workflow Stages:                                            │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐│
│ │ Upload  │Raw AI   │Selected │Upscale  │Retouch  │ Final   ││
│ │ [img]   │[img]    │[img]    │[img]    │[img]    │[img]    ││
│ │ 3 items │5 items  │2 items  │1 item   │1 item   │1 item   ││
│ └─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘│
├─────────────────────────────────────────────────────────────┤
│ Actions: [Download Selected] [Compare Selected] [Comments]  │
└─────────────────────────────────────────────────────────────┘
```

#### 4.1.2 Stage Card Design
```
┌─────────────────┐
│ Stage Name      │
├─────────────────┤
│                 │
│   [Thumbnail]   │
│                 │
├─────────────────┤
│ X items         │
│ [View All]      │
└─────────────────┘
```

### 4.2 Responsive Behavior

#### 4.2.1 Desktop (1200px+)
- 6 workflow stages displayed horizontally
- Full filter controls visible
- Side-by-side comparison modal

#### 4.2.2 Tablet (768px - 1199px)
- 4 workflow stages visible, horizontal scroll for others
- Collapsible filter controls
- Stacked comparison modal

#### 4.2.3 Mobile (< 768px)
- 2 workflow stages visible, horizontal scroll
- Bottom sheet for filters
- Full-screen comparison modal

### 4.3 Interaction Patterns

#### 4.3.1 Navigation
- **Arrow Keys**: Navigate between products
- **Tab**: Navigate between workflow stages
- **Enter/Space**: Open stage in full view
- **Escape**: Close modals/overlays

#### 4.3.2 Selection
- **Click**: Select/deselect stage
- **Shift+Click**: Select range of stages
- **Ctrl+Click**: Multi-select stages

## 5. Implementation Status

### 5.1 Phase 1: Foundation ✅ COMPLETED
- [x] ✅ Database migration for product sizes
- [x] ✅ Basic component structure with enhanced UI
- [x] ✅ Routing and navigation setup
- [x] ✅ Core data fetching hooks

### 5.2 Phase 2: Core Features ✅ COMPLETED
- [x] ✅ Product filtering and search (tags implemented)
- [x] ✅ Workflow stage grid display with professional design
- [x] ✅ Product navigation controls in header
- [x] ✅ Enhanced asset thumbnail display with navigation

### 5.3 Phase 3: Advanced Features ✅ PARTIALLY COMPLETED
- [x] ✅ Full-screen comparison modal structure
- [x] ✅ Comment integration with collaborative features
- [ ] ⏳ Bulk operations (planned)
- [x] ✅ Performance optimizations for image loading

### 5.4 Phase 4: Polish & Testing ✅ PARTIALLY COMPLETED
- [x] ✅ Responsive design refinements
- [x] ✅ Professional UI polish and visual hierarchy
- [ ] ⏳ Unit and E2E tests (planned)
- [ ] ⏳ User acceptance testing (planned)

### 5.5 Phase 5: Enhanced Features ✅ COMPLETED
- [x] ✅ Workflow progress tracking with visual indicators
- [x] ✅ Status-driven stage design (completed, in progress, pending, empty)
- [x] ✅ Professional header with share/export functionality
- [x] ✅ Metadata display and user management
- [x] ✅ Console toggle for additional functionality

## 6. Testing Strategy

### 6.1 Unit Tests
- Component rendering and props handling
- Hook functionality and state management
- Filter logic and data transformations
- Navigation and selection logic

### 6.2 Integration Tests
- API data fetching and caching
- Component interaction flows
- Filter and search functionality
- Modal and overlay behavior

### 6.3 E2E Tests
- Complete user workflows
- Cross-browser compatibility
- Performance under load
- Accessibility compliance

## 7. Rollout Plan

### 7.1 Development Environment
- Local development and testing
- Feature flag for gradual rollout
- Staging environment validation

### 7.2 Production Rollout
- **Week 1**: Internal team testing
- **Week 2**: Beta users (selected brand admins)
- **Week 3**: Gradual rollout to all users
- **Week 4**: Full availability

## 8. Success Criteria

### 8.1 Technical Acceptance
- [ ] All functional requirements implemented
- [ ] Performance benchmarks met
- [ ] Security review passed
- [ ] Accessibility standards met (WCAG 2.1 AA)

### 8.2 User Acceptance
- [ ] User testing sessions completed
- [ ] Feedback incorporated
- [ ] Training materials created
- [ ] Support documentation updated

## 9. Current Implementation Status

### 9.1 ✅ Completed Features (Phase 1 Complete)
- **Professional UI Design**: Enhanced visual hierarchy and modern interface
- **Workflow Progress Tracking**: Visual progress indicators with completion percentages
- **Status-Driven Stage Design**: Color-coded status system (completed, in progress, pending, empty)
- **Enhanced Navigation**: Product navigation with metadata display
- **Collaborative Comments**: User avatars, timestamps, and markdown support
- **Responsive Layout**: Adaptive grid system for different screen sizes
- **Image Management**: Thumbnail strips and navigation controls
- **Professional Polish**: Consistent design language and accessibility features
- **Core Data Flow**: Products with assets, workflow stage grouping, and asset display
- **State Management**: CompareContext with comprehensive state handling
- **Modal System**: Full-screen comparison modal with navigation

### 9.2 ⏳ Phase 2 Priorities (Next Development Cycle)
**Priority 1: Enhanced Filtering & Search**
- **SKU Search**: Real-time product filtering by SKU
- **Size-based Filtering**: Filter products by available sizes
- **Advanced Tag Filtering**: Multi-tag selection with AND/OR logic
- **Filter Persistence**: Remember user filter preferences

**Priority 2: Performance & UX Improvements**
- **Image Lazy Loading**: Progressive image loading for better performance
- **Keyboard Navigation**: Arrow keys for product navigation, shortcuts for actions
- **Bulk Operations**: Multi-asset selection and batch download
- **Enhanced Loading States**: Skeleton loaders and progressive enhancement

**Priority 3: Advanced Features**
- **Zoom & Pan**: Detailed image inspection capabilities
- **Export Functionality**: PDF reports and comparison exports
- **Asset Metadata Display**: Enhanced asset information overlay
- **Stage Progress Indicators**: Visual workflow completion tracking

### 9.3 🔮 Phase 3 Future Enhancements
- **Multi-Product Comparison**: Compare workflow stages across multiple products
- **AI-Powered Insights**: Quality scoring and automated suggestions
- **Real-time Collaboration**: Live editing and synchronized viewing
- **Advanced Analytics**: Workflow performance metrics and reporting
- **Custom Workflows**: User-defined stage configurations
- **API Integration**: Third-party tool connectivity

### 9.4 🚀 Technical Debt & Quality Assurance
- **Unit Testing**: Comprehensive test coverage for all components
- **E2E Testing**: User workflow validation and regression testing
- **Performance Monitoring**: Image loading optimization and metrics
- **Accessibility Audit**: WCAG 2.1 AA compliance verification
- **Documentation**: Component documentation and usage guides
- **Error Handling**: Comprehensive error states and recovery mechanisms

---

**Document Version:** 2.0
**Last Updated:** 2025-01-27
**Next Review:** 2025-02-03
**Implementation Status:** Phase 1 Complete - Core UI Implemented
