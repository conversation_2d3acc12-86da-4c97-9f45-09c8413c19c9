# Asset Compare View - Phase 1 Scope

**Feature ID:** FAS-78  
**Phase:** 1 (Product-Required)  
**Status:** Ready for Implementation  
**Last Updated:** 2025-01-07  

## 🎯 Phase 1 Scope: Product-Required Compare View

### **Core Concept**
Users must select a specific product to view all its workflow stages side by side. This creates a focused, product-centric workflow review experience.

### **Key Requirements**

#### ✅ **INCLUDED in Phase 1:**
- **Product Selection Required**: User must choose a specific product
- **Product Filtering**: Filter product list by SKU, tags, and sizes
- **Workflow Stage Display**: Show all stages horizontally for selected product
- **Product Navigation**: Arrow controls to move between products
- **Asset Thumbnails**: Show representative images for each stage
- **Asset Counts**: Display number of assets in each stage
- **Full-Screen Comparison**: Modal for detailed asset comparison
- **Comments**: Add feedback on specific stages

#### ❌ **NOT INCLUDED in Phase 1:**
- Ungrouped assets (assets without product_id)
- Multi-product comparison
- Tag-only filtering (without product selection)
- Size-only filtering (without product selection)
- Bulk operations across products

### **User Flow**

1. **Enter Compare View** for a collection
2. **Filter Products** (optional) by SKU, tags, or sizes
3. **Select Product** from dropdown (required step)
4. **View Workflow Stages** horizontally for that product
5. **Navigate Between Products** using arrow controls
6. **Compare Assets** in full-screen modal
7. **Add Comments** on specific stages

### **UI Layout**

```
┌─────────────────────────────────────────────────────────────┐
│ Header: Collection Name > Compare View                      │
├─────────────────────────────────────────────────────────────┤
│ Product Filters: [SKU Search] [Tags ▼] [Sizes ▼]          │
├─────────────────────────────────────────────────────────────┤
│ Product Selection: [Select Product ▼] [◀] [▶]             │
├─────────────────────────────────────────────────────────────┤
│ Selected Product: Dr Denim Moxy Straight Pants (SKU-001)   │
│                   Sizes: S, M, L                           │
├─────────────────────────────────────────────────────────────┤
│ Workflow Stages:                                            │
│ ┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐│
│ │ Upload  │Raw AI   │Selected │Upscale  │Retouch  │ Final   ││
│ │ [img]   │[img]    │[img]    │[img]    │[img]    │[img]    ││
│ │ 3 items │5 items  │2 items  │1 item   │1 item   │1 item   ││
│ └─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘│
├─────────────────────────────────────────────────────────────┤
│ Actions: [Download Selected] [Compare Selected] [Comments]  │
└─────────────────────────────────────────────────────────────┘
```

### **Technical Implementation**

#### **Database Queries**
```sql
-- Get products with assets and workflow stage counts
SELECT p.*, COUNT(a.id) as total_assets,
       COUNT(CASE WHEN a.workflow_stage = 'upload' THEN 1 END) as upload_count,
       -- ... other stage counts
FROM products p
JOIN assets a ON a.product_id = p.id
WHERE a.collection_id = $1
GROUP BY p.id
HAVING COUNT(a.id) > 0;

-- Get all assets for selected product
SELECT a.* FROM assets a
WHERE a.collection_id = $1 AND a.product_id = $2
ORDER BY a.workflow_stage, a.created_at;
```

#### **Component Structure**
```
AssetCompareView
├── ProductFilters (SKU, tags, sizes)
├── ProductNavigator (selection + arrow controls)
├── ProductInfo (selected product details)
├── WorkflowStageGrid
│   └── AssetStageCard (×6 workflow stages)
└── CompareModal (full-screen comparison)
```

#### **State Management**
```typescript
interface CompareState {
  selectedProductId: string | null; // Required
  productFilters: ProductFilters;
  selectedAssetIds: string[];
  isModalOpen: boolean;
  availableProducts: ProductWithAssets[];
  selectedProductAssets: AssetsByStage;
}
```

### **Success Criteria**

#### **Functional**
- [ ] User can filter products by SKU, tags, and sizes
- [ ] User must select a product to view workflow stages
- [ ] All workflow stages display correctly for selected product
- [ ] Navigation between products works smoothly
- [ ] Asset thumbnails load efficiently
- [ ] Full-screen comparison modal functions
- [ ] Comments can be added to stages

#### **Technical**
- [ ] Page loads in < 2 seconds
- [ ] Product navigation < 500ms
- [ ] Responsive design works on desktop/tablet
- [ ] Proper error handling and loading states
- [ ] Accessibility compliance (WCAG 2.1 AA)

#### **User Experience**
- [ ] Clear indication when no product is selected
- [ ] Intuitive product selection and navigation
- [ ] Smooth transitions between products
- [ ] Helpful empty states and loading indicators

### **Future Phases**

#### **Phase 2: Multi-Product Support**
- Tag-only and size-only filtering
- Multi-product comparison
- Ungrouped assets support
- Bulk operations across products

#### **Phase 3: Advanced Features**
- AI quality scoring
- Automated suggestions
- Advanced analytics
- External tool integration

### **Implementation Timeline**

- **Week 1**: Database migration + routing + core components
- **Week 2**: Product filtering + navigation + stage grid
- **Week 3**: Comparison modal + comments + polish
- **Week 4**: Testing + documentation + deployment

### **Ready to Start**

✅ **Documentation Complete**  
✅ **Scope Clearly Defined**  
✅ **Technical Architecture Ready**  
✅ **Implementation Plan Detailed**  

**Next Step**: Begin Phase 1 implementation with database migration for product sizes field.

---

**This focused approach ensures we build a solid foundation that users will love, then expand based on real feedback and usage patterns.**
