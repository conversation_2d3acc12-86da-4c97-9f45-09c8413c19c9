# FashionLab Invitation System Analysis

## Overview
The FashionLab platform uses an email-based invitation system to add new members to organizations. The system supports both organization member invitations and platform admin invitations.

## Current Implementation

### 1. Invitation Creation (`InviteMemberForm.tsx`)

**Who can invite:**
- Based on the RLS policies after the single role system migration, **any authenticated user** can create invitations
- However, the UI and frontend logic suggests the intention was to restrict this to organization admins
- The current implementation has simplified RLS policies that delegate permission checking to the application layer

**Process:**
1. User enters one or more email addresses (comma or newline separated)
2. System validates email formats
3. System checks if users are already members of the organization
4. For each valid email:
   - Generates a unique invitation token (UUID)
   - Creates a record in `pending_invitations` table
   - Sends an invitation email via Supabase Edge Function

**Key features:**
- Support for bulk invitations
- Prevention of duplicate invitations
- Automatic expiration after 48 hours
- Optional personal message
- Default role assignment as 'brand_member'

### 2. Email Sending (`emailUtils.ts` + Edge Function)

**Email system:**
- Uses Supabase Edge Functions with Resend API
- Sends beautifully formatted HTML emails with fallback plain text
- Includes invitation link: `{origin}/invite/{token}`
- Local development support with direct URLs for testing

**Edge Function (`send-email/index.ts`):**
- Generic email sending function
- Requires `RESEND_API_KEY` environment variable
- Supports both HTML and plain text content
- CORS-enabled for cross-origin requests

### 3. Invitation Acceptance (`InvitationAccept.tsx`)

**Flow for new users:**
1. User clicks invitation link
2. System validates invitation (not expired, not already accepted)
3. User can either:
   - **Sign up** (create new account)
   - **Sign in** (existing account)
4. Upon successful authentication, invitation is accepted

**Flow for existing users:**
1. If user is already logged in and email confirmed, invitation is auto-accepted
2. Otherwise, user must sign in to accept

**Database trigger (`handle_email_confirmation`):**
- Automatically creates user profile when email is confirmed
- Creates organization membership for organization invitations
- Assigns default role ('brand_member' for org invitations, 'platform_admin' for platform invitations)
- Marks invitation as accepted
- Logs security event

### 4. Database Structure

**`pending_invitations` table:**
```sql
- id (UUID)
- email (text) - recipient email
- organization_id (UUID, nullable) - null for platform admin invitations
- invited_by (UUID) - user who sent invitation
- token (UUID) - unique invitation token
- accepted (boolean, default false)
- accepted_at (timestamp, nullable)
- expires_at (timestamp) - 48 hours from creation
- message (text, nullable) - personal message
- created_at, updated_at
```

**RLS Policies (current state):**
- **SELECT**: Any authenticated user OR unauthenticated with valid token
- **INSERT**: Any authenticated user
- **UPDATE**: Any authenticated user
- **DELETE**: Any authenticated user

*Note: These are simplified policies that rely on application-layer permission checks*

### 5. Security Considerations

**Current vulnerabilities:**
1. **Overly permissive RLS policies** - Any authenticated user can create invitations for any organization
2. **No server-side role validation** - The system trusts the frontend to enforce permissions
3. **Potential for invitation spam** - No rate limiting on invitation creation

**Existing safeguards:**
- Invitation tokens are UUIDs (hard to guess)
- 48-hour expiration limit
- Email confirmation required for new accounts
- Duplicate invitation prevention
- Security activity logging

### 6. User Experience

**For inviters:**
- Simple bulk invitation interface
- Clear feedback on success/failure
- Local development testing support

**For invitees:**
- Clear invitation page with organization branding
- Choice between sign up and sign in
- Pre-filled email from invitation
- Clear error messages for expired/invalid invitations

## Recommendations

1. **Fix RLS policies** to properly restrict invitation creation to organization admins
2. **Add rate limiting** to prevent invitation spam
3. **Add server-side validation** for user permissions
4. **Consider adding invitation quotas** per organization
5. **Add invitation management UI** to view/revoke pending invitations
6. **Implement role selection** during invitation (currently hardcoded to 'brand_member')

## Related Files

- Frontend: `/src/components/organizations/InviteMemberForm.tsx`
- Frontend: `/src/pages/InvitationAccept.tsx`
- Utils: `/src/components/common/utils/emailUtils.ts`
- Edge Function: `/supabase/functions/send-email/index.ts`
- Database: Various migration files in `/supabase/migrations/`
- Database Trigger: `handle_email_confirmation` function