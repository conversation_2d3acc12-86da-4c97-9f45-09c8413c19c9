# IPv6 Database Connection Issue

## Problem
When connecting to Supabase databases, you may encounter this error:
```
failed to connect to postgres: failed to connect to `host=db.xxx.supabase.co user=postgres database=postgres`: 
dial error (dial tcp [IPv6_ADDRESS]:5432: connect: no route to host)
```

This happens when your system tries to connect via IPv6 but doesn't have proper IPv6 routing configured.

## Solution

### Use the Pooler URL (Recommended)
The pooler URL resolves to IPv4 and avoids the IPv6 issue entirely:

```bash
# Staging
psql "postgresql://postgres.qnfmiotatmkoumlymynq:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"

# Production  
psql "postgresql://postgres.cpelxqvcjnbpnphttzsn:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"
```

### For Supabase CLI Operations
When using `supabase db push` or similar commands:

```bash
# Use the --db-url flag with pooler URL
supabase db push --db-url "postgresql://postgres.PROJECT_REF:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"

# For migrations
supabase migration repair --db-url "postgresql://postgres.PROJECT_REF:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"
```

### For Direct SQL Execution
```bash
# Apply a migration file directly
psql "postgresql://postgres.PROJECT_REF:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres" -c "$(cat path/to/migration.sql)"

# Run a query
psql "postgresql://postgres.PROJECT_REF:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres" -c "SELECT * FROM users LIMIT 5;"
```

## Alternative Solutions

### 1. Force IPv4 Resolution (System-wide)
Add to `/etc/hosts`:
```
# Force IPv4 for Supabase
***********  db.qnfmiotatmkoumlymynq.supabase.co
***********  db.cpelxqvcjnbpnphttzsn.supabase.co
```
*Note: IP addresses may change, so this is not recommended for production use.*

### 2. Disable IPv6 (Not Recommended)
On macOS, you can temporarily disable IPv6:
```bash
networksetup -setv6off Wi-Fi
# To re-enable: networksetup -setv6automatic Wi-Fi
```

## Key Points
- Always use the pooler URL format: `postgres.PROJECT_REF:<EMAIL>`
- The pooler URL provides better connection stability and IPv4 compatibility
- This issue affects all Supabase CLI commands that connect to remote databases
- The Supabase dashboard SQL editor is not affected by this issue

## Related Issues
- Supabase CLI linking failures
- Database migration deployment errors
- Direct psql connection timeouts