# Supabase Common Issues & Solutions

This document covers common issues encountered with Supabase and their solutions, based on real debugging experiences.

## 🚨 Critical Issues

### Fashion Lab API Authentication (RESOLVED)
**Issue**: Images generating but not storing in database
**Cause**: Incorrect authentication format (`Bearer` vs `jwt`)
**Status**: ✅ Fixed
**Guide**: [Fashion Lab Authentication Fix](./fashion-lab-authentication-fix.md)

## Table of Contents
1. [Fashion Lab API Authentication](#fashion-lab-api-authentication-resolved)
2. [RLS Policies Blocking Returned Data](#rls-policies-blocking-returned-data)
3. [Invalid PostgREST Query Syntax](#invalid-postgrest-query-syntax)
3. [Update Verification & Replication Lag](#update-verification--replication-lag)
4. [Debugging Supabase Issues](#debugging-supabase-issues)

---

## RLS Policies Blocking Returned Data

### Problem
Updates succeed but return empty arrays when using `.select()`, even though the user has permission to update.

### Symptoms
```javascript
const { data, error } = await supabase
  .from('table')
  .update({ field: 'value' })
  .eq('id', id)
  .select();

// data is [] even though update succeeded
```

### Root Cause
The RLS policy's `WITH CHECK` clause filters the returned data. Even if the `USING` clause allows the update, the `WITH CHECK` clause determines what data can be returned.

### Solution
Create separate policies for different user types, especially for admin users:

```sql
-- Separate policy for platform/admin users
CREATE POLICY "Platform users can update any record"
ON public.table_name FOR UPDATE
USING (
  auth.uid() IN (
    SELECT id FROM public.users 
    WHERE role IN ('platform_super', 'platform_admin')
  )
)
WITH CHECK (true);  -- Allow returning any data

-- Regular user policy with restrictions
CREATE POLICY "Users can update their own records"
ON public.table_name FOR UPDATE
USING (
  auth.uid() = user_id
)
WITH CHECK (
  auth.uid() = user_id  -- Only return data they own
);
```

### Key Learning
- `USING` clause: Controls WHO can perform the operation
- `WITH CHECK` clause: Controls WHAT data can be returned/inserted
- For admin users, use `WITH CHECK (true)` to avoid filtering

---

## Invalid PostgREST Query Syntax

### Problem
Attempting to use invalid query syntax causes 400 Bad Request errors.

### Example of Invalid Syntax
```javascript
// ❌ This doesn't work - invalid OR syntax
query = query.or(`id.neq.${timestamp}_never_matches`);
```

### Error Message
```
400 Bad Request
```

### Solution
Use valid PostgREST operators and syntax:

```javascript
// ✅ Valid OR syntax
query = query.or('status.eq.active,status.eq.pending');

// ✅ For cache busting, use query key instead
const { data } = await supabase
  .from('table')
  .select('*')
  .eq('field', value);

// Use React Query's queryKey for cache management
queryKey: ['assets', collectionId, timestamp]
```

### PostgREST Valid Operators
- `eq` - equals
- `neq` - not equals
- `gt` - greater than
- `gte` - greater than or equal
- `lt` - less than
- `lte` - less than or equal
- `like` - LIKE operator
- `ilike` - case-insensitive LIKE
- `in` - in array
- `is` - IS (for null checks)

---

## Update Verification & Replication Lag

### Problem
Updates appear to succeed but data doesn't change immediately due to replication lag or other issues.

### Solution Pattern
```typescript
export async function updateWithVerification(
  supabase: SupabaseClient,
  table: string,
  updates: Record<string, any>,
  filter: { field: string; value: any }
) {
  // 1. Perform update with .select() for immediate feedback
  const { data: updateData, error } = await supabase
    .from(table)
    .update(updates)
    .eq(filter.field, filter.value)
    .select();

  if (error) throw error;

  // 2. Check if update returned data
  if (!updateData || updateData.length === 0) {
    throw new Error('Update failed - no data returned (possible RLS issue)');
  }

  // 3. Verify the update was applied
  const isUpdated = updateData.every(row => 
    Object.entries(updates).every(([key, value]) => 
      row[key] === value
    )
  );

  if (!isUpdated) {
    throw new Error('Update was not applied by the database');
  }

  // 4. Wait for replication if needed
  await new Promise(resolve => setTimeout(resolve, 1500));

  return updateData;
}
```

### Key Practices
1. Always use `.select()` on updates to get immediate feedback
2. Check if data was returned (RLS issue if empty)
3. Verify the returned data has the expected values
4. Add delay for replication lag in production environments
5. Implement retry logic for critical updates

---

## Debugging Supabase Issues

### 1. Enable Detailed Logging

```typescript
// Add comprehensive logging to operations
console.log('[Operation] Starting...', {
  table,
  operation: 'update',
  data: updates,
  filter
});

const { data, error } = await supabase
  .from(table)
  .update(updates)
  .eq('id', id)
  .select();

console.log('[Operation] Result:', {
  success: !error,
  dataReturned: data?.length || 0,
  error: error?.message,
  sample: data?.[0]
});
```

### 2. Check RLS Policies

```sql
-- View all policies for a table
SELECT 
    policyname,
    permissive,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public' 
    AND tablename = 'your_table'
ORDER BY cmd, policyname;

-- Check if RLS is enabled
SELECT relrowsecurity 
FROM pg_class 
WHERE relname = 'your_table';
```

### 3. Test User Permissions

```sql
-- Check current user context
SELECT 
    auth.uid() as user_id,
    auth.role() as jwt_role,
    u.role as user_role,
    u.email
FROM public.users u
WHERE u.id = auth.uid();

-- Test if user can see specific records
SELECT * FROM your_table WHERE id = 'record-id';
```

### 4. Create Debug Functions

```sql
CREATE OR REPLACE FUNCTION debug_update_record(
  p_table_name TEXT,
  p_record_id UUID,
  p_updates JSONB
)
RETURNS TABLE(
  success BOOLEAN,
  error_message TEXT,
  old_data JSONB,
  new_data JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_old_data JSONB;
  v_new_data JSONB;
  v_error TEXT;
BEGIN
  -- Get current data
  EXECUTE format('SELECT to_jsonb(t) FROM %I.%I t WHERE id = $1', 'public', p_table_name)
  INTO v_old_data
  USING p_record_id;

  -- Try update
  BEGIN
    EXECUTE format('UPDATE %I.%I SET data = data || $1 WHERE id = $2', 'public', p_table_name)
    USING p_updates, p_record_id;
    
    -- Get new data
    EXECUTE format('SELECT to_jsonb(t) FROM %I.%I t WHERE id = $1', 'public', p_table_name)
    INTO v_new_data
    USING p_record_id;
    
    RETURN QUERY SELECT true, NULL::TEXT, v_old_data, v_new_data;
  EXCEPTION 
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS v_error = MESSAGE_TEXT;
      RETURN QUERY SELECT false, v_error, v_old_data, NULL::JSONB;
  END;
END;
$$;
```

### 5. Common Debugging Checklist

1. **Check browser console** for detailed error messages
2. **Verify user authentication** - Is the user logged in?
3. **Check user role** - Does the user have the expected role?
4. **Test in SQL Editor** - Try the operation directly in Supabase dashboard
5. **Check RLS policies** - Are they too restrictive?
6. **Look for triggers** - Any database triggers affecting the operation?
7. **Check for constraints** - Foreign keys, unique constraints, etc.
8. **Verify data types** - Enum mismatches, type casting issues
9. **Test locally first** - Does it work with local Supabase?
10. **Check migrations** - Are all migrations applied in production?

---

## Best Practices

### 1. Always Use .select() on Updates
```typescript
// ❌ Don't do this
const { error } = await supabase
  .from('table')
  .update(data)
  .eq('id', id);

// ✅ Do this instead
const { data: updatedData, error } = await supabase
  .from('table')
  .update(data)
  .eq('id', id)
  .select();

// Now you can verify the update
if (!updatedData || updatedData.length === 0) {
  console.error('Update failed - no data returned');
}
```

### 2. Implement Standard Error Handling
```typescript
function handleSupabaseError(error: any) {
  if (!error) return null;
  
  const errorMap = {
    '42501': 'Permission denied',
    '42P01': 'Table not found',
    '23505': 'Duplicate entry',
    '23503': 'Foreign key violation',
    '22P02': 'Invalid input syntax',
    'PGRST301': 'JWT expired'
  };
  
  return {
    message: errorMap[error.code] || error.message,
    code: error.code,
    details: error.details
  };
}
```

### 3. Use Transactions for Complex Operations
```typescript
// Use Supabase's transaction support when available
// or implement compensating transactions
async function complexOperation() {
  const updates = [];
  
  try {
    // Track all operations
    updates.push(await operation1());
    updates.push(await operation2());
    updates.push(await operation3());
    
    return updates;
  } catch (error) {
    // Rollback logic
    for (const update of updates.reverse()) {
      await rollback(update);
    }
    throw error;
  }
}
```

### 4. Monitor Production Issues
- Set up error tracking (Sentry, LogRocket, etc.)
- Log all Supabase errors with context
- Monitor slow queries in Supabase dashboard
- Set up alerts for failed operations

---

## Related Resources
- [Supabase RLS Documentation](https://supabase.com/docs/guides/auth/row-level-security)
- [PostgREST Operators](https://postgrest.org/en/stable/api.html#operators)
- [Supabase Debugging Guide](https://supabase.com/docs/guides/platform/debugging)
- [PostgreSQL Error Codes](https://www.postgresql.org/docs/current/errcodes-appendix.html)