# Tag System Redesign - Implementation Plan

## Overview

This document outlines the complete implementation plan for redesigning the FashionLab tag system to support proper multi-tenant isolation while maintaining usability.

## Current Issues

1. **Privacy & Security (FAS-95)**: Tags are global across ALL organizations
2. **Visibility Issues (FAS-127, FAS-129)**: Tags only appear if used on assets
3. **Scalability**: Global tag list becomes unwieldy as platform grows

## Solution: Category-Based Tag System with Collection Scoping

### Tag Categories

The new system implements four distinct tag categories:

1. **Global Tags** (`category = 'global'`)
   - Platform-wide workflow stages managed by FashionLab
   - Examples: upload, draft, upscale, retouch, final
   - Visible to all users, editable only by platform admins

2. **Angles** (`category = 'angles'`)
   - Standard view types for fashion photography
   - Examples: front, back, side, detail, packshot, lifestyle
   - Visible to all users, managed by platform

3. **Styling Tags** (`category = 'styling'`)
   - Style-related tags for fashion items
   - Can be global or collection-specific (future implementation)
   - Examples: casual, formal, streetwear, etc.

4. **Collection Tags** (`category = 'collection'`)
   - Customer-created tags specific to their collections
   - Private to the organization
   - Fully customizable by brand users

### Database Schema Changes

```sql
-- Step 1: Update tag_category enum
CREATE TYPE tag_category AS ENUM (
  'global',      -- Platform-wide tags
  'angles',      -- View angles
  'styling',     -- Style-related tags
  'collection'   -- Customer-created tags
);

-- Step 2: Add collection_id to tags table
ALTER TABLE tags 
ADD COLUMN collection_id UUID REFERENCES collections(id) ON DELETE CASCADE;

-- Step 3: Update unique constraint
ALTER TABLE tags DROP CONSTRAINT IF EXISTS tags_name_category_key;
ALTER TABLE tags ADD CONSTRAINT tags_name_category_collection_key 
  UNIQUE NULLS NOT DISTINCT (name, category, collection_id);

-- Step 4: Create performance indexes
CREATE INDEX idx_tags_collection_id ON tags(collection_id) WHERE collection_id IS NOT NULL;
CREATE INDEX idx_tags_category ON tags(category);
CREATE INDEX idx_tags_global ON tags(category) 
  WHERE category IN ('global', 'angles', 'styling') AND collection_id IS NULL;
```

### Migration Strategy

#### Phase 1: Categorize Existing Tags

```sql
-- Migrate existing categories to new enum values
ALTER TABLE tags 
  ALTER COLUMN category TYPE tag_category 
  USING CASE 
    WHEN category::text = 'view_type' THEN 'angles'::tag_category
    WHEN category::text = 'workflow_stage' THEN 'global'::tag_category
    WHEN category::text = 'product_specific' THEN 'collection'::tag_category
    WHEN category::text = 'custom' THEN 'collection'::tag_category
    ELSE 'collection'::tag_category
  END;

-- Set collection_id to NULL for global, angles, and styling tags
UPDATE tags 
SET collection_id = NULL
WHERE category IN ('global', 'angles', 'styling');

-- Common workflow stages become global
UPDATE tags 
SET category = 'global', collection_id = NULL
WHERE name IN ('upload', 'draft', 'upscale', 'retouch', 'final', 'brief', 'input', 'custom_models', 'library', 'raw');

-- Common view types become angles
UPDATE tags
SET category = 'angles', collection_id = NULL
WHERE name IN ('front', 'back', 'detail', 'packshot', 'lifestyle', 'side', 'close-up', '45-degree', 'top-down');
```

#### Phase 2: Assign Collection IDs to Non-Global Tags

```sql
-- For tags used in only one collection, assign that collection
WITH single_collection_tags AS (
  SELECT 
    t.id,
    MIN(a.collection_id) as collection_id,
    COUNT(DISTINCT a.collection_id) as collection_count
  FROM tags t
  JOIN asset_tags at ON t.id = at.tag_id
  JOIN assets a ON at.asset_id = a.id
  WHERE t.is_global = false
  GROUP BY t.id
  HAVING COUNT(DISTINCT a.collection_id) = 1
)
UPDATE tags t
SET collection_id = sct.collection_id
FROM single_collection_tags sct
WHERE t.id = sct.id;

-- For tags used across multiple collections, create duplicates per collection
INSERT INTO tags (name, category, color, collection_id, is_global)
SELECT DISTINCT
  t.name,
  t.category,
  t.color,
  a.collection_id,
  false
FROM tags t
JOIN asset_tags at ON t.id = at.tag_id
JOIN assets a ON at.asset_id = a.id
WHERE 
  t.is_global = false 
  AND t.collection_id IS NULL
  AND a.collection_id IS NOT NULL;

-- Update asset_tags to point to the new collection-specific tags
UPDATE asset_tags at
SET tag_id = (
  SELECT t2.id 
  FROM tags t2
  JOIN assets a ON a.id = at.asset_id
  WHERE 
    t2.name = (SELECT name FROM tags WHERE id = at.tag_id)
    AND t2.category = (SELECT category FROM tags WHERE id = at.tag_id)
    AND t2.collection_id = a.collection_id
    AND t2.is_global = false
  LIMIT 1
)
WHERE EXISTS (
  SELECT 1 
  FROM tags t
  JOIN assets a ON a.id = at.asset_id
  JOIN tags t2 ON t2.name = t.name 
    AND t2.category = t.category 
    AND t2.collection_id = a.collection_id
  WHERE t.id = at.tag_id 
    AND t.is_global = false 
    AND t.collection_id IS NULL
);

-- Clean up orphaned tags
DELETE FROM tags 
WHERE is_global = false 
  AND collection_id IS NULL
  AND id NOT IN (SELECT DISTINCT tag_id FROM asset_tags);
```

### Updated RLS Policies

```sql
-- New SELECT policy: Users can see global tags + tags from their collections
CREATE POLICY "Users can view relevant tags" ON tags
FOR SELECT TO authenticated
USING (
  -- Global, angles, and styling tags are visible to all
  (category IN ('global', 'angles', 'styling') AND collection_id IS NULL)
  OR 
  -- Collection tags are visible to organization members
  collection_id IN (
    SELECT c.id 
    FROM collections c
    JOIN organization_memberships om ON c.organization_id = om.organization_id
    WHERE om.user_id = auth.uid()
  )
);

-- INSERT policy: Users can only create collection-specific tags
CREATE POLICY "Users can create collection tags" ON tags
FOR INSERT TO authenticated
WITH CHECK (
  -- Must be a collection tag
  category = 'collection'
  AND 
  -- Must specify a collection
  collection_id IS NOT NULL
  AND
  -- Must be a member of the organization
  collection_id IN (
    SELECT c.id 
    FROM collections c
    JOIN organization_memberships om ON c.organization_id = om.organization_id
    WHERE om.user_id = auth.uid()
  )
);

-- UPDATE policy: Platform admins can update any tag, brand admins can update collection tags
CREATE POLICY "Admins can update tags" ON tags
FOR UPDATE TO authenticated
USING (
  -- Platform admins can update any tag
  (SELECT role FROM users WHERE id = auth.uid()) IN ('platform_admin', 'platform_super')
  OR
  -- Brand admins can update collection tags in their organization
  (
    category = 'collection'
    AND collection_id IN (
      SELECT c.id 
      FROM collections c
      JOIN organization_memberships om ON c.organization_id = om.organization_id
      WHERE om.user_id = auth.uid()
        AND (SELECT role FROM users WHERE id = auth.uid()) = 'brand_admin'
    )
  )
);

-- DELETE policy: Same as update
CREATE POLICY "Admins can delete tags" ON tags
FOR DELETE TO authenticated
USING (
  -- Platform admins can delete any tag
  (SELECT role FROM users WHERE id = auth.uid()) IN ('platform_admin', 'platform_super')
  OR
  -- Brand admins can delete collection tags in their organization
  (
    category = 'collection'
    AND collection_id IN (
      SELECT c.id 
      FROM collections c
      JOIN organization_memberships om ON c.organization_id = om.organization_id
      WHERE om.user_id = auth.uid()
        AND (SELECT role FROM users WHERE id = auth.uid()) = 'brand_admin'
    )
  )
);
```

## Frontend Implementation Changes

### 1. Update `useTags` Hook

```typescript
// src/lib/hooks/useTags.ts
export function useTags(collectionId?: string) {
  return useQuery({
    queryKey: ['tags', collectionId],
    queryFn: async () => {
      let query = supabase
        .from('tags')
        .select('*')
        .order('name');

      // If in a collection context, get global + collection tags
      if (collectionId) {
        query = query.or(`is_global.eq.true,collection_id.eq.${collectionId}`);
      } else {
        // Outside collection context, only show global tags
        query = query.eq('is_global', true);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
  });
}
```

### 2. Update Tag Creation UI

```typescript
// src/components/tags/CreateTagDialog.tsx
interface CreateTagDialogProps {
  collectionId: string;
  onSuccess?: () => void;
}

export function CreateTagDialog({ collectionId, onSuccess }: CreateTagDialogProps) {
  const createTag = async (values: TagFormValues) => {
    const { error } = await supabase
      .from('tags')
      .insert({
        ...values,
        collection_id: collectionId,
        is_global: false, // Users can only create collection tags
      });

    if (error) throw error;
    onSuccess?.();
  };

  // ... rest of component
}
```

### 3. Visual Indicators in UI

```typescript
// src/components/tags/TagBadge.tsx
export function TagBadge({ tag }: { tag: Tag }) {
  const getIcon = () => {
    switch (tag.category) {
      case 'global':
        return <Globe className="w-3 h-3 mr-1" />;
      case 'angles':
        return <Camera className="w-3 h-3 mr-1" />;
      case 'styling':
        return <Palette className="w-3 h-3 mr-1" />;
      case 'collection':
        return <Folder className="w-3 h-3 mr-1" />;
      default:
        return null;
    }
  };

  return (
    <Badge style={{ backgroundColor: tag.color }}>
      {getIcon()}
      {tag.name}
    </Badge>
  );
}
```

### 4. Update FilterSidebar

```typescript
// src/components/filters/FilterSidebar.tsx
export function FilterSidebar({ collectionId }: { collectionId: string }) {
  const { data: tags } = useTags(collectionId); // Now scoped to collection

  // Group tags by category for better UX
  const tagsByCategory = {
    global: tags?.filter(t => t.category === 'global') || [],
    angles: tags?.filter(t => t.category === 'angles') || [],
    styling: tags?.filter(t => t.category === 'styling') || [],
    collection: tags?.filter(t => t.category === 'collection') || []
  };

  return (
    <div className="space-y-6">
      {tagsByCategory.global.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2">Workflow Stages</h4>
          <div className="space-y-1">
            {tagsByCategory.global.map(tag => <TagFilter key={tag.id} tag={tag} />)}
          </div>
        </div>
      )}
      
      {tagsByCategory.angles.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2">Angles</h4>
          <div className="space-y-1">
            {tagsByCategory.angles.map(tag => <TagFilter key={tag.id} tag={tag} />)}
          </div>
        </div>
      )}
      
      {tagsByCategory.styling.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2">Styling</h4>
          <div className="space-y-1">
            {tagsByCategory.styling.map(tag => <TagFilter key={tag.id} tag={tag} />)}
          </div>
        </div>
      )}
      
      {tagsByCategory.collection.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2">Collection Tags</h4>
          <div className="space-y-1">
            {tagsByCategory.collection.map(tag => <TagFilter key={tag.id} tag={tag} />)}
          </div>
        </div>
      )}
    </div>
  );
}
```

## Testing Plan

### 1. Unit Tests

```typescript
// src/lib/hooks/__tests__/useTags.test.ts
describe('useTags', () => {
  it('returns only global tags when no collectionId provided', async () => {
    const { result } = renderHook(() => useTags());
    await waitFor(() => expect(result.current.isSuccess).toBe(true));
    
    expect(result.current.data).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ is_global: true })
      ])
    );
  });

  it('returns global and collection tags when collectionId provided', async () => {
    const { result } = renderHook(() => useTags('collection-123'));
    await waitFor(() => expect(result.current.isSuccess).toBe(true));
    
    const globalTags = result.current.data?.filter(t => t.is_global);
    const collectionTags = result.current.data?.filter(t => t.collection_id === 'collection-123');
    
    expect(globalTags?.length).toBeGreaterThan(0);
    expect(collectionTags?.length).toBeGreaterThan(0);
  });
});
```

### 2. Integration Tests

```sql
-- Test organization isolation
BEGIN;
SET LOCAL ROLE authenticated;
SET LOCAL request.jwt.claim.sub TO 'user-from-org-a';

-- Should not see tags from other organizations
SELECT COUNT(*) FROM tags WHERE collection_id IN (
  SELECT id FROM collections WHERE organization_id = 'org-b-id'
);
-- Expected: 0

ROLLBACK;
```

### 3. Migration Testing

1. **Backup production data**
2. **Test migration on staging with production data copy**
3. **Verify tag counts match expected results**
4. **Test all user roles can access appropriate tags**
5. **Verify no data loss**

## Local Development Workflow

### Phase 1: Database Changes (Complete)
1. Created feature branch: `feature/tag-system-redesign`
2. Created migration file: `20250702212526_tag_system_redesign.sql`
3. Updated tag categories: global, angles, styling, collection

### Phase 2: Local Testing
```bash
# Reset local database with new migration
supabase db reset

# Start local Supabase
supabase start

# Test the migration locally
npm run dev
```

### Phase 3: Frontend Implementation (Next Steps)
1. Update TypeScript types for new tag categories
2. Update React hooks to handle category-based filtering
3. Update UI components for tag creation and display
4. Test all tag operations locally

### Phase 4: Prepare for Review
1. Test all scenarios locally
2. Document any breaking changes
3. Update migration script if needed
4. Create PR for review (DO NOT merge to staging/production)

## Success Metrics

1. **Zero cross-organization tag visibility** (security metric)
2. **Tags visible immediately after creation** (fixes FAS-127/129)
3. **<50ms query time for tag fetching** (performance metric)
4. **90%+ user satisfaction with new tag system** (UX metric)

## Rollback Plan

If issues arise:

1. **Revert RLS policies** to original state
2. **Keep schema changes** (they're backward compatible)
3. **Revert frontend to use all tags** temporarily
4. **Investigate and fix issues**
5. **Re-deploy with fixes**

## Long-term Considerations

1. **Tag Templates**: Allow organizations to create tag templates
2. **Tag Hierarchies**: Support parent-child tag relationships
3. **Tag Permissions**: Granular permissions per tag
4. **Tag Analytics**: Usage statistics and insights
5. **Bulk Tag Management**: Admin tools for tag cleanup