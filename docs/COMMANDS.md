# Claude Commands

## Daily Work

```bash
# Start your day
- Check Linear issues: mcp__linear__getIssues
- Check project status: npm run status
- Start local dev: npm run dev

# Work on a feature
- Get issue details: mcp__linear__getIssueById --id "FAS-123"
- Create feature branch: git checkout -b feature/fas-123-description
- Update issue status: mcp__linear__updateIssue --id "FAS-123" --stateId "[in-progress-id]"

# Complete feature
- Run tests: npm run lint && npm run typecheck
- Commit changes: git add . && git commit -m "feat: description"
- Push branch: git push origin feature/fas-123-description
- Create PR: gh pr create --base main --title "feat: FAS-123 description"
```

## Database Operations

```bash
# Create migration
supabase migration new descriptive_name
supabase db reset  # Test locally

# Deploy migrations
mcp__supabase__list_migrations --project_id qnfmiotatmkoumlymynq  # Check staging
mcp__supabase__apply_migration --project_id qnfmiotatmkoumlymynq --name "migration_name" --query "[SQL]"

# Debug database
mcp__supabase__list_tables --project_id [id]
mcp__supabase__execute_sql --project_id [id] --query "SELECT * FROM table LIMIT 10"
```

## Deployment

```bash
# Deploy to staging (auto on push to main)
git push origin main

# Deploy to production
gh pr create --base production --head main --title "Deploy: description"
# After merge, production auto-deploys

# Check deployment logs
mcp__supabase__get_logs --project_id [id] --service "api"
```

## Debugging

```bash
# Check environment
npm run dev:check
npm run env:check
npm run status

# Check Supabase errors
mcp__supabase__get_advisors --project_id [id] --type "security"
mcp__supabase__get_logs --project_id [id] --service "postgres"

# Search documentation
mcp__supabase__search_docs --graphql_query 'query { searchDocs(query: "RLS policies") { nodes { title href } } }'
```

## Common NPM Scripts

```bash
npm run dev                  # Start development server
npm run build               # Build for production
npm run test                # Run tests
npm run lint                # Run linter
npm run typecheck           # Check TypeScript
npm run supabase:start      # Start local Supabase
npm run supabase:reset      # Reset local database
```