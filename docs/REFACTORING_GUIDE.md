# Image Generator Refactoring Guide

## Overview

The Fashion Lab V2 API components have been refactored following KISS principles to improve maintainability, testability, and readability.

## Key Changes

### 1. Component Breakdown

**Before**: Single 1777-line `ImageGeneratorDemo.tsx` component
**After**: 7 focused components with clear responsibilities

- `ImageGenerator<PERSON>ontainer` - Main coordinator (300 lines)
- `V2ImageUploader` - 4-image upload handling (150 lines)
- `PromptBuilder` - Visual prompt construction (250 lines)
- `GeneratedImageGrid` - Image display grid (150 lines)
- `ImageSelectionPanel` - Model/angle/garment selection (200 lines)
- `TechnicalSettings` - Generation parameters (150 lines)
- `ImageDetailModal` - Single image view (100 lines)

### 2. Unified API Service

**Before**: Separate hooks for V1 and V2 APIs
```typescript
// Old approach
const { generateImages } = useImageGeneration(); // V1
const { generate } = useFashionLabImages(); // V2
```

**After**: Single unified service
```typescript
// New approach
const result = await UnifiedImageGenerationService.generateImages(params);
// Automatically detects V1 vs V2 based on parameters
```

### 3. State Management

**Before**: 20+ individual useState calls
```typescript
const [selectedModels, setSelectedModels] = useState<string[]>([]);
const [selectedAngles, setSelectedAngles] = useState<number[]>([]);
const [selectedGarments, setSelectedGarments] = useState<string[]>([]);
// ... many more
```

**After**: Single useReducer with clear actions
```typescript
const [state, dispatch] = useReducer(generatorReducer, initialState);
dispatch({ type: 'SET_MODELS', payload: ['S', 'M'] });
```

### 4. Configuration Extraction

**Before**: Hardcoded values throughout component
**After**: Centralized configuration
```typescript
import { CAMPAIGN_MODELS, ANGLE_BLOCKS } from '../../config/imageGeneration.config';
```

## Migration Steps

### Step 1: Update Imports

```typescript
// Old
import ImageGeneratorDemo from './pages/demo/ImageGeneratorDemo';

// New
import { ImageGeneratorContainer } from './components/image-generation';
```

### Step 2: Replace Component Usage

```typescript
// Old
<ImageGeneratorDemo />

// New
<ImageGeneratorContainer />
```

### Step 3: Update API Calls (if using programmatically)

```typescript
// Old - separate API calls
if (hasV2Images) {
  await generateV2({ ... });
} else {
  await apiGenerateImages({ ... });
}

// New - unified API
await UnifiedImageGenerationService.generateImages({
  prompt,
  collectionId,
  // V1 params (optional)
  modelId,
  loraName,
  // V2 params (optional)
  faceImage,
  image2,
  image3,
  image4,
});
```

## Component Usage Examples

### Using Individual Components

```typescript
import { V2ImageUploader, PromptBuilder } from '@/components/image-generation';

function MyCustomGenerator() {
  const [v2Images, setV2Images] = useState<V2Images>({});
  const [prompt, setPrompt] = useState('');
  
  return (
    <>
      <V2ImageUploader 
        images={v2Images}
        onImagesChange={setV2Images}
      />
      
      <PromptBuilder
        prompt={prompt}
        onPromptChange={setPrompt}
        activeBlocks={[]}
        onActiveBlocksChange={() => {}}
      />
    </>
  );
}
```

### Customizing Configuration

```typescript
// Create your own config
export const CUSTOM_MODELS = [
  {
    id: 'custom-1',
    name: 'Custom Model',
    shortName: 'CM1',
    promptText: 'custom model description',
    lora: 'custom_lora_v1',
    image: '/path/to/image.jpg'
  }
];

// Use in component
<ImageSelectionPanel models={CUSTOM_MODELS} />
```

## Benefits

1. **Better Testing**: Each component can be tested in isolation
2. **Easier Maintenance**: Find and fix issues in focused components
3. **Reusability**: Use components in other parts of the application
4. **Performance**: Components only re-render when their props change
5. **Type Safety**: Better TypeScript support with smaller interfaces

## Rollback Plan

If you need to rollback:
1. The original `ImageGeneratorDemo.tsx` is unchanged
2. Simply switch imports back to the original component
3. No database or API changes required

## Future Improvements

1. Add unit tests for each component
2. Create Storybook stories for visual testing
3. Add error boundaries around key components
4. Implement lazy loading for better performance
5. Add analytics tracking for usage patterns