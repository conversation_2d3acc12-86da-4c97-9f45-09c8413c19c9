# Getting Started

## Prerequisites

- Node.js 18+
- Git
- Supabase CLI: `npm install -g supabase`

## Setup

```bash
# 1. Clone and install
git clone [repo]
cd fashionlab-v1
npm install

# 2. Configure environment
cp .env.example .env.local
# Edit .env.local with your values

# 3. Start development
npm run supabase:start    # Start local database
npm run dev              # Start dev server (http://localhost:8080)
```

## Quick Checks

```bash
npm run dev:check        # Check if everything is set up
npm run env:check        # Verify environment variables
npm run status           # Show all environments status
```

## Common Issues

**Port conflicts**: Kill existing processes on ports 8080, 54321  
**Database errors**: Run `npm run supabase:reset`  
**Environment issues**: Check `.env.local` matches `.env.example`

## Scripts Reference

See `package.json` for all available scripts or run:
```bash
npm run
```