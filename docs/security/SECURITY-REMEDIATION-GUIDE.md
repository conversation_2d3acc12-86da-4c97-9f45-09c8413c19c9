# FashionLab Security Remediation Guide

## Executive Summary

This guide provides comprehensive remediation strategies for security vulnerabilities identified in the FashionLab platform. Each vulnerability includes immediate fixes, long-term solutions, and defense-in-depth measures.

## Critical Vulnerabilities and Remediations

### 1. Hardcoded Test Credentials

**Severity**: Critical  
**Location**: `/src/pages/Login.tsx`  
**Issue**: Test credentials are exposed in production code

#### Immediate Fix

```typescript
// src/pages/Login.tsx
// REMOVE lines 63-71 containing test credentials
// Replace with environment-based solution:

const getTestCredentials = () => {
  if (process.env.NODE_ENV !== 'development' || !process.env.VITE_ENABLE_TEST_CREDENTIALS) {
    return [];
  }
  
  // Load from environment or config file, never hardcode
  return JSON.parse(process.env.VITE_TEST_CREDENTIALS || '[]');
};
```

#### Long-term Solution

Create a secure test data management system:

```typescript
// src/utils/testDataManager.ts
export class TestDataManager {
  private static instance: TestDataManager;
  
  private constructor() {}
  
  static getInstance(): TestDataManager {
    if (!TestDataManager.instance) {
      TestDataManager.instance = new TestDataManager();
    }
    return TestDataManager.instance;
  }
  
  async getTestCredentials(): Promise<TestCredential[]> {
    // Only in development with explicit flag
    if (process.env.NODE_ENV !== 'development') {
      return [];
    }
    
    // Fetch from secure backend or encrypted config
    const response = await fetch('/api/dev/test-credentials', {
      headers: {
        'X-Dev-Token': process.env.VITE_DEV_ACCESS_TOKEN || ''
      }
    });
    
    if (!response.ok) return [];
    
    return response.json();
  }
}
```

#### Defense in Depth

1. **Environment Detection Service**:
```typescript
// src/services/environmentService.ts
export class EnvironmentService {
  static isProduction(): boolean {
    return process.env.NODE_ENV === 'production' ||
           window.location.hostname.includes('fashionlab.tech');
  }
  
  static canShowTestFeatures(): boolean {
    return !this.isProduction() && 
           process.env.VITE_ENABLE_DEV_FEATURES === 'true';
  }
}
```

2. **Build-time Stripping**:
```javascript
// vite.config.ts
export default defineConfig({
  plugins: [
    {
      name: 'strip-test-code',
      transform(code, id) {
        if (process.env.NODE_ENV === 'production') {
          // Remove test credential blocks
          return code.replace(/\/\/ TEST-CREDENTIALS-START[\s\S]*?\/\/ TEST-CREDENTIALS-END/g, '');
        }
        return code;
      }
    }
  ]
});
```

### 2. IDOR (Insecure Direct Object Reference) Vulnerabilities

**Severity**: High  
**Locations**: Multiple endpoints accessing collections, assets, and organizations  
**Issue**: Direct object IDs used without proper authorization checks

#### Immediate Fix

Add authorization middleware to all resource access:

```typescript
// src/hooks/useAuthorizedResource.ts
export function useAuthorizedResource<T>(
  resourceType: 'collection' | 'asset' | 'organization',
  resourceId: string,
  fetchFn: (id: string) => Promise<T>
) {
  const { user } = useSupabase();
  const { userRole } = useUserRole();
  
  const fetchWithAuth = async () => {
    // Check if user has access to parent organization first
    const hasAccess = await checkResourceAccess(user.id, resourceType, resourceId);
    
    if (!hasAccess) {
      throw new Error('Unauthorized access');
    }
    
    return fetchFn(resourceId);
  };
  
  return useQuery({
    queryKey: [resourceType, resourceId, user.id],
    queryFn: fetchWithAuth,
    enabled: !!user && !!resourceId
  });
}
```

#### Long-term Solution

Implement indirect object references with session-based mapping:

```typescript
// src/services/secureIdService.ts
export class SecureIdService {
  private static SESSION_KEY = 'secure_id_mapping';
  
  static generateSecureId(realId: string, userId: string): string {
    const mapping = this.getMapping();
    const secureId = crypto.randomUUID();
    
    mapping[secureId] = {
      realId,
      userId,
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 3600000).toISOString() // 1 hour
    };
    
    sessionStorage.setItem(this.SESSION_KEY, JSON.stringify(mapping));
    return secureId;
  }
  
  static resolveSecureId(secureId: string, userId: string): string | null {
    const mapping = this.getMapping();
    const entry = mapping[secureId];
    
    if (!entry || entry.userId !== userId) {
      return null;
    }
    
    if (new Date(entry.expiresAt) < new Date()) {
      delete mapping[secureId];
      sessionStorage.setItem(this.SESSION_KEY, JSON.stringify(mapping));
      return null;
    }
    
    return entry.realId;
  }
  
  private static getMapping(): Record<string, any> {
    const stored = sessionStorage.getItem(this.SESSION_KEY);
    return stored ? JSON.parse(stored) : {};
  }
}
```

#### Database-Level Protection

```sql
-- Add RLS policies with organization context
CREATE POLICY "assets_select_policy" ON assets
FOR SELECT USING (
  collection_id IN (
    SELECT c.id FROM collections c
    INNER JOIN organization_memberships om ON c.organization_id = om.organization_id
    WHERE om.user_id = auth.uid()
  )
);

-- Add audit triggers
CREATE FUNCTION log_unauthorized_access() RETURNS trigger AS $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = NEW.organization_id
  ) THEN
    INSERT INTO security_activity (user_id, event_type, event_data)
    VALUES (auth.uid(), 'unauthorized_access_attempt', 
            jsonb_build_object('resource_type', TG_TABLE_NAME, 'resource_id', NEW.id));
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### 3. CORS Misconfiguration

**Severity**: High  
**Location**: `/supabase/functions/_shared/cors.ts`  
**Issue**: Wildcard origin allows any domain

#### Immediate Fix

```typescript
// supabase/functions/_shared/cors.ts
const ALLOWED_ORIGINS = [
  'https://app.fashionlab.tech',
  'https://staging.fashionlab.tech',
  process.env.NODE_ENV === 'development' ? 'http://localhost:8080' : ''
].filter(Boolean);

export function getCorsHeaders(origin: string | null): Record<string, string> {
  const headers: Record<string, string> = {
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Max-Age': '86400',
  };
  
  if (origin && ALLOWED_ORIGINS.includes(origin)) {
    headers['Access-Control-Allow-Origin'] = origin;
    headers['Access-Control-Allow-Credentials'] = 'true';
  }
  
  return headers;
}
```

#### Long-term Solution

Implement dynamic CORS configuration:

```typescript
// supabase/functions/_shared/corsConfig.ts
export class CorsConfig {
  private static allowedOrigins: Set<string>;
  
  static async initialize() {
    // Load from database or environment
    const { data } = await supabase
      .from('cors_configurations')
      .select('origin, enabled')
      .eq('enabled', true);
    
    this.allowedOrigins = new Set(data?.map(d => d.origin) || []);
  }
  
  static validateOrigin(origin: string | null): boolean {
    if (!origin) return false;
    
    // Check exact match
    if (this.allowedOrigins.has(origin)) return true;
    
    // Check wildcard patterns
    for (const allowed of this.allowedOrigins) {
      if (allowed.includes('*')) {
        const pattern = allowed.replace(/\*/g, '.*');
        if (new RegExp(`^${pattern}$`).test(origin)) {
          return true;
        }
      }
    }
    
    return false;
  }
}
```

### 4. Missing Rate Limiting

**Severity**: High  
**Locations**: All API endpoints  
**Issue**: No rate limiting protection

#### Immediate Fix

Add rate limiting middleware:

```typescript
// src/middleware/rateLimiter.ts
import { RateLimiterMemory } from 'rate-limiter-flexible';

const rateLimiters = {
  auth: new RateLimiterMemory({
    points: 5, // 5 attempts
    duration: 900, // per 15 minutes
    blockDuration: 900, // block for 15 minutes
  }),
  api: new RateLimiterMemory({
    points: 100, // 100 requests
    duration: 60, // per minute
  }),
  upload: new RateLimiterMemory({
    points: 10, // 10 uploads
    duration: 3600, // per hour
  })
};

export async function checkRateLimit(
  type: 'auth' | 'api' | 'upload',
  identifier: string
): Promise<{ allowed: boolean; retryAfter?: number }> {
  try {
    await rateLimiters[type].consume(identifier);
    return { allowed: true };
  } catch (rejRes) {
    return {
      allowed: false,
      retryAfter: Math.round(rejRes.msBeforeNext / 1000) || 60
    };
  }
}
```

#### Long-term Solution

Implement distributed rate limiting with Redis:

```typescript
// src/services/distributedRateLimiter.ts
import Redis from 'ioredis';

export class DistributedRateLimiter {
  private redis: Redis;
  
  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
    });
  }
  
  async checkLimit(
    key: string,
    limit: number,
    window: number
  ): Promise<{ allowed: boolean; remaining: number; reset: number }> {
    const now = Date.now();
    const windowStart = now - window * 1000;
    
    const pipeline = this.redis.pipeline();
    pipeline.zremrangebyscore(key, '-inf', windowStart);
    pipeline.zadd(key, now, `${now}-${Math.random()}`);
    pipeline.zcard(key);
    pipeline.expire(key, window + 1);
    
    const results = await pipeline.exec();
    const count = results?.[2]?.[1] as number || 0;
    
    return {
      allowed: count <= limit,
      remaining: Math.max(0, limit - count),
      reset: Math.ceil((windowStart + window * 1000) / 1000)
    };
  }
}
```

### 5. Weak Password Policy

**Severity**: Medium  
**Location**: Password validation logic  
**Issue**: Minimum 8 characters only

#### Immediate Fix

```typescript
// src/utils/passwordValidation.ts
export interface PasswordStrength {
  score: number;
  feedback: string[];
  isValid: boolean;
}

export function validatePassword(password: string): PasswordStrength {
  const feedback: string[] = [];
  let score = 0;
  
  // Length check
  if (password.length >= 12) score += 2;
  else if (password.length >= 10) score += 1;
  else feedback.push('Password should be at least 10 characters');
  
  // Complexity checks
  if (/[a-z]/.test(password)) score += 1;
  else feedback.push('Include lowercase letters');
  
  if (/[A-Z]/.test(password)) score += 1;
  else feedback.push('Include uppercase letters');
  
  if (/[0-9]/.test(password)) score += 1;
  else feedback.push('Include numbers');
  
  if (/[^a-zA-Z0-9]/.test(password)) score += 1;
  else feedback.push('Include special characters');
  
  // Common patterns check
  if (!/(.)\1{2,}/.test(password)) score += 1;
  else feedback.push('Avoid repeated characters');
  
  // Dictionary check (basic)
  const commonPasswords = ['password', '123456', 'admin', 'letmein'];
  if (!commonPasswords.some(common => password.toLowerCase().includes(common))) {
    score += 1;
  } else {
    feedback.push('Avoid common passwords');
  }
  
  return {
    score,
    feedback,
    isValid: score >= 6 && password.length >= 10
  };
}
```

#### Long-term Solution

Integrate with zxcvbn or similar library:

```typescript
// src/services/advancedPasswordValidation.ts
import zxcvbn from 'zxcvbn';

export class PasswordPolicyService {
  private static minScore = 3; // 0-4 scale
  private static minLength = 10;
  private static maxLength = 128;
  
  static validate(
    password: string,
    userInputs: string[] = []
  ): { valid: boolean; errors: string[]; suggestions: string[] } {
    const errors: string[] = [];
    const suggestions: string[] = [];
    
    // Length validation
    if (password.length < this.minLength) {
      errors.push(`Password must be at least ${this.minLength} characters`);
    }
    
    if (password.length > this.maxLength) {
      errors.push(`Password must not exceed ${this.maxLength} characters`);
    }
    
    // Strength analysis
    const result = zxcvbn(password, userInputs);
    
    if (result.score < this.minScore) {
      errors.push('Password is too weak');
      suggestions.push(...(result.feedback.suggestions || []));
    }
    
    // Check against breach database
    const isBreached = await this.checkBreachedPassword(password);
    if (isBreached) {
      errors.push('This password has been found in data breaches');
    }
    
    return {
      valid: errors.length === 0,
      errors,
      suggestions
    };
  }
  
  private static async checkBreachedPassword(password: string): Promise<boolean> {
    // Implement k-anonymity check with HaveIBeenPwned API
    const hash = await this.sha1(password);
    const prefix = hash.substring(0, 5);
    const suffix = hash.substring(5);
    
    const response = await fetch(`https://api.pwnedpasswords.com/range/${prefix}`);
    const text = await response.text();
    
    return text.includes(suffix.toUpperCase());
  }
  
  private static async sha1(message: string): Promise<string> {
    const msgBuffer = new TextEncoder().encode(message);
    const hashBuffer = await crypto.subtle.digest('SHA-1', msgBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }
}
```

### 6. Path Traversal Vulnerabilities

**Severity**: Medium  
**Locations**: File upload and storage operations  
**Issue**: Insufficient path validation

#### Immediate Fix

```typescript
// src/utils/pathSanitization.ts
export function sanitizePath(userInput: string): string {
  // Remove any path traversal attempts
  let sanitized = userInput
    .replace(/\.\./g, '')
    .replace(/\/\//g, '/')
    .replace(/\\/g, '/')
    .replace(/^\//, '');
  
  // Remove any special characters that could be problematic
  sanitized = sanitized.replace(/[^a-zA-Z0-9._\-\/]/g, '_');
  
  // Ensure no hidden files
  if (sanitized.startsWith('.')) {
    sanitized = sanitized.substring(1);
  }
  
  return sanitized;
}

export function validateFilePath(path: string, allowedBasePath: string): boolean {
  const normalizedPath = path.normalize(path);
  const normalizedBase = path.normalize(allowedBasePath);
  
  // Ensure the path doesn't escape the base directory
  return normalizedPath.startsWith(normalizedBase);
}
```

#### Long-term Solution

```typescript
// src/services/secureFileService.ts
import * as path from 'path';
import { createHash } from 'crypto';

export class SecureFileService {
  private static ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.pdf'];
  private static MAX_PATH_LENGTH = 255;
  
  static generateSecurePath(
    organizationId: string,
    collectionId: string,
    fileName: string
  ): string {
    // Validate inputs
    if (!this.isValidUUID(organizationId) || !this.isValidUUID(collectionId)) {
      throw new Error('Invalid UUID provided');
    }
    
    // Sanitize filename
    const ext = path.extname(fileName).toLowerCase();
    if (!this.ALLOWED_EXTENSIONS.includes(ext)) {
      throw new Error(`File extension ${ext} not allowed`);
    }
    
    // Generate secure filename
    const timestamp = Date.now();
    const randomBytes = crypto.randomBytes(8).toString('hex');
    const hash = createHash('sha256')
      .update(`${organizationId}-${collectionId}-${timestamp}-${randomBytes}`)
      .digest('hex')
      .substring(0, 16);
    
    const secureName = `${timestamp}_${hash}${ext}`;
    
    // Build path with validated components
    const securePath = path.join(
      'organizations',
      organizationId,
      'collections',
      collectionId,
      secureName
    );
    
    if (securePath.length > this.MAX_PATH_LENGTH) {
      throw new Error('Generated path exceeds maximum length');
    }
    
    return securePath;
  }
  
  private static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }
}
```

### 7. SQL Injection Prevention

**Severity**: High  
**Locations**: Database queries throughout the application  
**Issue**: Potential for SQL injection if raw queries are used

#### Immediate Fix

Ensure all Supabase queries use the query builder:

```typescript
// src/hooks/useSecureQuery.ts
export function useSecureQuery<T>(
  table: string,
  filters: Record<string, any>
) {
  const { supabase } = useSupabase();
  
  const buildQuery = () => {
    let query = supabase.from(table).select();
    
    // Safe filtering using Supabase query builder
    Object.entries(filters).forEach(([key, value]) => {
      // Validate column names against whitelist
      if (!isValidColumn(table, key)) {
        throw new Error(`Invalid column: ${key}`);
      }
      
      if (Array.isArray(value)) {
        query = query.in(key, value);
      } else if (value === null) {
        query = query.is(key, null);
      } else {
        query = query.eq(key, value);
      }
    });
    
    return query;
  };
  
  return useQuery({
    queryKey: [table, filters],
    queryFn: async () => {
      const { data, error } = await buildQuery();
      if (error) throw error;
      return data as T[];
    }
  });
}
```

#### Long-term Solution

Create a query validation layer:

```typescript
// src/services/queryValidator.ts
export class QueryValidator {
  private static columnWhitelist: Map<string, Set<string>> = new Map([
    ['assets', new Set(['id', 'collection_id', 'workflow_stage', 'created_at'])],
    ['collections', new Set(['id', 'organization_id', 'name', 'status'])],
    // Add all tables and their allowed columns
  ]);
  
  static validateQuery(
    table: string,
    operation: 'select' | 'insert' | 'update' | 'delete',
    data: Record<string, any>
  ): void {
    const allowedColumns = this.columnWhitelist.get(table);
    if (!allowedColumns) {
      throw new Error(`Table ${table} not whitelisted`);
    }
    
    Object.keys(data).forEach(column => {
      if (!allowedColumns.has(column)) {
        throw new Error(`Column ${column} not allowed for table ${table}`);
      }
      
      // Validate data types
      this.validateDataType(table, column, data[column]);
    });
  }
  
  private static validateDataType(
    table: string,
    column: string,
    value: any
  ): void {
    // Implement type validation based on schema
    const expectedType = this.getExpectedType(table, column);
    
    switch (expectedType) {
      case 'uuid':
        if (!this.isValidUUID(value)) {
          throw new Error(`Invalid UUID for ${column}`);
        }
        break;
      case 'string':
        if (typeof value !== 'string' || value.length > 255) {
          throw new Error(`Invalid string for ${column}`);
        }
        break;
      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          throw new Error(`Invalid number for ${column}`);
        }
        break;
      // Add more type validations
    }
  }
  
  private static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }
  
  private static getExpectedType(table: string, column: string): string {
    // Return expected type from schema definition
    // This should be loaded from your database schema
    const schema: Record<string, Record<string, string>> = {
      assets: {
        id: 'uuid',
        collection_id: 'uuid',
        workflow_stage: 'string',
        file_size: 'number'
      },
      // Add more schema definitions
    };
    
    return schema[table]?.[column] || 'string';
  }
}
```

### 8. Session Management

**Severity**: Medium  
**Issue**: Session fixation and improper session handling

#### Immediate Fix

```typescript
// src/services/sessionManager.ts
export class SessionManager {
  private static SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  private static ACTIVITY_CHECK_INTERVAL = 60 * 1000; // 1 minute
  
  static initializeSession(userId: string): void {
    // Generate new session ID on login
    const sessionId = crypto.randomUUID();
    
    sessionStorage.setItem('session_id', sessionId);
    sessionStorage.setItem('session_started', Date.now().toString());
    sessionStorage.setItem('last_activity', Date.now().toString());
    
    // Start activity monitoring
    this.startActivityMonitoring();
  }
  
  static validateSession(): boolean {
    const sessionId = sessionStorage.getItem('session_id');
    const lastActivity = sessionStorage.getItem('last_activity');
    
    if (!sessionId || !lastActivity) {
      return false;
    }
    
    const timeSinceActivity = Date.now() - parseInt(lastActivity);
    if (timeSinceActivity > this.SESSION_TIMEOUT) {
      this.clearSession();
      return false;
    }
    
    return true;
  }
  
  static updateActivity(): void {
    sessionStorage.setItem('last_activity', Date.now().toString());
  }
  
  static clearSession(): void {
    sessionStorage.clear();
    // Also clear any secure cookies
    document.cookie.split(';').forEach(cookie => {
      const eqPos = cookie.indexOf('=');
      const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;secure;samesite=strict`;
    });
  }
  
  private static startActivityMonitoring(): void {
    setInterval(() => {
      if (!this.validateSession()) {
        window.location.href = '/login?reason=session_expired';
      }
    }, this.ACTIVITY_CHECK_INTERVAL);
    
    // Update activity on user interaction
    ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
      document.addEventListener(event, () => this.updateActivity(), { passive: true });
    });
  }
}
```

### 9. File Upload Security

**Severity**: High  
**Location**: File upload handlers  
**Issue**: Insufficient file validation

#### Immediate Fix

```typescript
// src/services/fileUploadValidator.ts
export class FileUploadValidator {
  private static ALLOWED_MIME_TYPES = [
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/gif',
    'application/pdf'
  ];
  
  private static MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  private static MAGIC_NUMBERS = {
    'image/jpeg': [0xFF, 0xD8, 0xFF],
    'image/png': [0x89, 0x50, 0x4E, 0x47],
    'image/gif': [0x47, 0x49, 0x46],
    'image/webp': [0x52, 0x49, 0x46, 0x46],
    'application/pdf': [0x25, 0x50, 0x44, 0x46]
  };
  
  static async validateFile(file: File): Promise<{
    valid: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];
    
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      errors.push(`File size exceeds ${this.MAX_FILE_SIZE / 1024 / 1024}MB limit`);
    }
    
    // Check MIME type
    if (!this.ALLOWED_MIME_TYPES.includes(file.type)) {
      errors.push(`File type ${file.type} not allowed`);
    }
    
    // Verify magic numbers
    const magicValid = await this.verifyMagicNumbers(file);
    if (!magicValid) {
      errors.push('File content does not match declared type');
    }
    
    // Check for malicious content
    const isSafe = await this.scanForMaliciousContent(file);
    if (!isSafe) {
      errors.push('File contains potentially malicious content');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  private static async verifyMagicNumbers(file: File): Promise<boolean> {
    const bytes = new Uint8Array(await file.slice(0, 8).arrayBuffer());
    const expectedMagic = this.MAGIC_NUMBERS[file.type as keyof typeof this.MAGIC_NUMBERS];
    
    if (!expectedMagic) return false;
    
    return expectedMagic.every((byte, index) => bytes[index] === byte);
  }
  
  private static async scanForMaliciousContent(file: File): Promise<boolean> {
    // Check for embedded scripts in images
    if (file.type.startsWith('image/')) {
      const text = await file.text();
      const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /onclick=/i,
        /onerror=/i,
        /<iframe/i
      ];
      
      return !suspiciousPatterns.some(pattern => pattern.test(text));
    }
    
    return true;
  }
}
```

#### Long-term Solution

Implement server-side scanning with ClamAV:

```typescript
// src/services/antivirusScanner.ts
export class AntivirusScanner {
  private static CLAMAV_ENDPOINT = process.env.CLAMAV_ENDPOINT;
  
  static async scanFile(file: File): Promise<{
    clean: boolean;
    threats: string[];
  }> {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
      const response = await fetch(`${this.CLAMAV_ENDPOINT}/scan`, {
        method: 'POST',
        body: formData
      });
      
      const result = await response.json();
      
      return {
        clean: result.status === 'clean',
        threats: result.threats || []
      };
    } catch (error) {
      // If scanner is unavailable, be conservative
      console.error('Antivirus scan failed:', error);
      return {
        clean: false,
        threats: ['Scanner unavailable']
      };
    }
  }
}
```

### 10. Horizontal Privilege Escalation Prevention

**Severity**: High  
**Issue**: Users might access resources from other organizations

#### Immediate Fix

```typescript
// src/hooks/useOrganizationGuard.ts
export function useOrganizationGuard() {
  const { user } = useSupabase();
  const { organizationId } = useOrganizationContext();
  
  const checkAccess = async (resourceOrgId: string): Promise<boolean> => {
    if (!user || !resourceOrgId) return false;
    
    // Platform admins have access to all
    const { data: profile } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profile?.role === 'platform_admin' || profile?.role === 'platform_super') {
      return true;
    }
    
    // Check organization membership
    const { data: membership } = await supabase
      .from('organization_memberships')
      .select('id')
      .eq('user_id', user.id)
      .eq('organization_id', resourceOrgId)
      .single();
    
    return !!membership;
  };
  
  const guardedFetch = async <T>(
    fetchFn: () => Promise<T>,
    resourceOrgId: string
  ): Promise<T> => {
    const hasAccess = await checkAccess(resourceOrgId);
    
    if (!hasAccess) {
      // Log the attempt
      await supabase.from('security_activity').insert({
        user_id: user.id,
        event_type: 'unauthorized_org_access',
        event_data: {
          attempted_org_id: resourceOrgId,
          current_org_id: organizationId
        }
      });
      
      throw new Error('Access denied to organization resources');
    }
    
    return fetchFn();
  };
  
  return { checkAccess, guardedFetch };
}
```

## Implementation Priority

1. **Critical (Implement Immediately)**
   - Remove hardcoded test credentials
   - Fix CORS configuration
   - Implement basic rate limiting

2. **High (Within 1 Week)**
   - Add IDOR protection with authorization checks
   - Implement file upload validation
   - Strengthen password policy

3. **Medium (Within 1 Month)**
   - Deploy distributed rate limiting
   - Implement advanced session management
   - Add comprehensive SQL injection prevention

4. **Long-term (Within 3 Months)**
   - Deploy antivirus scanning
   - Implement full indirect object reference system
   - Add comprehensive security monitoring

## Testing Strategies

### Unit Tests

```typescript
// src/tests/security/passwordValidation.test.ts
describe('Password Validation', () => {
  it('should reject weak passwords', () => {
    const weak = ['password', '12345678', 'abcdefgh'];
    weak.forEach(pwd => {
      const result = validatePassword(pwd);
      expect(result.isValid).toBe(false);
    });
  });
  
  it('should accept strong passwords', () => {
    const strong = ['Str0ng!Pass123', 'C0mpl3x#P@ssw0rd'];
    strong.forEach(pwd => {
      const result = validatePassword(pwd);
      expect(result.isValid).toBe(true);
    });
  });
});
```

### Integration Tests

```typescript
// src/tests/security/authorization.test.ts
describe('Authorization Tests', () => {
  it('should prevent cross-organization access', async () => {
    const user1 = await createTestUser('org1');
    const user2 = await createTestUser('org2');
    const asset = await createTestAsset('org1');
    
    // User2 should not access org1 asset
    await expect(
      fetchAsset(asset.id, user2.token)
    ).rejects.toThrow('Unauthorized');
  });
});
```

## Monitoring and Alerting

```typescript
// src/services/securityMonitor.ts
export class SecurityMonitor {
  static async logSecurityEvent(
    userId: string,
    eventType: string,
    details: Record<string, any>
  ): Promise<void> {
    await supabase.from('security_activity').insert({
      user_id: userId,
      event_type: eventType,
      event_data: details,
      ip_address: await this.getClientIp(),
      user_agent: navigator.userAgent
    });
    
    // Alert on critical events
    if (this.isCriticalEvent(eventType)) {
      await this.sendSecurityAlert(userId, eventType, details);
    }
  }
  
  private static isCriticalEvent(eventType: string): boolean {
    const criticalEvents = [
      'unauthorized_access_attempt',
      'sql_injection_attempt',
      'rate_limit_exceeded',
      'malicious_file_upload'
    ];
    
    return criticalEvents.includes(eventType);
  }
}
```

## Regression Prevention

1. **Automated Security Scanning**: Integrate SAST tools in CI/CD
2. **Pre-commit Hooks**: Check for hardcoded secrets
3. **Code Review Checklist**: Security-focused review process
4. **Regular Penetration Testing**: Quarterly security assessments
5. **Security Training**: Developer security awareness program

## Conclusion

This remediation guide provides comprehensive strategies to address all identified security vulnerabilities. Implement fixes based on severity and maintain ongoing security practices to prevent regression.