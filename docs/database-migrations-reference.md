# Database Migrations Reference

Advanced migration patterns and troubleshooting guide.

## Migration Workflow

### 1. Solo Developer Workflow
```bash
# Create migration
supabase migration new feature_name

# Test locally
supabase db reset

# Deploy to staging
supabase db push --linked

# Deploy to production (after PR merge)
npm run migrate:production
```

### 2. Team Workflow
When multiple developers work on migrations:
1. Always pull latest before creating new migrations
2. Check for conflicts in migration timestamps
3. Test combined migrations locally before pushing

## Advanced Migration Patterns

### Detecting Breaking Changes
```sql
-- In your migration, add warnings
DO $$
BEGIN
  RAISE WARNING 'BREAKING CHANGE: This migration removes column X from table Y';
END $$;
```

### Safe Column Renaming
```sql
-- Step 1: Add new column
ALTER TABLE users ADD COLUMN display_name TEXT;

-- Step 2: Copy data
UPDATE users SET display_name = full_name;

-- Step 3: In next release, drop old column
-- ALTER TABLE users DROP COLUMN full_name;
```

### Complex RLS Policy Updates
```sql
-- Temporarily disable RLS during migration
ALTER TABLE assets DISABLE ROW LEVEL SECURITY;

-- Update policies
DROP POLICY IF EXISTS "old_policy" ON assets;
CREATE POLICY "new_policy" ON assets ...;

-- Re-enable RLS
ALTER TABLE assets ENABLE ROW LEVEL SECURITY;
```

## Rollback Strategies

### 1. Pre-emptive Rollback Plan
Always include rollback SQL in migration comments:
```sql
-- Migration: Add user preferences
-- Rollback: DROP TABLE user_preferences CASCADE;

CREATE TABLE user_preferences (...);
```

### 2. Emergency Rollback
```bash
# Connect to affected environment
psql $DATABASE_URL

# Run rollback SQL manually
BEGIN;
-- Your rollback SQL here
COMMIT;
```

### 3. Point-in-Time Recovery
For critical failures, use Supabase dashboard:
- Go to Settings > Database
- Use Point-in-Time Recovery
- Select timestamp before bad migration

## Migration Gotchas

### 1. RLS Default Permissions
New tables have no access by default:
```sql
-- Always add at least one policy
CREATE POLICY "Enable read access for authenticated users" 
ON new_table FOR SELECT 
USING (auth.role() = 'authenticated');
```

### 2. Foreign Key Constraints
Order matters with foreign keys:
```sql
-- Create referenced table first
CREATE TABLE organizations (...);

-- Then create referencing table
CREATE TABLE users (
  organization_id UUID REFERENCES organizations(id)
);
```

### 3. Enum Type Changes
PostgreSQL enums are immutable:
```sql
-- Can't modify enum, must recreate
ALTER TYPE old_enum RENAME TO old_enum_backup;
CREATE TYPE old_enum AS ENUM ('value1', 'value2', 'new_value');

-- Migrate data...
DROP TYPE old_enum_backup;
```

## Testing Migrations

### Local Testing Checklist
- [ ] Run `supabase db reset`
- [ ] Check all tables created/modified
- [ ] Verify RLS policies work
- [ ] Test with different user roles
- [ ] Check performance on large datasets

### Staging Testing
- [ ] Apply migration
- [ ] Run E2E tests
- [ ] Check application functionality
- [ ] Monitor error logs
- [ ] Verify no breaking changes

## Emergency Procedures

### Failed Migration on Staging/Production
1. **Don't panic** - Data is usually safe
2. **Check logs** - `supabase db migrate list`
3. **Manual fix** - Connect via psql and fix
4. **Mark as complete** - Update migration history
5. **Document** - Add fix to next migration

### Locked Database
```sql
-- Find blocking queries
SELECT pid, query, state 
FROM pg_stat_activity 
WHERE state != 'idle';

-- Kill blocking query
SELECT pg_terminate_backend(pid);
```