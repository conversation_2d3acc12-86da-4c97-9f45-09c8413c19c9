# FashionLab Database Schema Documentation

## Overview

FashionLab is a multi-tenant SaaS platform for AI-powered fashion imagery management. The database uses PostgreSQL with Supabase for authentication and row-level security (RLS).

## Core Data Model

```
Organizations (Brands)
├── Collections (Campaigns/Seasons)
│   ├── Assets (Images/Files)
│   │   ├── Products (optional grouping)
│   │   ├── Comments (feedback/annotations)
│   │   └── Tags (workflow stages, categories)
│   └── Products (can group multiple assets)
└── Members (Users with specific roles)
```

## Table Definitions

### organizations
Represents brands/companies using the platform.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| name | TEXT | Organization name |
| logo_url | TEXT | Logo image URL |
| created_at | TIMESTAMPTZ | Creation timestamp |
| updated_at | TIMESTAMPTZ | Last update timestamp |
| settings | JSONB | Organization preferences |

### users
User profiles linked to Supabase Auth.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key (matches auth.users.id) |
| email | TEXT | User email |
| role | user_role | Platform-wide role |
| full_name | TEXT | Display name |
| avatar_url | TEXT | Profile picture URL |
| preferences | JSONB | User settings |
| created_at | TIMESTAMPTZ | Creation timestamp |
| updated_at | TIMESTAMPTZ | Last update timestamp |

### organization_memberships
Many-to-many relationship between users and organizations.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Foreign key to users |
| organization_id | UUID | Foreign key to organizations |
| joined_at | TIMESTAMPTZ | Membership start date |

### collections
Campaigns or seasonal groupings of assets.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| organization_id | UUID | Foreign key to organizations |
| name | TEXT | Collection name |
| description | TEXT | Collection description |
| season | TEXT | Season identifier |
| year | INTEGER | Year of collection |
| cover_image_url | TEXT | Cover image URL |
| brief_url | TEXT | Brief document URL |
| status | TEXT | Collection status |
| created_at | TIMESTAMPTZ | Creation timestamp |
| updated_at | TIMESTAMPTZ | Last update timestamp |

### assets
Images and files within collections.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| collection_id | UUID | Foreign key to collections |
| product_id | UUID | Optional foreign key to products |
| file_name | TEXT | Original filename |
| file_path | TEXT | Storage path |
| file_type | TEXT | MIME type |
| file_size | INTEGER | Size in bytes |
| width | INTEGER | Image width in pixels |
| height | INTEGER | Image height in pixels |
| workflow_stage | workflow_stage | Current stage |
| version | INTEGER | Version number |
| parent_asset_id | UUID | Link to original asset |
| metadata | JSONB | Processing metadata |
| created_at | TIMESTAMPTZ | Upload timestamp |
| updated_at | TIMESTAMPTZ | Last update timestamp |
| created_by | UUID | Uploader user ID |

### products
Optional grouping for assets within collections.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| collection_id | UUID | Foreign key to collections |
| name | TEXT | Product name |
| sku | TEXT | Product SKU |
| description | TEXT | Product description |
| metadata | JSONB | Additional product data |
| created_at | TIMESTAMPTZ | Creation timestamp |
| updated_at | TIMESTAMPTZ | Last update timestamp |

### comments
Feedback and annotations on assets.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| asset_id | UUID | Foreign key to assets |
| user_id | UUID | Foreign key to users |
| content | TEXT | Comment text |
| status | comment_status | open/resolved |
| position_x | NUMERIC | X coordinate for annotations |
| position_y | NUMERIC | Y coordinate for annotations |
| created_at | TIMESTAMPTZ | Creation timestamp |
| updated_at | TIMESTAMPTZ | Last update timestamp |

### tags
Categorization system for assets.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| name | TEXT | Tag name |
| category | tag_category | Tag type |
| color | TEXT | Display color |
| organization_id | UUID | Foreign key to organizations |
| created_at | TIMESTAMPTZ | Creation timestamp |

### asset_tags
Many-to-many relationship between assets and tags.

| Column | Type | Description |
|--------|------|-------------|
| asset_id | UUID | Foreign key to assets |
| tag_id | UUID | Foreign key to tags |
| created_at | TIMESTAMPTZ | Tag application timestamp |

### pending_invitations
Email invitations to join organizations.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| email | TEXT | Invitee email |
| organization_id | UUID | Foreign key to organizations |
| invited_by | UUID | Foreign key to users |
| token | TEXT | Unique invitation token |
| expires_at | TIMESTAMPTZ | Expiration timestamp |
| accepted | BOOLEAN | Acceptance status |
| message | TEXT | Personal message |
| created_at | TIMESTAMPTZ | Creation timestamp |

### security_activity
Audit log for security events.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Foreign key to users |
| event_type | TEXT | Event category |
| event_data | JSONB | Event details |
| ip_address | INET | Client IP |
| user_agent | TEXT | Browser info |
| created_at | TIMESTAMPTZ | Event timestamp |

## Type Definitions

### user_role
```sql
CREATE TYPE user_role AS ENUM (
    'platform_super',      -- Full system access
    'platform_admin',      -- Support access
    'brand_admin',         -- Organization admin
    'brand_member',        -- Regular member
    'external_retoucher',  -- External contractor
    'external_prompter'    -- Read-only external
);
```

### workflow_stage
```sql
CREATE TYPE workflow_stage AS ENUM (
    'upload',    -- Initial upload
    'draft',     -- AI generated draft
    'upscale',   -- Upscaled version
    'retouch',   -- Retouched version
    'final'      -- Final approved
);
```

### tag_category
```sql
CREATE TYPE tag_category AS ENUM (
    'view_type',        -- Front, back, detail
    'workflow_stage',   -- Stage tags
    'product_specific', -- Product attributes
    'custom'           -- User-defined
);
```

### comment_status
```sql
CREATE TYPE comment_status AS ENUM (
    'open',     -- Needs attention
    'resolved'  -- Completed
);
```

## Storage Buckets

| Bucket | Purpose | Access |
|--------|---------|---------|
| profiles | User avatars, org logos | Public read |
| assets | Main asset storage | Private |
| thumbnails | Asset thumbnails | Public read |
| compressed | WebP compressed versions | Public read |
| general-uploads | Briefs, covers, misc | Private |

## Architecture Layers

```
┌─────────────────────────────────────────────────────────────┐
│                     PLATFORM LEVEL                         │
│  Roles: platform_super, platform_admin                    │
│  Scope: Full platform access, manage all organizations     │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                    ORGANIZATION LEVEL                       │
│  Roles: brand_admin, brand_member                          │
│  Scope: Organization-specific data and operations          │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                   EXTERNAL LEVEL                            │
│  Roles: external_retoucher, external_prompter              │
│  Scope: Specialized access to organization assets          │
└─────────────────────────────────────────────────────────────┘
```

## Security Model

### Authentication
- Handled entirely by Supabase Auth
- Users authenticate via email/password or magic link
- Sessions managed by Supabase

### Authorization (RLS)
- All tables have RLS enabled
- Policies use auth.uid() to identify current user
- Single role system via users.role
- Multi-tenant isolation via organization_memberships

### Key Security Patterns
```sql
-- Check organization membership
auth.uid() IN (
    SELECT user_id FROM organization_memberships
    WHERE organization_id = table.organization_id
)

-- Check user role
(SELECT role FROM users WHERE id = auth.uid()) = 'brand_admin'

-- Platform user bypass
(SELECT role FROM users WHERE id = auth.uid()) 
IN ('platform_super', 'platform_admin')
```

### Permission Matrix

| Role | Organizations | Collections | Assets | Products | Comments | Members |
|------|---------------|-------------|--------|----------|----------|---------|
| `platform_super` | Full CRUD | Full CRUD | Full CRUD | Full CRUD | Full CRUD | Full CRUD |
| `platform_admin` | Full CRUD | Full CRUD | Full CRUD | Full CRUD | Full CRUD | View All |
| `brand_admin` | Update Own | Full CRUD | Full CRUD | Full CRUD | Full CRUD | Manage Own Org |
| `brand_member` | View Own | View | CRUD | CRUD | CRUD Own | View Own Org |
| `external_retoucher` | View Own | View | CRUD | View | CRUD Own | View Own Org |
| `external_prompter` | View Own | View | Read Only | View | View Only | View Own Org |

## Indexes

### Performance Indexes
- assets: collection_id, product_id, workflow_stage
- comments: asset_id, user_id
- organization_memberships: user_id, organization_id
- pending_invitations: email, token, expires_at

### Foreign Key Indexes
All foreign keys have corresponding indexes for join performance.

## Database Functions

### handle_new_user()
Triggered on auth.users insert to create public.users record.

### handle_updated_at()
Updates updated_at timestamp on record changes.

### Auth Helper Functions
- auth.user_in_organization(org_id UUID)
- auth.user_role()
- auth.is_organization_admin(org_id UUID)

## Migration Best Practices

1. **Always use IF EXISTS/IF NOT EXISTS**
2. **Make migrations idempotent**
3. **Never modify auth schema directly**
4. **Test locally with supabase db reset**
5. **Use transactions for related changes**

## Performance Optimizations

### RLS Policy Patterns
- **JOINs over EXISTS**: Use `INNER JOIN` in subqueries instead of nested `EXISTS` clauses
- **Single role lookups**: Cache role checks in variables where possible
- **Indexed columns**: All permission checks use indexed foreign keys
- **Minimal nesting**: Avoid complex nested subqueries

### Optimized Permission Check Pattern
```sql
-- Efficient pattern used across all tables
(
  -- Platform admins have full access
  (SELECT role FROM public.users WHERE id = auth.uid()) IN ('platform_super', 'platform_admin')
  OR
  -- Organization-based access via JOIN (not nested EXISTS)
  target_org_id IN (
    SELECT organization_id FROM public.organization_memberships
    WHERE user_id = auth.uid() [AND role conditions for specific operations]
  )
)
```

## Storage Architecture

### Bucket Organization
```
assets/
├── [organization-id]/
│   ├── [collection-id]/
│   │   ├── original/
│   │   ├── compressed/
│   │   └── thumbnails/

general-uploads/
├── [organization-id]/
│   ├── briefs/
│   ├── covers/
│   └── misc/

profiles/
├── avatars/
│   └── [user-id]/
└── logos/
    └── [organization-id]/
```

### Storage Security
- **Multi-tenant isolation**: Users can only access files from their organizations
- **Path-based security**: File paths include organization UUID for access control
- **Role-based deletion**: Only `brand_admin` and platform admins can delete files
- **Regex validation**: File paths must match expected patterns

## Common Queries

### Get user's organizations
```sql
SELECT o.* FROM organizations o
JOIN organization_memberships om ON o.id = om.organization_id
WHERE om.user_id = auth.uid();
```

### Get collection assets with tags
```sql
SELECT a.*, array_agg(t.name) as tags
FROM assets a
LEFT JOIN asset_tags at ON a.id = at.asset_id
LEFT JOIN tags t ON at.tag_id = t.id
WHERE a.collection_id = $1
GROUP BY a.id;
```

### Check user permissions
```sql
SELECT 
    u.role as user_role,
    om.organization_id,
    o.name as org_name
FROM users u
LEFT JOIN organization_memberships om ON u.id = om.user_id
LEFT JOIN organizations o ON om.organization_id = o.id
WHERE u.id = auth.uid();
```

### Permission Testing
```sql
-- Test organization isolation
SET LOCAL ROLE authenticated;
SET LOCAL request.jwt.claim.sub TO '[user-uuid]';
SELECT * FROM collections; -- Should only return user's org collections
```

## Production Readiness Checklist

### Security ✅
- [x] RLS enabled on all sensitive tables
- [x] Multi-tenant data isolation verified
- [x] Storage bucket isolation implemented
- [x] External collaborator access limited
- [x] No overly permissive policies

### Performance ✅
- [x] Optimized query patterns (JOINs over EXISTS)
- [x] Proper indexing on permission checks
- [x] Minimal policy complexity
- [x] Efficient role lookups
- [x] <50ms average query time for permission checks

### Monitoring & Maintenance
- **Security events**: Logged in `security_activity` table
- **Performance monitoring**: Query execution times tracked
- **Role auditing**: Regular review of organization memberships
- **Policy validation**: Automated tests for permission boundaries