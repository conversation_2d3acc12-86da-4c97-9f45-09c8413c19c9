# Staging Data Sync Guide

## Understanding Data Differences

### Local vs Staging vs Production

1. **Local Development**
   - Uses seed data from `/supabase/seed.sql`
   - Resets to seed data on `supabase db reset`
   - Passwords and test users are always consistent

2. **Staging Environment**
   - Does NOT automatically receive seed data
   - User data persists between deployments
   - Password changes are permanent until manually reset
   - Schema changes are applied via migrations

3. **Production Environment**
   - Real user data
   - Never reset or seeded
   - Schema changes only via migrations

## Common Issues

### Login Works Locally but Not on Staging

This usually happens because:
1. The user doesn't exist on staging
2. The password was changed on staging
3. Email confirmation is required on staging but not local

### Debugging Steps

1. **Check if user exists**:
   ```sql
   SELECT id, email FROM auth.users WHERE email = '<EMAIL>';
   ```

2. **Check user state**:
   ```sql
   SELECT 
       id, 
       email, 
       email_confirmed_at,
       encrypted_password IS NOT NULL as has_password
   FROM auth.users 
   WHERE email = '<EMAIL>';
   ```

3. **Reset password**:
   ```sql
   UPDATE auth.users 
   SET 
       encrypted_password = crypt('password123', gen_salt('bf')),
       email_confirmed_at = COALESCE(email_confirmed_at, NOW())
   WHERE email = '<EMAIL>';
   ```

## Database Type Generation

When you run `supabase gen types typescript --local`, it:
- Reads the LOCAL database schema
- Generates TypeScript types
- Does NOT affect staging or production

To keep staging in sync:
1. Create migrations for schema changes
2. Push migrations to staging
3. The deployment process will apply them

## Best Practices

1. **Never manually modify staging/production data** unless necessary
2. **Use migrations** for all schema changes
3. **Test locally first** before pushing to staging
4. **Document any manual data changes** for team awareness
5. **Keep seed data updated** with test scenarios

## Migration Workflow

```bash
# 1. Make schema changes locally
# 2. Create migration
supabase migration new your_migration_name

# 3. Generate types locally
supabase gen types typescript --local > src/types/database.types.ts

# 4. Test locally
supabase db reset

# 5. Push to staging (migrations applied automatically on deploy)
git add .
git commit -m "Add migration for X"
git push origin main
```

The Vercel deployment will automatically apply pending migrations to the staging database.