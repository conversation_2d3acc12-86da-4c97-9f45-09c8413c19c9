# Database Development

## Migration Development
- Create migration: `supabase migration new epic_name_description`
- Test locally: `supabase db reset`
- Verify with: `supabase db diff`
   
## Testing Database Changes
- Always test on local first
- Check RLS policies with different roles
- Verify foreign key constraints
- Test rollback scenarios
   
## Deployment
- Link to staging first: `supabase link --project-ref qnfmiotatmkoumlymynq`
- Push to staging: `supabase db push`
- Test on staging thoroughly
- Only then push to production

## Database Migration Process

**IMPORTANT**: Always check which project you're connected to before pushing migrations:
- `supabase link --project-ref [PROJECT_ID]` - Link to specific project
- `supabase projects list` - List available projects

Commands:
- `supabase db reset` - Reset local database
- `supabase db push` - Push local migrations to linked project (verify it's staging!)
- `supabase migration new <name>` - Create new migration
- `supabase migration list` - List migrations