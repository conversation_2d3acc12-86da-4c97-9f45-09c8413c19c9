# RLS Policy Patterns & Best Practices

This guide provides patterns and best practices for Row Level Security (RLS) policies in Supabase, based on real-world issues and solutions.

## Understanding RLS Policy Components

### The Two Clauses

Every RLS policy has two main components:

1. **`USING` clause**: Determines which rows can be accessed
2. **`WITH CHECK` clause**: Validates new/updated data AND affects what data is returned

```sql
CREATE POLICY "policy_name" ON table_name
FOR UPDATE
USING (
  -- Which existing rows can be updated
  auth.uid() = user_id
)
WITH CHECK (
  -- What new values are allowed AND what data can be returned
  auth.uid() = user_id
);
```

## Common Patterns

### 1. Platform Admin Full Access Pattern

**Problem**: Admin users need unrestricted access but WITH CHECK limits returned data.

**Solution**: Separate policy with `WITH CHECK (true)`

```sql
-- ✅ Correct: Admin can update and see all data
CREATE POLICY "platform_admins_full_access"
ON public.table_name
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid() 
    AND role IN ('platform_super', 'platform_admin')
  )
)
WITH CHECK (true);  -- Critical: Allow all data to be returned
```

### 2. Organization-Based Access Pattern

**Problem**: Users should only access data within their organization.

**Solution**: Check organization membership

```sql
CREATE POLICY "organization_members_access"
ON public.assets
FOR ALL
USING (
  EXISTS (
    SELECT 1 
    FROM public.collections c
    JOIN public.organization_memberships om ON om.organization_id = c.organization_id
    WHERE c.id = assets.collection_id 
    AND om.user_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 
    FROM public.collections c
    JOIN public.organization_memberships om ON om.organization_id = c.organization_id
    WHERE c.id = collection_id  -- Note: no table prefix in WITH CHECK
    AND om.user_id = auth.uid()
  )
);
```

### 3. Role-Based Write Restrictions Pattern

**Problem**: Some roles should have read-only access.

**Solution**: Different policies for different operations

```sql
-- Read access for all members
CREATE POLICY "members_read_assets"
ON public.assets
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE organization_id = (
      SELECT organization_id FROM public.collections 
      WHERE id = assets.collection_id
    )
    AND user_id = auth.uid()
  )
);

-- Write access only for specific roles
CREATE POLICY "writers_modify_assets"
ON public.assets
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.users u
    JOIN public.organization_memberships om ON om.user_id = u.id
    WHERE u.id = auth.uid()
    AND u.role NOT IN ('external_prompter')  -- Exclude read-only roles
    AND om.organization_id = (
      SELECT organization_id FROM public.collections 
      WHERE id = assets.collection_id
    )
  )
)
WITH CHECK (
  -- Same check for consistency
  EXISTS (
    SELECT 1 FROM public.users u
    JOIN public.organization_memberships om ON om.user_id = u.id
    WHERE u.id = auth.uid()
    AND u.role NOT IN ('external_prompter')
    AND om.organization_id = (
      SELECT organization_id FROM public.collections 
      WHERE id = collection_id
    )
  )
);
```

### 4. Owner-Only Pattern

**Problem**: Users should only modify their own records.

**Solution**: Simple user_id check

```sql
CREATE POLICY "users_own_records"
ON public.user_preferences
FOR ALL
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);
```

### 5. Public Read, Authenticated Write Pattern

**Problem**: Data should be publicly readable but only authenticated users can modify.

**Solution**: Different policies for different operations

```sql
-- Anyone can read
CREATE POLICY "public_read"
ON public.announcements
FOR SELECT
USING (true);

-- Only authenticated users can create
CREATE POLICY "authenticated_create"
ON public.announcements
FOR INSERT
WITH CHECK (
  auth.uid() IS NOT NULL
  AND EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid()
  )
);
```

## Debugging RLS Issues

### 1. Update Returns Empty Array

**Symptom**:
```javascript
const { data, error } = await supabase
  .from('table')
  .update({ field: 'value' })
  .eq('id', id)
  .select();

console.log(data); // []
console.log(error); // null
```

**Diagnosis**: WITH CHECK clause is filtering the returned data.

**Fix**: Check your WITH CHECK clause or create a separate policy for admin users.

### 2. Permission Denied Errors

**Symptom**:
```
{
  code: '42501',
  message: 'new row violates row-level security policy for table "table_name"'
}
```

**Diagnosis**: USING or WITH CHECK clause is denying access.

**Debug Steps**:
```sql
-- Check what the user can see
SET LOCAL ROLE authenticated;
SET LOCAL request.jwt.claims.sub TO 'user-uuid-here';
SELECT * FROM table_name WHERE id = 'record-id';

-- Check user's role and permissions
SELECT 
  auth.uid(),
  auth.role(),
  (SELECT role FROM public.users WHERE id = auth.uid());
```

### 3. Inconsistent Behavior

**Symptom**: Works locally but not in production.

**Common Causes**:
1. Different RLS policies between environments
2. Missing migrations in production
3. Different user roles/data in production

**Debug Query**:
```sql
-- Compare policies between environments
SELECT 
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public' 
    AND tablename = 'your_table'
ORDER BY policyname;
```

## Best Practices

### 1. Use Separate Policies for Different Operations
```sql
-- ❌ Don't use FOR ALL unless truly needed
CREATE POLICY "do_everything" ON table FOR ALL ...

-- ✅ Be specific about operations
CREATE POLICY "read_access" ON table FOR SELECT ...
CREATE POLICY "create_access" ON table FOR INSERT ...
CREATE POLICY "update_access" ON table FOR UPDATE ...
CREATE POLICY "delete_access" ON table FOR DELETE ...
```

### 2. Optimize Policy Performance
```sql
-- ❌ Avoid multiple nested EXISTS
USING (
  EXISTS (
    SELECT 1 FROM table1
    WHERE EXISTS (
      SELECT 1 FROM table2
      WHERE EXISTS (...)
    )
  )
)

-- ✅ Use JOINs for better performance
USING (
  EXISTS (
    SELECT 1 
    FROM table1 t1
    JOIN table2 t2 ON t1.id = t2.table1_id
    WHERE t1.user_id = auth.uid()
  )
)
```

### 3. Always Test Updates with .select()
```typescript
// Always use .select() to verify updates work correctly
const { data, error } = await supabase
  .from('table')
  .update(updates)
  .eq('id', id)
  .select();

if (!data || data.length === 0) {
  console.error('Update may have failed due to RLS policies');
}
```

### 4. Document Your Policies
```sql
-- Always add comments explaining the policy
COMMENT ON POLICY "platform_admins_full_access" ON public.assets IS 
'Platform administrators have unrestricted access to all assets. 
WITH CHECK (true) ensures updates return data properly.';
```

### 5. Use Helper Functions
```sql
-- Create reusable functions for common checks
CREATE OR REPLACE FUNCTION auth.is_platform_user()
RETURNS BOOLEAN AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('platform_super', 'platform_admin')
  );
$$ LANGUAGE sql SECURITY DEFINER;

-- Use in policies
CREATE POLICY "platform_users_access"
ON public.table_name
FOR ALL
USING (auth.is_platform_user())
WITH CHECK (auth.is_platform_user());
```

## Testing RLS Policies

### 1. Unit Test Your Policies
```sql
-- Test as different users
DO $$
DECLARE
  test_user_id UUID := 'test-user-uuid';
  test_admin_id UUID := 'test-admin-uuid';
  result RECORD;
BEGIN
  -- Test as regular user
  SET LOCAL ROLE authenticated;
  SET LOCAL request.jwt.claims.sub TO test_user_id;
  
  -- Should only see own records
  SELECT COUNT(*) INTO result FROM assets;
  ASSERT result.count = expected_count, 'User sees wrong number of assets';
  
  -- Test as admin
  SET LOCAL request.jwt.claims.sub TO test_admin_id;
  
  -- Should see all records
  SELECT COUNT(*) INTO result FROM assets;
  ASSERT result.count = total_count, 'Admin should see all assets';
END $$;
```

### 2. Integration Test Pattern
```typescript
describe('RLS Policies', () => {
  it('should allow users to update their own assets', async () => {
    const { data, error } = await supabase
      .from('assets')
      .update({ workflow_stage: 'draft' })
      .eq('id', ownedAssetId)
      .select();
      
    expect(error).toBeNull();
    expect(data).toHaveLength(1);
    expect(data[0].workflow_stage).toBe('draft');
  });
  
  it('should prevent users from updating others assets', async () => {
    const { data, error } = await supabase
      .from('assets')
      .update({ workflow_stage: 'draft' })
      .eq('id', otherUserAssetId)
      .select();
      
    expect(error).toBeDefined();
    expect(error.code).toBe('42501');
  });
});
```

## Migration Template

When creating new RLS policies, use this template:

```sql
-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "old_policy_name" ON public.table_name;

-- Create new policies with clear naming
CREATE POLICY "role_operation_condition"
ON public.table_name
FOR operation
USING (
  -- Who can perform this operation
  -- Be specific and optimize for performance
)
WITH CHECK (
  -- What data can be inserted/updated
  -- Remember: this affects returned data too!
);

-- Add helpful comments
COMMENT ON POLICY "role_operation_condition" ON public.table_name IS 
'Clear description of what this policy does and why.
Important notes about edge cases or performance considerations.';

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.table_name TO authenticated;
GRANT USAGE ON SEQUENCE table_name_id_seq TO authenticated;
```

## Common Gotchas

1. **WITH CHECK affects SELECT in updates**: Even if USING allows the update, WITH CHECK determines what data is returned
2. **No table prefix in WITH CHECK**: Use `column_name` not `table.column_name`
3. **Policies are OR'd together**: Multiple permissive policies create more access, not less
4. **SECURITY DEFINER functions bypass RLS**: Be careful with these
5. **RLS doesn't apply to superusers**: Testing with superuser role will bypass policies

## References
- [Supabase RLS Documentation](https://supabase.com/docs/guides/auth/row-level-security)
- [PostgreSQL RLS Documentation](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)
- [PostgREST Security](https://postgrest.org/en/stable/auth.html)