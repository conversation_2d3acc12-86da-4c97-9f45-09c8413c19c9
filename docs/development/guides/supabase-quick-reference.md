# Supabase Quick Reference

Quick patterns and snippets for common Supabase operations.

## Update Operations

### ✅ Always Use .select() on Updates
```typescript
// Get immediate feedback on what was updated
const { data, error } = await supabase
  .from('table')
  .update({ field: 'value' })
  .eq('id', id)
  .select();

// Check if update succeeded
if (!data || data.length === 0) {
  throw new Error('Update failed - no data returned');
}
```

### ✅ Update with Verification
```typescript
async function updateWithVerification(
  table: string,
  updates: Record<string, any>,
  filter: { field: string; value: any }
) {
  // Update and get immediate result
  const { data, error } = await supabase
    .from(table)
    .update(updates)
    .eq(filter.field, filter.value)
    .select();

  if (error) throw error;
  if (!data?.length) throw new Error('No data returned - check RLS');

  // Verify updates were applied
  const success = data.every(row => 
    Object.entries(updates).every(([key, val]) => row[key] === val)
  );

  if (!success) throw new Error('Updates not applied');
  
  return data;
}
```

### ✅ Bulk Update Pattern
```typescript
// Update multiple records
const { data, error } = await supabase
  .from('assets')
  .update({ status: 'archived' })
  .in('id', assetIds)
  .select();

// With React Query invalidation
await queryClient.invalidateQueries({ 
  queryKey: ['assets'] 
});
```

## Query Patterns

### ✅ Complex Filters
```typescript
// Multiple conditions
const { data } = await supabase
  .from('assets')
  .select('*')
  .eq('organization_id', orgId)
  .in('status', ['active', 'pending'])
  .gte('created_at', startDate)
  .order('created_at', { ascending: false })
  .limit(50);

// OR conditions
const { data } = await supabase
  .from('assets')
  .select('*')
  .or('status.eq.draft,status.eq.review');
```

### ✅ Joins and Relations
```typescript
// Get assets with collection info
const { data } = await supabase
  .from('assets')
  .select(`
    *,
    collection:collections (
      id,
      name,
      organization:organizations (
        id,
        name
      )
    )
  `)
  .eq('id', assetId)
  .single();
```

### ✅ Count Queries
```typescript
// Get count without data
const { count } = await supabase
  .from('assets')
  .select('*', { count: 'exact', head: true })
  .eq('collection_id', collectionId);

// Get data with count
const { data, count } = await supabase
  .from('assets')
  .select('*', { count: 'exact' })
  .eq('collection_id', collectionId)
  .range(0, 9); // Pagination
```

## Error Handling

### ✅ Standard Error Handler
```typescript
function handleSupabaseError(error: any) {
  const errorMessages: Record<string, string> = {
    '42501': 'Permission denied',
    '42P01': 'Table not found',
    '23505': 'Duplicate entry',
    '23503': 'Foreign key violation',
    '22P02': 'Invalid input',
    'PGRST301': 'JWT expired',
    'PGRST204': 'No rows found'
  };

  return {
    message: errorMessages[error?.code] || error?.message || 'Unknown error',
    code: error?.code
  };
}

// Usage
try {
  const { data, error } = await supabase.from('table').select();
  if (error) throw error;
} catch (err) {
  const { message } = handleSupabaseError(err);
  toast.error(message);
}
```

## RLS Policy Quick Fixes

### ✅ Platform Admin Full Access
```sql
CREATE POLICY "platform_admin_all_access"
ON public.table_name FOR ALL
USING (
  auth.uid() IN (
    SELECT id FROM public.users 
    WHERE role IN ('platform_super', 'platform_admin')
  )
)
WITH CHECK (true);  -- Important: allows data to be returned
```

### ✅ Organization Member Access
```sql
CREATE POLICY "org_members_access"
ON public.table_name FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE organization_id = table_name.organization_id
    AND user_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE organization_id = organization_id  -- No table prefix!
    AND user_id = auth.uid()
  )
);
```

## React Query Integration

### ✅ Custom Hook Pattern
```typescript
export function useAssets(collectionId?: string) {
  const { supabase } = useSupabase();
  
  return useQuery({
    queryKey: ['assets', collectionId],
    queryFn: async () => {
      let query = supabase.from('assets').select('*');
      
      if (collectionId) {
        query = query.eq('collection_id', collectionId);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) throw error;
      return data || [];
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}
```

### ✅ Mutation with Optimistic Updates
```typescript
const updateAsset = useMutation({
  mutationFn: async ({ id, updates }: { id: string; updates: any }) => {
    const { data, error } = await supabase
      .from('assets')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
      
    if (error) throw error;
    if (!data) throw new Error('Update failed');
    return data;
  },
  onMutate: async ({ id, updates }) => {
    // Cancel queries
    await queryClient.cancelQueries({ queryKey: ['assets'] });
    
    // Snapshot previous value
    const previous = queryClient.getQueryData(['assets']);
    
    // Optimistically update
    queryClient.setQueryData(['assets'], (old: any[]) =>
      old.map(item => item.id === id ? { ...item, ...updates } : item)
    );
    
    return { previous };
  },
  onError: (err, variables, context) => {
    // Rollback on error
    if (context?.previous) {
      queryClient.setQueryData(['assets'], context.previous);
    }
  },
  onSettled: () => {
    // Always refetch after error or success
    queryClient.invalidateQueries({ queryKey: ['assets'] });
  }
});
```

## Storage Operations

### ✅ Upload File
```typescript
async function uploadFile(bucket: string, path: string, file: File) {
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file, {
      cacheControl: '3600',
      upsert: false
    });
    
  if (error) throw error;
  
  // Get public URL
  const { data: { publicUrl } } = supabase.storage
    .from(bucket)
    .getPublicUrl(path);
    
  return publicUrl;
}
```

### ✅ Delete Files
```typescript
async function deleteFiles(bucket: string, paths: string[]) {
  const { error } = await supabase.storage
    .from(bucket)
    .remove(paths);
    
  if (error) throw error;
}
```

## Auth Operations

### ✅ Check User Role
```typescript
async function getUserRole() {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) return null;
  
  const { data, error } = await supabase
    .from('users')
    .select('role')
    .eq('id', user.id)
    .single();
    
  return data?.role || null;
}
```

### ✅ Sign Out Completely
```typescript
async function signOut() {
  // Clear React Query cache
  queryClient.clear();
  
  // Sign out from Supabase
  await supabase.auth.signOut();
  
  // Redirect
  window.location.href = '/login';
}
```

## Debugging Queries

### ✅ Log Query Details
```typescript
// Development helper
function debugQuery(query: any) {
  if (process.env.NODE_ENV === 'development') {
    console.log('Query:', {
      table: query._table,
      select: query._select,
      filters: query._filters,
      order: query._order
    });
  }
  return query;
}

// Usage
const { data } = await debugQuery(
  supabase.from('assets').select('*').eq('id', id)
);
```

### ✅ Test in SQL Editor
```sql
-- Test as specific user
SET LOCAL ROLE authenticated;
SET LOCAL request.jwt.claims.sub TO 'user-uuid-here';

-- Run your query
SELECT * FROM assets WHERE id = 'asset-uuid';

-- Check what policies are applying
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM assets WHERE id = 'asset-uuid';
```

## Common Gotchas

1. **Empty arrays vs null**: Supabase returns `[]` for no results, not `null`
2. **UUID format**: Always cast string IDs to UUID: `'id-string'::uuid`
3. **Timezone**: Supabase uses UTC - always convert for display
4. **Case sensitivity**: Use `ilike` for case-insensitive searches
5. **Array operations**: Use `contains` for array fields, not `in`

## Performance Tips

1. **Use select() sparingly**: Only select fields you need
2. **Paginate large datasets**: Use `range()` for pagination
3. **Index foreign keys**: Ensure all foreign keys have indexes
4. **Avoid N+1 queries**: Use joins instead of multiple queries
5. **Cache with React Query**: Set appropriate `staleTime`