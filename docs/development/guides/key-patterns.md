# Key Development Patterns

## API Calls
All database operations go through Supabase client:
```typescript
const { data, error } = await supabase
  .from('assets')
  .select('*')
  .eq('collection_id', collectionId);
```

## Authentication Flow
Protected routes use the `ProtectedRoute` wrapper:
```tsx
<ProtectedRoute>
  <Dashboard />
</ProtectedRoute>
```

## Role-Based Access
Use `RoleProtectedRoute` with the new unified role system:

```tsx
import { createRoleGuard } from '@/components/auth/RoleProtectedRoute';

// Option 1: Use role guard helpers
const { platformOnly, adminOnly, brandOnly } = createRoleGuard();

if (platformOnly()) {
  // Show platform-only features
}

// Option 2: Use RoleProtectedRoute component
<RoleProtectedRoute allowedRole="platform_admin">
  <PlatformAdminDashboard />
</RoleProtectedRoute>

// Option 3: Use useUserRole hook directly
const { isPlatformUser, isBrandAdmin } = useUserRole();

if (isPlatformUser) {
  // Platform user features
} else if (isBrandAdmin) {
  // Brand admin features
}
```

### Available Roles
- `platform_super` - Platform super admin
- `platform_admin` - Platform admin  
- `brand_admin` - Brand administrator
- `brand_member` - Brand member
- `external_retoucher` - External retoucher
- `external_prompter` - External prompter

### Role Helper Functions
```tsx
const { 
  isPlatformUser,    // platform_super || platform_admin
  isBrandAdmin,      // brand_admin
  isBrandMember,     // brand_member  
  isExternalUser     // external_retoucher || external_prompter
} = useUserRole();
```

## Asset URL Generation
Use the `getAssetUrl` utility to handle asset URLs with proper thumbnail/compressed versions:
```typescript
const url = getAssetUrl(asset, true); // true for thumbnail
```

## Environment Configuration
Required environment variables:
```
VITE_SUPABASE_URL=<your-supabase-url>
VITE_SUPABASE_ANON_KEY=<your-anon-key>
```

## Testing Strategy
- Tests use Vitest
- Test files are colocated with components (`*.test.tsx`)
- Component testing with React Testing Library
- Mock Supabase calls in tests