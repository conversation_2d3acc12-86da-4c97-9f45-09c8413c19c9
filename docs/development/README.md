# Development Documentation

This directory contains all development-related documentation for the FashionLab platform.

## Directory Structure

### 🔄 Workflows
Development processes and methodologies
- [Git Workflow](workflows/git-workflow.md) - Branching strategy and commit guidelines

### 📚 Guides
Technical guides and patterns
- [Database Development](guides/database-development.md) - Database migration and testing guide
- [Key Patterns](guides/key-patterns.md) - Common development patterns and practices

### 🔌 Integrations
External service integrations
- [FashionLab API Integration](integrations/fashionlab-api-integration.md) - Planning for AI API integration
- [FashionLab API Examples](integrations/fashionlab-api-examples.md) - Example workflows and requests
- [Integration Summary](integrations/integration-summary.md) - Quick overview of integration architecture
