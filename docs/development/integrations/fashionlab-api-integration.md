# FashionLab API Integration Planning

## Overview

This document outlines how the platform will integrate with the **FashionLab API** (the external AI API currently running on <PERSON><PERSON><PERSON> at `https://fashionlab.notfirst.rodeo`).

## Architecture

1. **FashionLab API** (External - Already Running):
   - Hosted on Hetzner server
   - Handles AI model training and image generation
   - Microservices architecture
   - Bearer token authentication

2. **Platform Integration** (To Be Built):
   - Supabase Edge Function(s) to proxy requests to FashionLab API
   - NOT a separate platform API - just integration functions
   - Handles authentication translation and result storage

## Current State

### Existing Infrastructure

#### FashionLab API (External - Live)
Hosted at `https://fashionlab.notfirst.rodeo` with microservices:
- `/prepare-training` - Process images for model training
- `/train` - Train custom LoRA models
- `/generate-image` - Generate fashion images
- `/refine` - Refine images with masking
- `/queue/{queueId}` - Check async operation status
- JWT token authentication required (format: `jwt ${token}`)

#### Platform Infrastructure
- Supabase backend (PostgreSQL, Auth, Storage)
- Edge Functions: email, account deletion, JWT generation
- JWT authentication via Supabase Auth
- Row Level Security (RLS) policies

### What We Need to Build
- Edge Function(s) to proxy requests to FashionLab API
- Authentication token management
- Result storage in platform database
- Error handling and retry logic
- Usage tracking and quotas

## Integration Design

### Design Principles
1. **Simple Proxy Pattern**: Edge Function acts as authenticated proxy
2. **Token Management**: Handle FashionLab API authentication internally
3. **Data Persistence**: Store AI results in platform database
4. **Error Resilience**: Retry logic and graceful degradation
5. **Usage Control**: Track and limit API usage per organization

### Technology Choices
- **Integration Layer**: Supabase Edge Functions (Deno runtime)
- **FashionLab API**: External Hetzner server
- **Authentication**:
  - Users: Supabase Auth (JWT)
  - FashionLab API: JWT tokens with `jwt ${token}` format (stored securely)
- **Database**: PostgreSQL via Supabase
- **File Storage**: Supabase Storage for platform assets

## Integration Architecture

### System Components

1. **FashionLab API** (External)
   - Base URL: `https://fashionlab.notfirst.rodeo`
   - Handles all AI operations
   - JWT token authentication (format: `jwt ${token}`)
   - Async operations with queue system

2. **Platform Integration** (Edge Functions)
   - Proxies requests to FashionLab API
   - Manages authentication
   - Stores results in platform
   - Tracks usage

### Integration Flow
```
Client App → Edge Function → FashionLab API
                ↓                ↓
          Supabase DB      Hetzner Server
```

## Edge Function Design

We'll create Edge Functions that:
1. Accept authenticated requests from platform users
2. Validate permissions and quotas
3. Forward requests to FashionLab API with bearer token
4. Store job metadata in database
5. Return results to client

### Example Edge Function
```typescript
// supabase/functions/ai-generate/index.ts
export async function handler(req: Request) {
  // 1. Validate user JWT
  const user = await validateUser(req);
  
  // 2. Check permissions and quotas
  await checkQuota(user.organization_id);
  
  // 3. Get FashionLab API token
  const apiToken = await getAIToken();
  
  // 4. Forward to FashionLab API
  const response = await fetch('https://fashionlab.notfirst.rodeo/generate-image', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiToken}`,
      'Content-Type': 'application/json'
    },
    body: req.body
  });
  
  // 5. Store job in database
  const job = await response.json();
  await storeJob(job, user);
  
  return new Response(JSON.stringify(job));
}
```

### Organizations (Brands)
```http
GET    /api/v1/organizations              # List user's organizations
GET    /api/v1/organizations/:id          # Get organization details
PATCH  /api/v1/organizations/:id          # Update organization
POST   /api/v1/organizations/:id/invite   # Invite member
GET    /api/v1/organizations/:id/members  # List members
```

### Campaigns (Collections)
```http
GET    /api/v1/campaigns                  # List campaigns
POST   /api/v1/campaigns                  # Create campaign
GET    /api/v1/campaigns/:id              # Get campaign
PATCH  /api/v1/campaigns/:id              # Update campaign
DELETE /api/v1/campaigns/:id              # Archive campaign
POST   /api/v1/campaigns/:id/assets       # Upload assets
```

### Assets
```http
GET    /api/v1/assets                     # List assets
POST   /api/v1/assets/upload              # Upload asset
GET    /api/v1/assets/:id                 # Get asset details
PATCH  /api/v1/assets/:id                 # Update asset
DELETE /api/v1/assets/:id                 # Delete asset
POST   /api/v1/assets/:id/download        # Get download URL
POST   /api/v1/assets/bulk                # Bulk operations
```

## Planned Edge Functions

### AI Integration Functions
```
supabase/functions/
├── ai-prepare-training/    # Proxy to /prepare-training
├── ai-train/              # Proxy to /train  
├── ai-generate/           # Proxy to /generate-image
├── ai-refine/             # Proxy to /refine
├── ai-check-status/       # Proxy to /queue/{id}
└── ai-import-results/     # Import AI results to platform assets
```

### Function Responsibilities

1. **ai-prepare-training**
   - Validate user has permission to train models
   - Forward images to FashionLab API
   - Store job metadata in database

2. **ai-train**
   - Check training quota
   - Initiate training on FashionLab API
   - Create model record in database

3. **ai-generate**
   - Verify model ownership
   - Check generation quota
   - Forward generation request
   - Track usage

4. **ai-refine**
   - Validate image ownership
   - Send refinement request
   - Update asset metadata

5. **ai-check-status**
   - Poll FashionLab API queue
   - Update job status in database
   - Trigger webhooks on completion

6. **ai-import-results**
   - Fetch completed results
   - Create platform assets
   - Apply tags and metadata

### Webhooks
```http
POST   /api/v1/webhooks                   # Register webhook
GET    /api/v1/webhooks                   # List webhooks
PATCH  /api/v1/webhooks/:id               # Update webhook
DELETE /api/v1/webhooks/:id               # Delete webhook
POST   /api/v1/webhooks/:id/test          # Test webhook
```

## Authentication & Authorization

### Authentication Methods
1. **JWT Tokens**: For user sessions (web app)
2. **API Keys**: For server-to-server communication
3. **OAuth 2.0**: Future consideration for third-party apps

### API Key Management
```typescript
interface ApiKey {
  id: string;
  name: string;
  key_prefix: string;  // First 8 chars for identification
  hashed_key: string;  // Bcrypt hash of full key
  organization_id: string;
  permissions: string[];  // Scoped permissions
  rate_limit: number;     // Requests per minute
  expires_at?: Date;
  last_used_at?: Date;
  created_at: Date;
}
```

### Permission Scopes
- `assets:read` - View assets
- `assets:write` - Upload/modify assets
- `campaigns:read` - View campaigns
- `campaigns:write` - Create/modify campaigns
- `ai:generate` - Use AI generation
- `ai:train` - Train custom models
- `webhooks:manage` - Configure webhooks

## Rate Limiting

### Tier Structure
```typescript
interface RateLimitTier {
  name: 'free' | 'starter' | 'pro' | 'enterprise';
  requests_per_minute: number;
  requests_per_day: number;
  concurrent_jobs: number;
  ai_generations_per_day: number;
}

const rateLimits: Record<string, RateLimitTier> = {
  free: {
    name: 'free',
    requests_per_minute: 60,
    requests_per_day: 1000,
    concurrent_jobs: 1,
    ai_generations_per_day: 10
  },
  starter: {
    name: 'starter',
    requests_per_minute: 300,
    requests_per_day: 10000,
    concurrent_jobs: 3,
    ai_generations_per_day: 100
  },
  // ... more tiers
};
```

### Rate Limit Headers
```http
X-RateLimit-Limit: 300
X-RateLimit-Remaining: 299
X-RateLimit-Reset: 1620121020
X-RateLimit-Tier: starter
```

## Async Operations

### Job Queue Design
```typescript
interface Job {
  id: string;
  type: 'ai_generation' | 'model_training' | 'bulk_upload';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;  // 0-100
  organization_id: string;
  created_by: string;
  input: Record<string, any>;
  output?: Record<string, any>;
  error?: string;
  created_at: Date;
  started_at?: Date;
  completed_at?: Date;
}
```

### Webhook Events
- `job.created`
- `job.started`
- `job.progress`
- `job.completed`
- `job.failed`
- `asset.created`
- `asset.updated`
- `campaign.created`

## SDK Development

### TypeScript/JavaScript SDK
```typescript
import { FashionLabClient } from '@fashionlab/sdk';

const client = new FashionLabClient({
  apiKey: process.env.FASHIONLAB_API_KEY,
  baseUrl: 'https://api.fashionlab.io'
});

// Upload and prepare training images
const prepareJob = await client.ai.prepareTraining({
  images: [file1, file2, file3],
  aspectRatio: '1024x1024',
  clothingType: 'jacket'
});

// Train custom model
const trainJob = await client.ai.train({
  preparedImages: prepareJob.images,
  triggerWord: 'uniquejacket123'
});

// Generate fashion images
const generateJob = await client.ai.generate({
  prompt: 'elegant black dress with lace details',
  modelName: trainJob.modelName,
  modelType: 'Laila',
  count: 3
});

// Refine generated image
const refineJob = await client.ai.refine({
  baseImage: generateJob.images[0],
  referenceImage: referenceFile,
  mask: maskData
});

// Import to platform asset management
const asset = await client.assets.importFromAI({
  aiJobId: generateJob.id,
  campaignId: 'campaign_123',
  tags: ['ai-generated', 'dress', 'black']
});
```

### Python SDK
```python
from fashionlab import FashionLabClient

client = FashionLabClient(
    api_key=os.environ["FASHIONLAB_API_KEY"]
)

# Upload asset
asset = client.assets.upload(
    campaign_id="campaign_123",
    file=file_buffer,
    metadata={"tags": ["summer", "dress"]}
)

# Generate AI image
job = client.ai.generate(
    model_id="model_456",
    prompt="Summer dress on model",
    style="editorial"
)

# Wait for completion
result = client.jobs.wait(job.id)
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Please retry after 60 seconds.",
    "details": {
      "limit": 300,
      "remaining": 0,
      "reset_at": "2025-05-17T12:00:00Z"
    }
  },
  "request_id": "req_abc123"
}
```

### Error Codes
- `UNAUTHORIZED` - Invalid or missing authentication
- `FORBIDDEN` - Insufficient permissions
- `NOT_FOUND` - Resource not found
- `VALIDATION_ERROR` - Invalid request data
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `INTERNAL_ERROR` - Server error
- `SERVICE_UNAVAILABLE` - Temporary outage

## Documentation Strategy

### API Documentation
1. **OpenAPI Specification**: Machine-readable API definition
2. **Interactive Docs**: Swagger UI or similar
3. **Getting Started Guide**: Quick setup tutorial
4. **Authentication Guide**: Detailed auth documentation
5. **Code Examples**: Multiple languages
6. **Use Case Guides**: Common scenarios
7. **Changelog**: Version history

### Developer Portal Features
- API key management
- Usage analytics
- Interactive console
- SDK downloads
- Support tickets
- Status page

## Implementation Phases

### Phase 1: Basic Integration (Week 1)
- [ ] Create first Edge Function (ai-generate)
- [ ] Implement bearer token management
- [ ] Test proxy to FashionLab API
- [ ] Basic error handling

### Phase 2: Complete Integration (Week 2)
- [ ] Implement all proxy functions
- [ ] Add job tracking in database
- [ ] Create usage tracking
- [ ] Import results function

### Phase 3: Platform Enhancement (Week 3)
- [ ] Add quota management
- [ ] Implement webhooks
- [ ] Create admin dashboard
- [ ] Usage analytics

### Phase 4: Future Considerations
- [ ] Build actual platform API (if needed)
- [ ] Create SDKs
- [ ] Advanced features

## Security Considerations

### API Security
1. **HTTPS Only**: Enforce TLS 1.2+
2. **API Key Rotation**: Regular key rotation
3. **IP Allowlisting**: Optional per organization
4. **Request Signing**: Webhook signatures
5. **Input Validation**: Strict parameter validation
6. **SQL Injection**: Parameterized queries
7. **Rate Limiting**: DDoS protection

### Data Protection
1. **Encryption at Rest**: Database encryption
2. **Encryption in Transit**: TLS for all communication
3. **Access Logging**: Audit trail for all operations
4. **GDPR Compliance**: Data retention policies
5. **Secure Deletion**: Proper data removal

## Performance Goals

### Response Time Targets
- Authentication: <100ms
- CRUD operations: <200ms
- File uploads: Streaming
- AI operations: Async only
- Webhook delivery: <5s

### Scalability Targets
- 1,000 requests/second
- 10,000 concurrent users
- 1M API calls/day
- 99.9% uptime SLA

## Monitoring & Analytics

### Metrics to Track
1. **API Usage**: Requests per endpoint
2. **Response Times**: P50, P90, P99
3. **Error Rates**: By endpoint and error type
4. **Rate Limit Hits**: Per organization
5. **SDK Usage**: Version adoption
6. **Geographic Distribution**: Request origins

### Monitoring Tools
- Application: Sentry
- Infrastructure: Datadog
- Uptime: Pingdom
- Analytics: Mixpanel

## Next Steps

1. **Immediate Actions**
   - Create first Edge Function (ai-generate)
   - Set up bearer token storage
   - Test basic proxy functionality
   - Create job tracking schema

2. **Short Term (1 week)**
   - Implement all proxy functions
   - Add error handling and retries
   - Create usage tracking
   - Test end-to-end workflow

3. **Medium Term (2 weeks)**
   - Add quota management
   - Implement result import
   - Create monitoring dashboard
   - Document integration patterns

4. **Future Considerations**
   - Evaluate need for full platform API
   - Consider direct client access options
   - Plan for scaling and caching