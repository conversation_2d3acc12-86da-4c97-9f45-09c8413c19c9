# API Integration Example

This document shows how the Platform API will integrate with the external AI API.

## Example: Complete AI Workflow via Platform API

### 1. Prepare Training Images

**Client Request** to Platform API:
```http
POST /api/v1/ai/models/prepare
Authorization: Bearer <platform-jwt>
Content-Type: multipart/form-data

files: [image1.jpg, image2.jpg, image3.jpg]
aspectRatio: "1024x1024"
clothingType: "jacket"
description: "Black leather jacket with gold zippers"
campaignId: "campaign_123"
```

**Platform API Actions**:
1. Validate user permissions for campaign
2. Get AI API bearer token
3. Forward request to AI API
4. Store job metadata in database

**Platform Forwards to AI API**:
```http
POST https://fashionlab.notfirst.rodeo/prepare-training
Authorization: Bearer <ai-bearer-token>

files: [image1.jpg, image2.jpg, image3.jpg]
aspect_ratio: "1024x1024"
prompt: "a jacket"
```

**Edge Function Response**:
```json
{
  "jobId": "job_abc123",
  "queueId": "queue_xyz789",
  "status": "processing",
  "type": "prepare_training",
  "campaignId": "campaign_123",
  "createdAt": "2025-05-17T10:00:00Z"
}
```

### 2. Train Model

**Client Request** to Platform API:
```http
POST /api/v1/ai/models/train
Authorization: Bearer <platform-jwt>
Content-Type: application/json

{
  "prepareJobId": "job_abc123",
  "modelName": "FL_Jacket_Summer2025",
  "triggerWord": "uniquejacket789"
}
```

**Platform API Actions**:
1. Fetch prepared images from previous job
2. Create model record in database
3. Forward to AI API for training

**Edge Function Response**:
```json
{
  "modelId": "model_def456",
  "jobId": "job_ghi789",
  "status": "training",
  "estimatedTime": "20 minutes",
  "modelName": "FL_Jacket_Summer2025"
}
```

### 3. Generate Images

**Client Request** to Platform API:
```http
POST /api/v1/ai/generate
Authorization: Bearer <platform-jwt>
Content-Type: application/json

{
  "modelId": "model_def456",
  "prompt": "elegant woman wearing the jacket at a rooftop party",
  "count": 3,
  "style": "editorial",
  "campaignId": "campaign_123"
}
```

**Edge Function Response**:
```json
{
  "jobId": "job_jkl012",
  "status": "generating",
  "estimatedTime": "2 minutes",
  "imageCount": 3
}
```

### 4. Check Job Status

**Client Request** to Platform API:
```http
GET /api/v1/ai/jobs/job_jkl012
Authorization: Bearer <platform-jwt>
```

**Edge Function Response**:
```json
{
  "jobId": "job_jkl012",
  "status": "completed",
  "type": "generation",
  "results": {
    "images": [
      {
        "id": "img_001",
        "url": "https://api.fashionlab.io/storage/v1/object/ai-generated/img_001.jpg",
        "thumbnailUrl": "https://api.fashionlab.io/storage/v1/object/ai-generated/img_001_thumb.jpg",
        "metadata": {
          "width": 1024,
          "height": 1024,
          "model": "FL_Jacket_Summer2025",
          "prompt": "elegant woman wearing the jacket at a rooftop party"
        }
      },
      // ... more images
    ]
  },
  "completedAt": "2025-05-17T10:02:30Z"
}
```

### 5. Import to Asset Management

**Client Request** to Platform API:
```http
POST /api/v1/assets/import-ai
Authorization: Bearer <platform-jwt>
Content-Type: application/json

{
  "jobId": "job_jkl012",
  "selectedImages": ["img_001", "img_003"],
  "campaignId": "campaign_123",
  "tags": ["ai-generated", "jacket", "rooftop", "editorial"],
  "workflowStage": "draft"
}
```

**Edge Function Response**:
```json
{
  "assets": [
    {
      "id": "asset_mno345",
      "campaignId": "campaign_123",
      "fileName": "FL_Jacket_Rooftop_001.jpg",
      "workflowStage": "draft",
      "tags": ["ai-generated", "jacket", "rooftop", "editorial"],
      "metadata": {
        "aiJobId": "job_jkl012",
        "modelUsed": "FL_Jacket_Summer2025",
        "generatedAt": "2025-05-17T10:02:30Z"
      }
    },
    // ... more assets
  ]
}
```

## Error Handling

The Platform API provides consistent error responses:

```json
{
  "error": {
    "code": "AI_SERVICE_ERROR",
    "message": "Failed to generate images",
    "details": {
      "aiError": "Model not found",
      "jobId": "job_jkl012"
    }
  },
  "requestId": "req_pqr678"
}
```

## Webhook Events

Platform API will emit webhooks for AI operations:

```json
{
  "event": "ai.job.completed",
  "data": {
    "jobId": "job_jkl012",
    "type": "generation",
    "status": "completed",
    "resultCount": 3,
    "organizationId": "org_123",
    "userId": "user_456"
  },
  "timestamp": "2025-05-17T10:02:30Z"
}
```

## Authentication Flow

1. Client authenticates with Platform API using Supabase JWT
2. Platform API validates user permissions
3. Platform API retrieves AI bearer token (cached)
4. Platform API makes request to AI API with bearer token
5. Platform API stores results and returns to client

This approach provides:
- Single authentication point for clients
- Permission management at platform level
- AI API token security (never exposed to clients)
- Unified error handling
- Complete audit trail