# FashionLab API Integration Summary

## Clarification

To be clear about the architecture:

1. **FashionLab API**: The existing external AI API running on Hetzner
   - URL: `https://fashionlab.notfirst.rodeo`
   - Handles: AI model training, image generation, refinement
   - Auth: Bearer tokens

2. **Platform Integration**: What we're building with Supabase Edge Functions
   - NOT a new API, just integration functions
   - Acts as a proxy to the FashionLab API
   - Handles auth translation and data storage

## What We're Building

We're creating Supabase Edge Functions that will:

1. **Proxy Requests**: Forward platform user requests to the FashionLab API
2. **Manage Authentication**: Handle FashionLab API bearer tokens internally
3. **Store Results**: Save AI-generated content in the platform database
4. **Track Usage**: Monitor API calls and enforce quotas

## Example Flow

```
User (with JWT) → Edge Function → FashionLab API (with Bearer token)
                       ↓
                 Platform Database
```

## Edge Functions to Create

- `ai-prepare-training` - Proxy to prepare training endpoint
- `ai-train` - Proxy to train endpoint
- `ai-generate` - Proxy to generate-image endpoint
- `ai-refine` - Proxy to refine endpoint
- `ai-check-status` - Check job status
- `ai-import-results` - Import results to platform

This is NOT a platform API - it's just integration functions that proxy requests to the external FashionLab API.