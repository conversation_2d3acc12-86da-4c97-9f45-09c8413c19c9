# Notes & Ideas System

During work sessions, capture notes and ideas in the `/notes/` directory:

## Structure
- `notes/session-notes.md` - Current session notes and reminders
- `notes/backlog.md` - Items to address later
- `notes/tech-debt.md` - Technical debt tracking
- `notes/ideas.md` - Feature ideas

## Usage
When the user mentions something to remember, add it to the appropriate notes file:
- "Remember to fix the login page" → Add to session-notes.md
- "We should refactor this later" → Add to tech-debt.md
- "Idea: add keyboard shortcuts" → Add to ideas.md

## Prefixes
- `TODO:` - Action items
- `BUG:` - Bugs to fix
- `IDEA:` - Feature ideas
- `DEBT:` - Technical debt
- `NOTE:` - General observations