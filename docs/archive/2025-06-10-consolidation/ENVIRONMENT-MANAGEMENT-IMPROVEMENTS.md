# Environment Management Improvement Recommendations

## Current State Analysis

### What's Working Well
1. **Clear branch strategy**: `main` → staging, `production` → production
2. **Automated frontend deployments** via Vercel
3. **Consistent project IDs** for Supabase environments
4. **Health check endpoints** for monitoring

### Areas for Improvement
1. **Manual database migrations** - prone to human error
2. **Environment variable management** - scattered across multiple platforms
3. **No automated rollback mechanism** for failed deployments
4. **Limited environment validation** before deployment

## Recommended Improvements

### 1. Environment Configuration Centralization

Create a single source of truth for environment configuration:

```javascript
// config/environments.js
export const environments = {
  development: {
    name: 'Development',
    supabaseProjectId: null, // local
    url: 'http://localhost:5173',
    branch: null
  },
  staging: {
    name: 'Staging',
    supabaseProjectId: 'qnfmiotatmkoumlymynq',
    url: 'https://staging.fashionlab.tech',
    branch: 'main'
  },
  production: {
    name: 'Production',
    supabaseProjectId: 'cpelxqvcjnbpnphttzsn',
    url: 'https://app.fashionlab.tech',
    branch: 'production'
  }
};
```

### 2. Pre-deployment Validation Script

Create a validation script to run before deployments:

```bash
#!/bin/bash
# scripts/validate-deployment.sh

ENVIRONMENT=$1

# Check environment variables
echo "Validating environment: $ENVIRONMENT"

# Verify Supabase connection
supabase status

# Check for pending migrations
PENDING=$(supabase migration list | grep -c "pending")
if [ $PENDING -gt 0 ]; then
  echo "⚠️  Warning: $PENDING pending migrations"
fi

# Verify build
npm run build

echo "✅ Validation complete"
```

### 3. Migration Deployment Script

Automate the manual steps with safety checks:

```bash
#!/bin/bash
# scripts/deploy-migrations.sh

ENVIRONMENT=$1

if [ "$ENVIRONMENT" == "staging" ]; then
  PROJECT_ID="qnfmiotatmkoumlymynq"
elif [ "$ENVIRONMENT" == "production" ]; then
  PROJECT_ID="cpelxqvcjnbpnphttzsn"
  
  # Extra confirmation for production
  echo "⚠️  Deploying to PRODUCTION. Type 'DEPLOY' to confirm:"
  read CONFIRM
  if [ "$CONFIRM" != "DEPLOY" ]; then
    echo "Deployment cancelled"
    exit 1
  fi
else
  echo "Invalid environment: $ENVIRONMENT"
  exit 1
fi

# Link to project
supabase link --project-ref $PROJECT_ID

# Show pending migrations
echo "Pending migrations:"
supabase migration list

# Deploy
supabase db push

echo "✅ Migrations deployed to $ENVIRONMENT"
```

### 4. Environment-Specific Configuration Files

Standardize configuration file structure:

```
config/
├── .env.development      # Local development
├── .env.staging         # Staging (for local testing against staging)
├── .env.production      # Production (for local testing against prod)
└── environments.json    # Environment metadata
```

### 5. Deployment Runbook

Create a standardized runbook in `docs/DEPLOYMENT-RUNBOOK.md`:

```markdown
# Deployment Runbook

## Pre-deployment Checklist
- [ ] All tests pass
- [ ] Migrations tested locally
- [ ] Environment variables verified
- [ ] No console errors in development

## Deployment Steps

### Staging
1. Run validation: `npm run validate:staging`
2. Merge to main branch
3. Monitor Vercel deployment
4. Deploy migrations: `npm run migrate:staging`
5. Run smoke tests: `npm run test:staging`

### Production
1. Verify staging is stable (24h recommended)
2. Run validation: `npm run validate:production`
3. Create and merge PR
4. Monitor Vercel deployment
5. Deploy migrations: `npm run migrate:production`
6. Run smoke tests: `npm run test:production`
7. Monitor error tracking for 30 minutes
```

### 6. Package.json Scripts Enhancement

Add helpful scripts to package.json:

```json
{
  "scripts": {
    "validate:staging": "./scripts/validate-deployment.sh staging",
    "validate:production": "./scripts/validate-deployment.sh production",
    "migrate:staging": "./scripts/deploy-migrations.sh staging",
    "migrate:production": "./scripts/deploy-migrations.sh production",
    "env:check": "node scripts/check-env-vars.js",
    "deploy:status": "node scripts/deployment-status.js"
  }
}
```

### 7. Environment Status Dashboard

Create a simple status checking script:

```javascript
// scripts/deployment-status.js
import { environments } from '../config/environments.js';

async function checkEnvironment(env) {
  console.log(`\nChecking ${env.name}...`);
  
  // Check health endpoint
  try {
    const response = await fetch(`${env.url}/health.json`);
    const data = await response.json();
    console.log(`✅ Frontend: ${data.status}`);
  } catch (error) {
    console.log(`❌ Frontend: Error - ${error.message}`);
  }
  
  // Check Supabase (would need to implement API check)
  console.log(`📦 Supabase Project: ${env.supabaseProjectId}`);
}

// Check all environments
Object.values(environments).forEach(checkEnvironment);
```

### 8. Documentation Improvements

1. **Create environment-specific docs**:
   - `docs/environments/staging.md` - Staging-specific info
   - `docs/environments/production.md` - Production-specific info

2. **Add troubleshooting guides**:
   - `docs/troubleshooting/deployment-issues.md`
   - `docs/troubleshooting/migration-failures.md`

3. **Maintain deployment log**:
   - `docs/deployments/CHANGELOG.md` - Track all deployments

### 9. Future Automation Considerations

While keeping the current manual approach, prepare for future automation:

1. **Deployment notifications**: Set up Vercel webhooks to notify team
2. **Migration tracking**: Log all migration deployments
3. **Rollback procedures**: Document manual rollback steps
4. **Health monitoring**: Set up external monitoring for both environments

### 10. Environment Variable Management

Create a unified approach:

1. **Template files**:
   ```bash
   .env.example         # All possible variables with descriptions
   .env.staging.example # Staging-specific template
   .env.production.example # Production-specific template
   ```

2. **Validation script**:
   ```javascript
   // scripts/check-env-vars.js
   const required = ['VITE_SUPABASE_URL', 'VITE_SUPABASE_ANON_KEY'];
   const missing = required.filter(key => !process.env[key]);
   
   if (missing.length > 0) {
     console.error('Missing environment variables:', missing);
     process.exit(1);
   }
   ```

## Implementation Priority

1. **High Priority** (Do immediately):
   - Create deployment scripts
   - Add package.json scripts
   - Update documentation

2. **Medium Priority** (Next sprint):
   - Centralize environment configuration
   - Create validation scripts
   - Set up deployment runbook

3. **Low Priority** (Future):
   - Build status dashboard
   - Implement automated notifications
   - Consider partial automation

## Summary

These improvements maintain your current simplified approach while:
- Reducing human error through scripts and checklists
- Improving visibility into environment states
- Standardizing deployment procedures
- Preparing for future automation if needed

The key is to make the manual process as reliable and consistent as possible.