# Database Migration Guide

This guide covers database migration management for the Fashionlab platform using Supabase and GitHub Actions.

## 🚀 Quick Start

### Creating a New Migration

1. **Create migration file locally**:
   ```bash
   npm run supabase:migration:new "add_new_feature"
   ```

2. **Edit the generated file** in `supabase/migrations/`:
   ```sql
   -- Migration: Add new feature
   -- Rollback: DROP TABLE new_feature_table;
   
   CREATE TABLE public.new_feature_table (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     name TEXT NOT NULL,
     created_at TIMESTAMPTZ DEFAULT NOW()
   );
   
   -- Enable RLS
   ALTER TABLE public.new_feature_table ENABLE ROW LEVEL SECURITY;
   
   -- Add policies
   CREATE POLICY "Users can view their own records" ON public.new_feature_table
   FOR SELECT USING (auth.uid() = user_id);
   ```

3. **Test locally**:
   ```bash
   npm run supabase:start
   npm run supabase:reset
   # Verify your changes work
   npm run supabase:stop
   ```

4. **Pre-deployment validation** (optional but recommended):
   ```bash
   # Go to GitHub Actions → Pre-Deployment Checks → Run workflow
   # Select environment (staging/production) and run validation
   ```

5. **Deploy to staging** - Push to `main` branch:
   ```bash
   git push origin main
   ```

6. **Deploy to production** - Push to `production` branch:
   ```bash
   git push origin production
   ```

## 📋 Migration Workflows

### 1. Pre-Deployment Validation (Manual)
- **Trigger**: Manual workflow dispatch before deploying
- **Checks**: Code quality, migrations, breaking changes
- **Location**: `.github/workflows/pre-deployment-checks.yml`
- **Usage**: Run before pushing to `main` or `production`

### 2. Migration Validation (Automatic)
- **Trigger**: Push to `main` or `production` with migration changes
- **Checks**: File naming, conflicts, SQL syntax, breaking changes
- **Location**: `.github/workflows/migration-checks.yml`

### 3. Automatic Deployment
- **Staging**: Runs on push to `main` branch
- **Production**: Runs on push to `production` branch
- **Location**: `.github/workflows/deploy.yml`

### 4. Manual Rollback
- **Trigger**: Manual workflow dispatch
- **Purpose**: Emergency rollbacks
- **Location**: `.github/workflows/migration-rollback.yml`

### 5. Schema Monitoring
- **Trigger**: Daily schedule + manual
- **Purpose**: Detect schema drift
- **Location**: `.github/workflows/db-sync-check.yml`

## � Solo Developer Workflow

Since you're working as a solo developer without PRs, here's the recommended workflow:

### Before Making Changes
1. **Always test locally first**:
   ```bash
   npm run supabase:start
   npm run supabase:reset
   # Make your changes and test
   npm run supabase:stop
   ```

### Before Deploying
2. **Run pre-deployment validation** (optional but recommended):

   **Option A: Local validation script (faster)**:
   ```bash
   npm run validate:staging     # For staging deployment
   npm run validate:production  # For production deployment
   npm run validate            # Default: staging
   ```

   **Option B: GitHub Actions validation**:
   - Go to **GitHub Actions** → **Pre-Deployment Checks**
   - Click **Run workflow**
   - Select target environment (staging/production)

### Deploying
3. **Deploy to staging first**:
   ```bash
   git add .
   git commit -m "feat: add new migration"
   git push origin main  # Automatically deploys to staging
   ```

4. **Test on staging**, then deploy to production:
   ```bash
   git push origin production  # Deploys to production
   ```

### Key Benefits for Solo Development
- **No PR overhead** - Push directly to branches
- **Automatic validation** - Migrations are checked on push
- **Safety nets** - Breaking change detection for production
- **Easy rollbacks** - Manual workflow for emergencies
- **Monitoring** - Daily checks ensure everything stays in sync

## �🛠️ Local Development

### Essential Commands

```bash
# Start local Supabase
npm run supabase:start

# Check status
npm run supabase:status

# Reset database (applies all migrations)
npm run supabase:reset

# Create new migration
npm run supabase:migration:new "description"

# List migrations
npm run supabase:migration:list

# Check schema differences
npm run supabase:db:diff

# Validate before deploying
npm run validate              # Validate for staging
npm run validate:production   # Validate for production

# Stop Supabase
npm run supabase:stop
```

### Linking to Remote Projects

```bash
# Link to staging
npm run supabase:link:staging

# Link to production
npm run supabase:link:production
```

## 🔒 Security Best Practices

### Row Level Security (RLS)

Always enable RLS on new tables:

```sql
-- Enable RLS
ALTER TABLE public.your_table ENABLE ROW LEVEL SECURITY;

-- Add appropriate policies
CREATE POLICY "policy_name" ON public.your_table
FOR SELECT USING (
  -- Your security logic here
  auth.uid() = user_id
);
```

### Common Policy Patterns

```sql
-- Organization-based access
CREATE POLICY "org_members_access" ON public.table_name
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.organization_memberships om
    WHERE om.user_id = auth.uid()
    AND om.organization_id = table_name.organization_id
  )
);

-- Platform admin access
CREATE POLICY "platform_admin_access" ON public.table_name
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('platform_super', 'platform_admin')
  )
);
```

## ⚠️ Breaking Changes

### What Constitutes a Breaking Change?

- `DROP TABLE`
- `DROP COLUMN`
- `ALTER COLUMN ... TYPE` (without USING clause)
- `DROP INDEX` (on frequently used columns)
- `DROP CONSTRAINT`
- Removing RLS policies

### Handling Breaking Changes

1. **Plan the migration carefully**
2. **Test thoroughly on staging**
3. **Coordinate with frontend team**
4. **Have a rollback plan**
5. **Monitor after deployment**

## 🚨 Emergency Procedures

### Rolling Back a Migration

1. Go to **GitHub Actions** → **Migration Rollback**
2. Click **Run workflow**
3. Select environment (staging/production)
4. Enter target migration timestamp
5. Provide rollback reason
6. Type "CONFIRM"
7. Monitor the rollback process

### Schema Drift Resolution

If daily checks detect schema drift:

1. **Identify the differences**:
   ```bash
   npm run supabase:db:diff
   ```

2. **Create corrective migration**:
   ```bash
   npm run supabase:migration:new "fix_schema_drift"
   ```

3. **Test and deploy** following normal process

### Failed Migration Recovery

1. **Check GitHub Actions logs** for error details
2. **Fix the migration file** locally
3. **Test with local reset**:
   ```bash
   npm run supabase:reset
   ```
4. **Create new migration** with the fix
5. **Deploy the corrected version**

## 📊 Monitoring

### Daily Health Checks

- Schema drift detection
- RLS policy validation
- Migration status verification

### Manual Checks

```bash
# Check migration status
npm run supabase:migration:list

# Compare local vs remote schema
npm run supabase:db:diff

# View database logs
npm run supabase:logs
```

## 🔧 Troubleshooting

### Common Issues

**Migration file naming error**:
- Ensure format: `YYYYMMDDHHMMSS_description.sql`

**Timestamp conflicts**:
- Check for duplicate timestamps in migration files

**RLS policy errors**:
- Verify policies exist for all RLS-enabled tables

**Schema drift**:
- Run `supabase db diff` to see differences
- Create migration to sync schema

### Getting Help

1. Check GitHub Actions logs
2. Review this documentation
3. Test locally with `supabase reset`
4. Ask team for assistance

## 📚 Additional Resources

- [Supabase CLI Documentation](https://supabase.com/docs/guides/cli)
- [Supabase Migrations Guide](https://supabase.com/docs/guides/cli/local-development#database-migrations)
- [Row Level Security](https://supabase.com/docs/guides/auth/row-level-security)
- [GitHub Actions Workflows](../.github/workflows/)
