# Database Guide

This guide covers everything you need to know about working with the FashionLab database using Supabase and PostgreSQL.

## Quick Reference

### Local Database
```bash
# Start local Supabase
supabase start

# Connect to local database
psql -h localhost -p 54322 -d postgres -U postgres
# Password: postgres

# Reset local database
supabase db reset
```

### Staging Database
```bash
# Connect to staging
psql -h db.qnfmiotatmkoumlymynq.supabase.co -p 5432 -d postgres -U postgres
# Password: Get from Supabase dashboard

# Or use connection string
psql "postgresql://postgres:[PASSWORD]@db.qnfmiotatmkoumlymynq.supabase.co:5432/postgres"
```

### Production Database
```bash
# Connect to production (when configured)
psql -h db.[PROD-PROJECT-REF].supabase.co -p 5432 -d postgres -U postgres
# Password: Get from Supabase dashboard
```

## Common Operations

### Migrations

```bash
# Create new migration
supabase migration new descriptive_name

# Test locally
supabase db reset

# Link to staging
supabase link --project-ref qnfmiotatmkoumlymynq

# Push to staging
supabase db push

# Check migration status
supabase migration list
```

### Useful Queries

```bash
# List all tables
\dt public.*

# Describe table structure
\d public.assets

# Check RLS policies
SELECT tablename, policyname, cmd, roles 
FROM pg_policies 
WHERE schemaname = 'public' 
ORDER BY tablename;

# Check user roles
SELECT id, email, role 
FROM public.users 
LIMIT 10;

# Check organization memberships
SELECT u.email, o.name as organization 
FROM public.users u
JOIN public.organization_memberships om ON u.id = om.user_id
JOIN public.organizations o ON om.organization_id = o.id;
```

### Type Generation

```bash
# Generate TypeScript types from database
supabase gen types typescript --local > src/types/database.types.ts
```

## Environment Setup

### 1. Copy Environment Files
```bash
# For local development
cp .env.local.example .env.local

# For staging access
cp .env.staging.example .env.staging
# Edit .env.staging and add your password from Supabase dashboard
```

### 2. Using Connection Scripts
```bash
# Connect to local database
./scripts/db-connect.sh local

# Connect to staging database
./scripts/db-connect.sh staging

# Run quick queries
./scripts/db-query.sh local "SELECT COUNT(*) FROM users;"
./scripts/db-query.sh staging "SELECT * FROM organizations LIMIT 5;"
```

### 3. Direct Connection with Environment Variables
```bash
# Load environment variables
source .env.staging

# Connect using loaded variables
psql -h $SUPABASE_DB_HOST -p $SUPABASE_DB_PORT -d $SUPABASE_DB_NAME -U $SUPABASE_DB_USER
```

## Debugging Issues

### Check RLS Policies
```sql
-- See all policies for a table
SELECT * FROM pg_policies 
WHERE tablename = 'assets' 
AND schemaname = 'public';

-- Check if RLS is enabled
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';
```

### Test User Permissions
```sql
-- Switch to user context
SET LOCAL ROLE authenticated;
SET LOCAL request.jwt.claims.sub TO 'user-uuid-here';

-- Test query
SELECT * FROM assets LIMIT 5;

-- Reset
RESET ROLE;
```

### WITH CHECK Issues
```sql
-- Find UPDATE policies with restrictive WITH CHECK
SELECT 
  tablename,
  policyname,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
AND cmd = 'UPDATE'
AND with_check != 'true';
```

## Migration Best Practices

### 1. Always Test Locally First
```bash
supabase db reset
```

### 2. Use Proper Migration Patterns
```sql
-- Use IF EXISTS/IF NOT EXISTS
DROP POLICY IF EXISTS "old_policy" ON table_name;
CREATE TABLE IF NOT EXISTS table_name (...);

-- Add columns safely
ALTER TABLE table_name 
ADD COLUMN IF NOT EXISTS column_name type;

-- Check before creating
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE policyname = 'policy_name'
  ) THEN
    CREATE POLICY "policy_name" ON table_name ...
  END IF;
END $$;
```

### 3. Document Migrations
```sql
-- Always add comments
COMMENT ON POLICY "policy_name" ON table_name IS 
'Clear description of what this policy does';
```

## Emergency Procedures

### Reset Staging Database
```bash
# WARNING: Deletes all data!
supabase db reset --linked
```

### Fix Failed Migration
```sql
-- Check migration history
SELECT * FROM supabase_migrations.schema_migrations 
ORDER BY inserted_at DESC;

-- Manually mark migration as completed (last resort)
INSERT INTO supabase_migrations.schema_migrations (version) 
VALUES ('20250526000000');
```

## Environment Files

### Required Files
- `.env.local` - Local database configuration
- `.env.staging` - Staging database configuration (create from .env.staging.example)
- `.env.production.local` - Production database configuration (when ready)

### File Structure
```
# Example .env.staging
SUPABASE_DB_HOST=db.qnfmiotatmkoumlymynq.supabase.co
SUPABASE_DB_PORT=5432
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=postgres
SUPABASE_DB_PASSWORD=your_password_here
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
```

## Quick Tips

1. **Use Supabase Dashboard SQL Editor** for quick queries on staging/production
2. **Use local psql** for development and complex operations
3. **Always backup** before major changes
4. **Test migrations** locally before pushing
5. **Keep passwords secure** - never commit them
6. **Use transactions** for complex operations
7. **Document manual fixes** in migration files

## Common Commands Cheatsheet

```bash
# Local
psql -h localhost -p 54322 -d postgres -U postgres

# Staging
psql -h db.qnfmiotatmkoumlymynq.supabase.co -p 5432 -d postgres -U postgres

# Quick staging query
psql -h db.qnfmiotatmkoumlymynq.supabase.co -p 5432 -d postgres -U postgres -c "SELECT COUNT(*) FROM users;"

# Migrations
supabase migration new feature_name
supabase db reset
supabase db push

# Types
supabase gen types typescript --local > src/types/database.types.ts
```

## Related Documentation
- [Supabase CLI Documentation](https://supabase.com/docs/reference/cli)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [RLS Policy Patterns](./development/guides/rls-policy-patterns.md)