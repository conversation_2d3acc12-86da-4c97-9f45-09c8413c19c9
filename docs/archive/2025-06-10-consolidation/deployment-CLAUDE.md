# Deployment Guide

This file provides guidance specific to deployment processes in the FashionLab project.

## Environments

| Environment | Branch | Description |
|-------------|--------|-------------|
| Development | N/A | Local development environment |
| Staging | `main` | Staging environment for testing |
| Production | `production` | Production environment (not yet configured) |

## Deployment Process

### Automated Deployments with Vercel

The project uses Vercel for automated deployments:

1. **Staging Deployment**
   - Automatically triggered when changes are pushed to the `main` branch
   - Used for testing and QA before production release

2. **Production Deployment**
   - Triggered when changes are pushed to the `production` branch
   - Deploys to the production site at app.fashionlab.tech

```bash
# Deploying to production
git checkout production
git merge main
git push origin production
```

## Supabase Configuration

### Projects

| Environment | Project Name | Project ID |
|-------------|--------------|------------|
| Development | Local Docker | N/A |
| Staging | Fashionlab Platform | qnfmiotatmkoumlymynq |
| Production | Not yet configured | TBD |

**IMPORTANT**: The staging project ID is `qnfmiotatmkoumlymynq`. Always verify the project ID before running commands.

### Schema Migration Workflow

Database schema changes follow this workflow:

1. Make schema changes locally in development
2. Generate migrations for the changes
3. Push migrations to the staging environment
4. Test thoroughly in staging
5. When ready, push to production

```bash
# Generate migration after making local schema changes
npx supabase migration new migration_name

# Push migrations to staging
npx supabase db push -p qnfmiotatmkoumlymynq

# Push migrations to production (when ready)
npx supabase db push -p [production-id]
```

## Staging Database Access

### Connection Details

- **Project Ref**: `qnfmiotatmkoumlymynq`
- **Connection URL**: `postgresql://postgres.qnfmiotatmkoumlymynq:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:6543/postgres`

### Database Connection

```bash
# Connect to staging database
psql -h db.qnfmiotatmkoumlymynq.supabase.co -p 5432 -d postgres -U postgres
# Password: Get from Supabase dashboard

# Run a query on staging
psql -h db.qnfmiotatmkoumlymynq.supabase.co -p 5432 -d postgres -U postgres -c "SELECT * FROM users LIMIT 5;"

# Check migration status
supabase migration list
```

### Important Notes About Staging

- Staging does NOT get seed data automatically
- Manual creation of test users is required for staging
- Use `/scripts/create-staging-test-users.sql` to create test users after deployment

## Troubleshooting

### Common Issues

1. **Migration Failures**
   - Check for foreign key constraint errors
   - Verify migration order
   - Use psql to diagnose issues

2. **404/406 Errors in Frontend**
   - Check RLS policies
   - Verify user roles and permissions
   - Confirm that all required tables exist

3. **Authentication Issues**
   - Confirm test users exist in the staging database
   - Check organization membership records
   - Verify RLS policies for users table

## Deployment Checklist

Before deploying to production:

1. ✅ All features pass testing on staging
2. ✅ Database migrations run successfully
3. ✅ RLS policies work correctly
4. ✅ All user roles function as expected
5. ✅ Performance is acceptable
6. ✅ Documentation is updated

See @docs/database-guide.md for complete database reference.