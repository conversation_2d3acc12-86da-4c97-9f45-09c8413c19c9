# Deployment Setup

## Overview

FashionLab uses a simple deployment setup:
- **Frontend**: Vercel (automatic Git deployments)
- **Database**: Supabase (migrations via GitHub Actions)
- **Local Development**: Docker-based Supabase instance

## How It Works

### Frontend Deployment (Automatic via Vercel)
1. **Staging**: Push to `main` → Auto-deploys to staging.fashionlab.tech
2. **Production**: Push to `production` → Auto-deploys to app.fashionlab.tech

Vercel handles:
- Building the application
- Environment variables per branch
- Preview deployments for PRs
- CDN distribution

### Database Migrations (GitHub Actions)
The `supabase-migrations.yml` workflow automatically runs when you push to:
- `main` branch → Runs migrations on staging Supabase
- `production` branch → Runs migrations on production Supabase

## Required Setup

### 1. GitHub Secrets
Add this secret to your GitHub repository (Settings → Secrets → Actions):

```
SUPABASE_ACCESS_TOKEN        # Your Supabase personal access token
```

To get your Supabase access token:
1. Go to https://app.supabase.com/account/tokens
2. Generate a new access token
3. Copy and save it as a GitHub secret

### 2. Vercel Setup
Vercel should already be connected to your GitHub repository. Ensure:
- Main branch deploys to staging project
- Production branch deploys to production project
- Environment variables are set per project:
  - `VITE_SUPABASE_URL`
  - `VITE_SUPABASE_ANON_KEY`
  - `VITE_ENVIRONMENT` (staging/production)

## Deployment Workflow

### Deploy to Staging
```bash
# Make your changes
git add .
git commit -m "feat: your feature"
git push origin main

# Automatic:
# 1. Vercel builds and deploys to staging.fashionlab.tech
# 2. GitHub Actions runs database migrations
```

### Deploy to Production
```bash
# Ensure production is up to date
git checkout production
git merge main
git push origin production

# Automatic:
# 1. Vercel builds and deploys to app.fashionlab.tech
# 2. GitHub Actions runs database migrations
```

## Manual Migration Commands

If you need to run migrations manually:

```bash
# Staging
supabase link --project-ref qnfmiotatmkoumlymynq
supabase db push

# Production
supabase link --project-ref cpelxqvcjnbpnphttzsn
supabase db push
```

## Creating New Migrations

```bash
# Create a new migration
supabase migration new your_feature_name

# Test locally
supabase db reset

# Commit and push
git add supabase/migrations/
git commit -m "migration: add your_feature_name"
git push origin main
```

## Monitoring Deployments

### Frontend (Vercel)
- Check build status: https://vercel.com/dashboard
- View deployment logs in Vercel dashboard
- Preview deployments created for each PR

### Database Migrations (GitHub)
- Check Actions tab in GitHub repository
- Look for "Supabase Migrations" workflow
- Green check = migrations successful
- Red X = check logs for errors

## Troubleshooting

### Frontend deployment fails
1. Check Vercel dashboard for build errors
2. Verify environment variables are set
3. Check if `npm run build` works locally

### Migration fails
1. Check GitHub Actions logs
2. Verify migration works locally with `supabase db reset`
3. Ensure SUPABASE_ACCESS_TOKEN is valid
4. Check migration file syntax

### Common Issues
- **Migration already applied**: This is OK, migrations are idempotent
- **Build fails on Vercel**: Check for TypeScript errors or missing dependencies
- **Wrong environment**: Verify you're on the correct branch