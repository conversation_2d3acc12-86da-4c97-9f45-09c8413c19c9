# Deployment Guide

This document outlines the deployment process for the Fashionlab platform.

## 🚀 Deployment Overview

### Architecture
- **Frontend Hosting**: Vercel (automatic deployments)
- **Database/Backend**: Supabase
- **Version Control**: GitHub
- **Database Migrations**: Manual via Supabase CLI

### Branch Strategy
- **`main`** → Automatically deploys to **Staging** environment via Vercel
- **`production`** → Automatically deploys to **Production** environment via Vercel

### Environments
- **Staging**: https://staging.fashionlab.tech
- **Production**: https://app.fashionlab.tech

## 🔧 Setup Instructions

### 1. Vercel Configuration
Vercel is configured to automatically deploy:
- `main` branch → Staging environment
- `production` branch → Production environment

No GitHub Actions or manual intervention required for frontend deployments.

### 2. Environment Variables
Configure these in your Vercel project settings:

**Staging Environment:**
- `VITE_SUPABASE_URL`: Your staging Supabase URL
- `VITE_SUPABASE_ANON_KEY`: Your staging Supabase anon key
- `VITE_ENVIRONMENT`: `staging`

**Production Environment:**
- `VITE_SUPABASE_URL`: Your production Supabase URL
- `VITE_SUPABASE_ANON_KEY`: Your production Supabase anon key
- `VITE_ENVIRONMENT`: `production`

### 3. Supabase Projects
- **Staging Project ID**: `qnfmiotatmkoumlymynq`
- **Production Project ID**: `cpelxqvcjnbpnphttzsn`

## 📝 Development Workflow

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/your-feature-name
```

### 2. Deploy to Staging
```bash
# Merge PR to main branch
# Vercel automatically deploys frontend to staging

# Manually push database migrations
supabase link --project-ref qnfmiotatmkoumlymynq
supabase db push
```

### 3. Deploy to Production
```bash
# Create PR from main to production
gh pr create --base production --head main --title "Deploy: description"

# After merging, Vercel automatically deploys frontend

# Manually push database migrations
supabase link --project-ref cpelxqvcjnbpnphttzsn
supabase db push
```

## 🗄️ Database Migration Management

### Creating Migrations
```bash
# Create a new migration
supabase migration new descriptive_name

# Test locally
supabase db reset
```

### Migration Deployment Process

#### Staging Migrations
1. Ensure you're linked to staging:
   ```bash
   supabase link --project-ref qnfmiotatmkoumlymynq
   ```
2. Push migrations:
   ```bash
   supabase db push
   ```

#### Production Migrations
1. **Always test on staging first**
2. Link to production:
   ```bash
   supabase link --project-ref cpelxqvcjnbpnphttzsn
   ```
3. Push migrations:
   ```bash
   supabase db push
   ```

### Migration Best Practices

1. **Test locally first**: Always run `supabase db reset` before committing
2. **Use descriptive names**: `20250603120000_add_user_preferences.sql`
3. **Make migrations idempotent**: Use `IF EXISTS/IF NOT EXISTS` clauses
4. **Document rollback steps** in migration comments
5. **Never edit existing migrations**: Create new ones to fix issues

### Emergency Procedures

#### Failed Migration
1. Check error logs
2. Fix the migration locally
3. Test with `supabase db reset`
4. Create a new migration with the fix
5. Deploy the corrected version

#### Manual Rollback
If you need to rollback a migration:
1. Create a new migration that reverses the changes
2. Test the rollback migration locally
3. Deploy through normal process

## 🔍 Health Monitoring

The application includes a health check endpoint at `/health.json`:
```json
{
  "status": "healthy",
  "service": "fashionlab-frontend",
  "version": "1.0.0",
  "timestamp": "2025-06-03T12:00:00Z",
  "environment": "production"
}
```

Check health status:
- **Staging**: https://staging.fashionlab.tech/health.json
- **Production**: https://app.fashionlab.tech/health.json

## 🚨 Troubleshooting

### Frontend Deployment Issues
1. Check Vercel dashboard for build logs
2. Verify environment variables in Vercel settings
3. Test build locally: `npm run build`

### Database Migration Issues
1. Ensure correct project is linked: `supabase status`
2. Check migration syntax: `supabase db reset` (locally)
3. Verify Supabase access token is valid
4. Use direct database URL if connection fails:
   ```bash
   supabase db push --db-url "postgresql://postgres.<project-ref>:<password>@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"
   ```

### Common Issues
- **SASL Authentication Error**: Use the pooler connection string
- **Migration Already Applied**: Check with `supabase migration list`
- **Environment Mismatch**: Verify you're linked to the correct project

## 📋 Deployment Checklist

### For Staging
- [ ] Test changes locally
- [ ] Create and test database migrations
- [ ] Push to `main` branch
- [ ] Wait for Vercel deployment
- [ ] Manually push database migrations
- [ ] Verify staging deployment
- [ ] Test critical flows

### For Production
- [ ] Staging fully tested
- [ ] Create PR from `main` to `production`
- [ ] Review and approve PR
- [ ] Merge PR
- [ ] Wait for Vercel deployment
- [ ] Manually push database migrations
- [ ] Verify production deployment
- [ ] Monitor health endpoint

## 🔐 Security Notes

1. **Never commit secrets**: Use environment variables
2. **Database passwords**: Store securely, never in code
3. **Access tokens**: Rotate regularly
4. **Production access**: Limit to authorized personnel only

## 📚 Related Documentation

- [Simple Deployment Guide](./DEPLOYMENT-SIMPLE.md) - Quick reference
- [Database Migration Guide](./DATABASE_MIGRATIONS.md) - Detailed migration docs
- [Supabase CLI Guide](./deployment/supabase-cli-migration-guide.md) - CLI usage