# Simple Deployment Guide

## Overview

This guide covers the essential deployment workflow for FashionLab. We use:
- **GitHub** for code management  
- **Vercel** for automatic deployments (frontend)
- **Supabase** for database and backend
- **Manual database migrations** via Supabase CLI

## Branch Structure

- `main` - Staging environment (auto-deploys to staging)
- `production` - Production environment (auto-deploys to production)

## Deployment Workflow

### 1. Deploy to Staging

When you push to `main`, Ver<PERSON> automatically deploys the frontend to staging:
```bash
git add .
git commit -m "feat: your feature description"
git push origin main
```

**Important**: Database migrations must be pushed manually after frontend deployment (see Database Migrations section below).

### 2. Deploy to Production

Production deployments require a Pull Request (PR) from `main` to `production`:

#### Step 1: Create a PR
```bash
# Make sure main is up to date
git checkout main
git pull

# Create PR using GitHub CLI
gh pr create --base production --head main --title "Deploy: Your deployment title" --body "Description of changes"
```

#### Step 2: Review and Merge
1. Go to the PR link that was generated
2. Review the changes
3. Click "Merge pull request"
4. Vercel will automatically deploy the frontend to production
5. **Manually push database migrations** (see Database Migrations section below)

## Database Migrations

### Connect to Supabase

#### First Time Setup
1. Get your access token from https://app.supabase.com/account/tokens
2. Login to Supabase CLI:
```bash
supabase login --token <your-access-token>
```

#### Connect to Staging Database
```bash
# Get password from Supabase dashboard > Settings > Database
supabase link --project-ref qnfmiotatmkoumlymynq --password <staging-password>
```

#### Connect to Production Database
```bash
# Get password from Supabase dashboard > Settings > Database  
supabase link --project-ref cpelxqvcjnbpnphttzsn --password <production-password>
```

### Push Migrations

#### To Staging
```bash
# Using the linked connection
supabase db push

# Or with direct URL if link fails
supabase db push --db-url "postgresql://postgres.qnfmiotatmkoumlymynq:<password>@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"
```

#### To Production
```bash
# First, switch to production project
supabase link --project-ref cpelxqvcjnbpnphttzsn --password <production-password>

# Push migrations
supabase db push --db-url "postgresql://postgres.cpelxqvcjnbpnphttzsn:<password>@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"
```

### Create New Migrations
```bash
# Create a new migration file
supabase migration new your_migration_name

# Edit the file in supabase/migrations/
# Test locally first
supabase db reset
```

## Quick Reference

### Project IDs
- **Staging**: `qnfmiotatmkoumlymynq`
- **Production**: `cpelxqvcjnbpnphttzsn`

### URLs
- **Staging**: https://staging.fashionlab.tech
- **Production**: https://app.fashionlab.tech

### Common Commands
```bash
# Check git status
git status

# View unpushed commits
git log origin/main..HEAD --oneline

# Check which Supabase project is linked
supabase status

# List migrations
supabase migration list

# View PR details
gh pr view <pr-number>
```

## Troubleshooting

### Supabase Connection Issues
If you get SASL auth errors, use the direct database URL with the pooler connection:
```bash
supabase db push --db-url "postgresql://postgres.<project-ref>:<password>@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"
```

### Migration Conflicts
If production has migrations not in your local:
```bash
supabase migration repair --status reverted <migration-version> --db-url "<connection-string>"
```

### Can't Create PR
Make sure you have GitHub CLI installed and authenticated:
```bash
# Install GitHub CLI (Mac)
brew install gh

# Authenticate
gh auth login
```

## Complete Deployment Checklist

### Staging Deployment
- [ ] Test feature locally
- [ ] Run `supabase db reset` to test migrations locally
- [ ] Commit and push to `main`
- [ ] Wait for Vercel to deploy frontend to staging
- [ ] Manually push database migrations to staging: `supabase db push`
- [ ] Test on staging environment

### Production Deployment
- [ ] Ensure staging is fully tested
- [ ] Create PR from `main` to `production`
- [ ] Review and merge PR
- [ ] Wait for Vercel to deploy frontend to production
- [ ] Switch to production project: `supabase link --project-ref cpelxqvcjnbpnphttzsn`
- [ ] Manually push database migrations to production: `supabase db push`
- [ ] Verify production deployment

## Important Notes

1. **Always test on staging first** - Never push directly to production
2. **Database migrations are MANUAL** - You must explicitly run `supabase db push` after each deployment
3. **Test migrations locally first** - Always run `supabase db reset` before committing
4. **Keep passwords secure** - Never commit database passwords
5. **One PR at a time** - Avoid multiple production deployments simultaneously
6. **Order matters** - Deploy frontend first (automatic via Vercel), then push database migrations