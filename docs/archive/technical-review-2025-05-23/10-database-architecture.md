# Database Architecture

## Overview

This week saw significant database architecture changes, including the merge of the clients table into organizations, simplification of the role system, and numerous RLS policy updates. The database now follows a cleaner multi-tenant architecture with improved security boundaries.

## Major Structural Changes

### 1. Clients → Organizations Merge

**Before**: Separate tables
```sql
-- Old structure
CREATE TABLE clients (
  id UUID PRIMARY KEY,
  name TEXT,
  ...
);

CREATE TABLE organizations (
  id UUID PRIMARY KEY,
  name TEXT,
  client_id UUID REFERENCES clients(id),
  ...
);
```

**After**: Unified structure
```sql
-- New structure  
CREATE TABLE organizations (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT DEFAULT 'brand',
  logo_url TEXT,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
```

**Migration Impact**:
- All client references updated to organization
- Foreign keys updated across all tables
- UI terminology updated consistently

### 2. Role System Changes

**Before**: Dual roles
```sql
-- Role in two places
users.role 
organization_members.role
```

**After**: Single role
```sql
-- Single source of truth
users.role ONLY
-- organization_members just tracks membership
```

### 3. New Tables Added

```sql
-- Pending invitations for member management
CREATE TABLE pending_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL,
  organization_id UUID REFERENCES organizations(id),
  invited_by UUID REFERENCES users(id),
  role TEXT NOT NULL,
  expires_at TIMESTAMPTZ NOT NULL,
  accepted_at TIMESTAMPTZ,
  message TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Security activity logging
CREATE TABLE security_activity (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  event_type TEXT NOT NULL,
  event_data JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Bulk uploads tracking
CREATE TABLE bulk_uploads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  collection_id UUID REFERENCES collections(id),
  user_id UUID REFERENCES users(id),
  status TEXT DEFAULT 'pending',
  total_files INTEGER,
  processed_files INTEGER DEFAULT 0,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Comment mentions for notifications
CREATE TABLE comment_mentions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  comment_id UUID REFERENCES comments(id),
  mentioned_user_id UUID REFERENCES users(id),
  mentioned_by UUID REFERENCES users(id),
  read_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now()
);
```

## Current Schema Structure

### Core Tables Hierarchy
```
organizations
  └── organization_members
  └── collections
       └── products
            └── assets
                 └── comments
                      └── comment_mentions
       └── assets (collection-level)
            └── comments
                 └── comment_mentions
```

### User & Auth Tables
```
users (auth.users extended)
  └── organization_members
  └── pending_invitations
  └── security_activity
  └── notification_preferences
```

## RLS Policy Structure

### Current State: TEMPORARILY DISABLED ⚠️
```sql
-- All RLS disabled for demo
ALTER TABLE organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE collections DISABLE ROW LEVEL SECURITY;
-- etc...
```

### Intended Policy Pattern
```sql
-- Platform users can see everything
CREATE POLICY "Platform users full access"
ON table_name FOR ALL
USING (
  auth.uid() IN (
    SELECT id FROM users 
    WHERE role IN ('platform_super', 'platform_admin')
  )
);

-- Organization members see their data
CREATE POLICY "Organization members read access"
ON table_name FOR SELECT
USING (
  organization_id IN (
    SELECT organization_id 
    FROM organization_members 
    WHERE user_id = auth.uid()
  )
);

-- Admins can modify their org data
CREATE POLICY "Brand admins write access"
ON table_name FOR INSERT
USING (
  auth.uid() IN (
    SELECT id FROM users
    WHERE role = 'brand_admin'
  ) AND
  organization_id IN (
    SELECT organization_id
    FROM organization_members
    WHERE user_id = auth.uid()
  )
);
```

## Database Issues to Address

### 1. Migration Errors
```sql
-- This migration fails
UPDATE auth.config SET ...
-- Error: relation "auth.config" does not exist

-- Fix needed: Remove or update migration
```

### 2. Constraint Violations
Some migrations assume data exists that might not:
- Default roles that don't exist
- Foreign keys to missing records
- Unique constraints on nullable fields

### 3. Performance Concerns
Missing indexes on:
- `organization_members(user_id, organization_id)`
- `assets(collection_id, workflow_stage)`
- `comments(asset_id, created_at)`
- `pending_invitations(email, organization_id)`

### 4. Data Integrity
- Orphaned records after clients merge
- Inconsistent timestamp fields
- Missing CASCADE deletes

## What Needs to Be Done

### Immediate (Before Re-enabling RLS)

1. **Audit All Tables**
   ```sql
   -- Check for orphaned records
   SELECT a.* FROM assets a
   LEFT JOIN collections c ON a.collection_id = c.id
   WHERE c.id IS NULL;
   
   -- Check for invalid roles
   SELECT * FROM users 
   WHERE role NOT IN (
     'platform_super', 'platform_admin', 
     'brand_admin', 'brand_member',
     'external_retoucher', 'external_prompter'
   );
   ```

2. **Fix Migration Scripts**
   ```sql
   -- Add IF EXISTS checks
   DROP POLICY IF EXISTS "policy_name" ON table_name;
   
   -- Use proper role enums
   CREATE TYPE user_role AS ENUM (
     'platform_super', 'platform_admin',
     'brand_admin', 'brand_member',
     'external_retoucher', 'external_prompter'
   );
   ```

3. **Add Missing Indexes**
   ```sql
   CREATE INDEX idx_org_members_user 
   ON organization_members(user_id);
   
   CREATE INDEX idx_assets_collection_stage 
   ON assets(collection_id, workflow_stage);
   
   CREATE INDEX idx_comments_asset_created 
   ON comments(asset_id, created_at DESC);
   ```

4. **Implement Soft Deletes**
   ```sql
   ALTER TABLE organizations 
   ADD COLUMN deleted_at TIMESTAMPTZ;
   
   -- Update RLS to filter deleted
   CREATE POLICY "Hide deleted organizations"
   ON organizations FOR SELECT
   USING (deleted_at IS NULL);
   ```

### Future Improvements

1. **Audit Trail System**
   ```sql
   CREATE TABLE audit_log (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     table_name TEXT NOT NULL,
     record_id UUID NOT NULL,
     action TEXT NOT NULL,
     old_data JSONB,
     new_data JSONB,
     user_id UUID REFERENCES users(id),
     created_at TIMESTAMPTZ DEFAULT now()
   );
   ```

2. **Archival Strategy**
   ```sql
   -- Move old data to archive tables
   CREATE TABLE assets_archive (
     LIKE assets INCLUDING ALL
   );
   
   -- Partition by date
   CREATE TABLE assets_2025_q1 
   PARTITION OF assets 
   FOR VALUES FROM ('2025-01-01') TO ('2025-04-01');
   ```

3. **Multi-tenant Optimization**
   ```sql
   -- Row-level tenant isolation
   ALTER TABLE assets 
   ADD COLUMN tenant_id UUID 
   GENERATED ALWAYS AS (
     (SELECT organization_id FROM collections 
      WHERE id = assets.collection_id)
   ) STORED;
   
   CREATE INDEX idx_assets_tenant 
   ON assets(tenant_id);
   ```

## Testing Database Changes

### RLS Policy Testing
```bash
# Test as different user roles
PGPASSWORD=postgres psql -h localhost -p 54322 -U postgres -d postgres << EOF
-- Set user context
SET LOCAL role TO 'authenticated';
SET LOCAL request.jwt.claims TO '{"sub": "test-user-id"}';

-- Test queries
SELECT * FROM organizations;
SELECT * FROM collections;
EOF
```

### Migration Testing
```bash
# Reset and test migrations
supabase db reset --local
supabase test db
```

### Performance Testing
```sql
EXPLAIN ANALYZE
SELECT a.*, c.name as collection_name
FROM assets a
JOIN collections c ON a.collection_id = c.id
WHERE c.organization_id = 'org-uuid'
ORDER BY a.created_at DESC
LIMIT 50;
```

## Security Considerations

1. **SQL Injection Prevention**
   - Always use parameterized queries
   - Validate UUIDs before use
   - Sanitize JSONB inputs

2. **Data Isolation**
   - Verify RLS policies with different roles
   - Test cross-tenant data access
   - Audit privileged operations

3. **Backup Strategy**
   - Daily automated backups
   - Point-in-time recovery enabled
   - Test restore procedures

## Monitoring Requirements

### Key Metrics
- Query performance (p95 < 100ms)
- Connection pool usage
- RLS policy evaluation time
- Failed authentication attempts
- Database size growth

### Alerts Needed
- Slow queries > 1s
- Connection pool exhaustion  
- Failed migrations
- Unusual data access patterns
- Storage approaching limits

## Database Conventions

### Naming Conventions
```sql
-- Tables: plural, snake_case
CREATE TABLE organizations (...);

-- Columns: snake_case
created_at TIMESTAMPTZ

-- Indexes: idx_table_columns
CREATE INDEX idx_assets_collection_id

-- Foreign keys: table_column_fkey
CONSTRAINT assets_collection_id_fkey

-- Policies: "Role action" description
CREATE POLICY "Admins can update assets"
```

### Data Types
- IDs: UUID with gen_random_uuid()
- Timestamps: TIMESTAMPTZ
- Status/Type: TEXT with CHECK constraints
- Metadata: JSONB with validation

### Default Values
- created_at: now()
- updated_at: now() with trigger
- status fields: reasonable defaults
- JSONB: '{}' not NULL

## Related Documentation
- [RLS Policies & Security](./11-rls-policies-security.md)
- [Role System Simplification](./02-role-system-simplification.md)
- [Environment Management](./20-environment-management.md)