# Testing Infrastructure

## What We Built

A comprehensive testing framework using <PERSON><PERSON> for E2E tests and Vitest for unit tests, providing the foundation for reliable, maintainable test coverage across the platform.

## Testing Stack

### Tools Implemented
- **Playwright**: E2E testing framework
- **Vitest**: Unit testing (React Testing Library)
- **Testing Utilities**: Custom helpers and fixtures
- **Local Supabase**: Test database isolation

### Test Structure
```
e2e/
  ├── fixtures/           # Test data and users
  ├── helpers/           # Reusable test utilities
  ├── auth.spec.ts       # Authentication flows
  ├── assets.spec.ts     # Asset management
  ├── campaigns.spec.ts  # Campaign/collection tests
  └── permissions.spec.ts # Role-based access

src/
  └── test/
      ├── setup.ts       # Vitest configuration
      └── *.test.tsx     # Component unit tests
```

## Current Test Coverage

### E2E Tests Implemented
```typescript
// auth.spec.ts
- ✅ Login with valid credentials
- ✅ Logout flow
- ✅ Invalid credential handling
- ⚠️  Password reset flow (incomplete)
- ❌ Two-factor authentication (not implemented)

// assets.spec.ts  
- ✅ Upload single asset
- ✅ View asset details
- ✅ Add comments to assets
- ⚠️  Bulk operations (basic only)
- ❌ Asset workflow transitions

// permissions.spec.ts
- ✅ Role-based page access
- ✅ Admin-only features
- ❌ Cross-organization isolation
- ❌ API-level permission tests
```

### Unit Tests
- Limited coverage (~15%)
- Focus on utilities
- Component tests minimal
- No integration tests

## Test Patterns Established

### 1. **Test User Fixtures**
```typescript
// fixtures/users.ts
export const testUsers = {
  platformAdmin: {
    email: '<EMAIL>',
    password: 'testpass123',
    role: 'platform_admin'
  },
  brandAdmin: {
    email: '<EMAIL>',
    password: 'testpass123',
    role: 'brand_admin',
    organizationId: 'test-org-123'
  },
  brandMember: {
    email: '<EMAIL>',
    password: 'testpass123',
    role: 'brand_member',
    organizationId: 'test-org-123'
  }
};
```

### 2. **Authentication Helper**
```typescript
// helpers/auth.ts
export async function loginAs(
  page: Page, 
  user: TestUser
): Promise<void> {
  await page.goto('/login');
  await page.fill('[name="email"]', user.email);
  await page.fill('[name="password"]', user.password);
  await page.click('button[type="submit"]');
  await page.waitForURL('/dashboard');
}
```

### 3. **Page Object Pattern** (Partial)
```typescript
class AssetDetailPage {
  constructor(private page: Page) {}
  
  async addComment(text: string) {
    await this.page.fill('[name="comment"]', text);
    await this.page.click('button:has-text("Add Comment")');
  }
  
  async verifyComment(text: string) {
    await expect(this.page.locator('.comment-text'))
      .toContainText(text);
  }
}
```

## Current Issues

### 1. **Test Data Management**
- No automatic cleanup
- Test data pollutes database
- Manual reset required
- No data factories

### 2. **Flaky Tests**
```typescript
// Common flakiness patterns
await page.click('button'); // No wait
await page.waitForTimeout(2000); // Arbitrary waits
expect(element).toBeVisible(); // Race conditions
```

### 3. **Missing Test Types**
- No API tests
- No performance tests
- No visual regression tests
- No accessibility tests

### 4. **Environment Issues**
- Tests run against development DB
- No CI/CD pipeline
- Local-only execution
- No parallel execution

## What Needs to Be Done

### Immediate (Test Reliability)

1. **Test Isolation**
   ```typescript
   // Create test context
   test.beforeEach(async ({ page }) => {
     // Create fresh test data
     const testOrg = await createTestOrganization();
     const testUser = await createTestUser(testOrg);
     
     // Login
     await loginAs(page, testUser);
     
     // Store context
     test.info().annotations.push({
       type: 'testContext',
       description: JSON.stringify({ testOrg, testUser })
     });
   });
   
   test.afterEach(async () => {
     // Cleanup
     const context = getTestContext();
     await cleanupTestOrganization(context.testOrg.id);
   });
   ```

2. **Robust Selectors**
   ```typescript
   // Bad: Fragile selectors
   await page.click('.MuiButton-root:nth-child(2)');
   
   // Good: Semantic selectors
   await page.click('[data-testid="submit-button"]');
   await page.click('button:has-text("Submit")');
   await page.getByRole('button', { name: 'Submit' });
   ```

3. **Wait Strategies**
   ```typescript
   // Bad: Fixed timeouts
   await page.waitForTimeout(2000);
   
   // Good: Wait for conditions
   await page.waitForLoadState('networkidle');
   await page.waitForSelector('[data-loaded="true"]');
   await expect(page.locator('.spinner')).toBeHidden();
   ```

### Test Coverage Expansion

1. **Critical User Journeys**
   ```typescript
   test.describe('Asset Upload Journey', () => {
     test('complete workflow from upload to approval', async ({ page }) => {
       // 1. Upload asset
       await uploadAsset(page, 'test-image.jpg');
       
       // 2. Add metadata
       await addAssetMetadata(page, {
         title: 'Test Asset',
         tags: ['test', 'demo']
       });
       
       // 3. Submit for review
       await submitForReview(page);
       
       // 4. Switch to admin
       await loginAs(page, testUsers.brandAdmin);
       
       // 5. Approve asset
       await approveAsset(page);
       
       // 6. Verify final state
       await verifyAssetStatus(page, 'approved');
     });
   });
   ```

2. **API Testing**
   ```typescript
   // api/assets.test.ts
   test('API: Create asset requires auth', async ({ request }) => {
     const response = await request.post('/api/assets', {
       data: { title: 'Test' }
     });
     
     expect(response.status()).toBe(401);
   });
   
   test('API: Users cannot access other org assets', async ({ request }) => {
     const response = await request.get('/api/assets/other-org-asset-id', {
       headers: { Authorization: `Bearer ${userToken}` }
     });
     
     expect(response.status()).toBe(403);
   });
   ```

3. **Visual Regression**
   ```typescript
   test('Asset detail page visual consistency', async ({ page }) => {
     await page.goto('/assets/test-asset-id');
     await page.waitForLoadState('networkidle');
     
     await expect(page).toHaveScreenshot('asset-detail.png', {
       fullPage: true,
       animations: 'disabled',
       mask: [page.locator('.timestamp')] // Mask dynamic content
     });
   });
   ```

### Test Infrastructure

1. **Test Database**
   ```yaml
   # docker-compose.test.yml
   services:
     test-db:
       image: supabase/postgres:14
       environment:
         POSTGRES_DB: test
       ports:
         - "54323:5432"
   ```

2. **CI/CD Pipeline**
   ```yaml
   # .github/workflows/test.yml
   name: Tests
   on: [push, pull_request]
   
   jobs:
     test:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         
         - name: Setup
           run: |
             npm ci
             npx playwright install
         
         - name: Run unit tests
           run: npm run test:unit
         
         - name: Run E2E tests
           run: npm run test:e2e
         
         - name: Upload results
           uses: actions/upload-artifact@v3
           with:
             name: test-results
             path: test-results/
   ```

3. **Test Reporting**
   ```typescript
   // playwright.config.ts
   export default {
     reporter: [
       ['html', { open: 'never' }],
       ['junit', { outputFile: 'test-results/junit.xml' }],
       ['json', { outputFile: 'test-results/results.json' }]
     ],
     use: {
       screenshot: 'only-on-failure',
       video: 'retain-on-failure',
       trace: 'on-first-retry'
     }
   };
   ```

## Test Best Practices

### 1. **Test Structure**
```typescript
test.describe('Feature: Asset Management', () => {
  test.describe('Upload', () => {
    test('single file upload', async ({ page }) => {
      // Arrange
      const file = 'test-image.jpg';
      
      // Act
      await uploadFile(page, file);
      
      // Assert
      await expect(page.locator('[data-testid="upload-success"]'))
        .toBeVisible();
    });
  });
});
```

### 2. **Test Data Builders**
```typescript
class AssetBuilder {
  private asset = {
    title: 'Test Asset',
    collection_id: 'test-collection',
    workflow_stage: 'draft'
  };
  
  withTitle(title: string) {
    this.asset.title = title;
    return this;
  }
  
  inCollection(collectionId: string) {
    this.asset.collection_id = collectionId;
    return this;
  }
  
  async build() {
    const { data } = await supabase
      .from('assets')
      .insert(this.asset)
      .select()
      .single();
    return data;
  }
}

// Usage
const asset = await new AssetBuilder()
  .withTitle('Product Shot')
  .inCollection(testCollection.id)
  .build();
```

### 3. **Custom Matchers**
```typescript
// Custom Playwright matchers
expect.extend({
  async toHaveRole(received: Page, role: string) {
    const userRole = await received.getAttribute('[data-user-role]');
    
    return {
      pass: userRole === role,
      message: () => `Expected role ${role}, got ${userRole}`
    };
  }
});

// Usage
await expect(page).toHaveRole('brand_admin');
```

## Performance Testing

```typescript
test('Gallery loads within performance budget', async ({ page }) => {
  const metrics = await page.evaluate(() => {
    return JSON.stringify(performance.getEntriesByType('navigation'));
  });
  
  const navigation = JSON.parse(metrics)[0];
  
  expect(navigation.loadEventEnd - navigation.loadEventStart)
    .toBeLessThan(3000); // 3s budget
  
  expect(navigation.domContentLoadedEventEnd)
    .toBeLessThan(1500); // 1.5s DOMContentLoaded
});
```

## Accessibility Testing

```typescript
test('Asset page meets WCAG standards', async ({ page }) => {
  await page.goto('/assets/test-id');
  
  const violations = await checkA11y(page);
  
  expect(violations).toHaveLength(0);
});

async function checkA11y(page: Page) {
  await injectAxe(page);
  return await page.evaluate(() => axe.run());
}
```

## Test Metrics to Track

- Test execution time
- Flakiness rate
- Coverage percentage
- Failed test patterns
- Time to fix failures

## Testing Checklist

### Before Each Feature
- [ ] Write test plan
- [ ] Create test data builders
- [ ] Add data-testid attributes
- [ ] Write happy path tests
- [ ] Write error case tests
- [ ] Add visual regression tests

### Before Each Release
- [ ] All tests passing
- [ ] Coverage > 80%
- [ ] No flaky tests
- [ ] Performance budgets met
- [ ] Accessibility passing
- [ ] Security tests passing

## Related Documentation
- [Coding Standards](./30-coding-standards.md)
- [CI/CD Pipeline](./21-deployment-workflow.md)
- [Performance Optimization](./31-lessons-learned.md)