# Member Invitation System

## What We Built

A complete email-based invitation system allowing organization administrators to invite new team members to join their brand on the FashionLab platform.

## What Changed

### Database Schema
- Added `pending_invitations` table with email, role, expiration tracking
- Added `message` column for personalized invitation messages
- Created relationship with organizations and users tables

### Email Integration
- Integrated Resend as email service provider
- Created Supabase Edge Function for email delivery
- Designed professional HTML email templates

### UI Components
- `InviteMemberForm.tsx` - Form for creating invitations
- `PendingInvitationsTable.tsx` - Managing sent invitations
- `InvitationAccept.tsx` - Landing page for invitation acceptance

### Authentication Flow
- Modified signup flow to handle invitation tokens
- Auto-assignment of organization membership on signup
- Email confirmation handling

## Why We Changed It

### Business Requirements
- Brands needed self-service team management
- Reduce manual onboarding overhead
- Professional appearance for external collaborators

### Technical Requirements
- Secure, time-limited invitations
- Audit trail for compliance
- Scalable email delivery

## Design Decisions

### 1. **Supabase Edge Functions for Email**
- **Why**: Serverless, integrated with auth
- **Alternative considered**: Direct Resend API calls
- **Decision**: Edge Functions provide better security and monitoring

### 2. **7-Day Expiration**
- **Why**: Balance between security and user convenience
- **Alternative**: No expiration or 24-hour limit
- **Decision**: 7 days allows for weekend/holiday scenarios

### 3. **Single-Use Tokens**
- **Why**: Prevent invitation link sharing
- **Alternative**: Multi-use with limits
- **Decision**: Single-use is more secure

### 4. **Role Assignment at Invitation**
- **Why**: Admins know intended role upfront
- **Alternative**: Role assignment after joining
- **Decision**: Reduces steps and confusion

## Current Issues

### 1. **RLS Policies**
```sql
-- Currently disabled, needs proper implementation
CREATE POLICY "Invitees can view their invitation"
ON pending_invitations FOR SELECT
USING (
  email = current_setting('request.jwt.claims')::json->>'email'
  OR auth.uid() IN (
    SELECT user_id FROM organization_members 
    WHERE organization_id = pending_invitations.organization_id 
    AND role = 'admin'
  )
);
```

### 2. **Email Delivery in Development**
- Inbucket integration works but is clunky
- Need better local testing solution

### 3. **Error Handling**
- Generic error messages don't help users
- Need specific messages for common scenarios

## What Needs to Be Done

### Immediate (Before Production)
1. **Re-enable RLS policies** with proper testing
2. **Add invitation analytics** - track open rates, acceptance rates
3. **Implement retry mechanism** for failed emails
4. **Add "resend invitation" functionality**
5. **Better error messages** for expired/used invitations

### Future Improvements
1. **Bulk invitations** - CSV upload for multiple members
2. **Custom email templates** per organization
3. **Invitation preview** before sending
4. **Two-factor authentication** for sensitive roles
5. **Slack/Teams integration** for notifications

## Testing Checklist

- [ ] Admin can create invitation
- [ ] Email is delivered within 30 seconds
- [ ] Invitation link works correctly
- [ ] Expired invitations show proper message
- [ ] Used invitations cannot be reused
- [ ] New user gets correct role and organization
- [ ] Existing user gets added to organization
- [ ] RLS policies prevent unauthorized access
- [ ] Email formatting works across clients

## Code Quality Improvements Needed

```typescript
// Current: Scattered error handling
try {
  // invite logic
} catch (error) {
  toast({ title: "Error", description: error.message });
}

// Better: Specific error handling
try {
  // invite logic
} catch (error) {
  if (error.code === 'duplicate_invitation') {
    toast({ 
      title: "Already invited", 
      description: "This email has already been invited to your organization" 
    });
  } else if (error.code === 'invalid_email') {
    toast({ 
      title: "Invalid email", 
      description: "Please check the email address and try again" 
    });
  } else {
    // generic fallback
  }
}
```

## Security Considerations

1. **Rate limiting** needed on invitation creation
2. **Email verification** before sending invitations
3. **Audit logging** for all invitation actions
4. **GDPR compliance** - data retention policies

## Performance Optimization

1. **Batch database operations** when checking existing invitations
2. **Cache organization member lists**
3. **Optimize email template size**

## Metrics to Track

- Invitation sent count
- Acceptance rate
- Time to accept
- Bounce rate
- Role distribution

## Related Documentation
- [Email Integration](./05-email-integration.md)
- [RLS Policies](./11-rls-policies-security.md)
- [Database Architecture](./10-database-architecture.md)