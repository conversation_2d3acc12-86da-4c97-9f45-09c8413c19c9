# Email Integration

## What We Built

A complete email delivery system using Resend as the provider and Supabase Edge Functions for secure server-side sending, supporting transactional emails for invitations, notifications, and user communications.

## What Changed

### Architecture
**Before**: No email capability
**After**: Full email system with:
- Resend integration for delivery
- Supabase Edge Functions for security
- HTML email templates
- Local testing with Inbucket
- Error handling and retry logic

### Implementation Details
```typescript
// Edge Function: send-email
export async function sendEmail(
  to: string,
  subject: string,
  html: string,
  from?: string
) {
  const resend = new Resend(RESEND_API_KEY);
  
  return await resend.emails.send({
    from: from || 'FashionLab <<EMAIL>>',
    to,
    subject,
    html,
  });
}
```

### Email Templates
- Professional HTML design
- Mobile responsive
- Brand consistency
- Dark/light mode support

## Why We Changed It

### Business Requirements
1. **User Invitations**: Core feature requirement
2. **Notifications**: Keep users informed
3. **Professional Image**: Branded communications
4. **Compliance**: Audit trail needs

### Technical Requirements
1. **Security**: API keys not in frontend
2. **Reliability**: Delivery guarantees
3. **Scalability**: Handle growth
4. **Testing**: Local development needs

## Design Decisions

### 1. **Resend vs Other Providers**
**Options Considered**:
- SendGrid: More complex, expensive
- AWS SES: Better for high volume
- Postmark: Similar to Resend

**Decision**: Resend
**Why**: 
- Simple API
- Good developer experience
- Competitive pricing
- React Email support

### 2. **Edge Functions vs Direct API**
**Decision**: Edge Functions
**Why**:
- Secure API key storage
- Rate limiting capability
- Centralized logging
- Future webhook support

### 3. **HTML Templates**
```html
<table cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px;">
  <tr>
    <td style="background-color: #030712; padding: 40px;">
      <!-- Email content -->
    </td>
  </tr>
</table>
```
**Why**: 
- Table layout for compatibility
- Inline styles for consistency
- Mobile-first responsive design

### 4. **Local Testing Strategy**
**Decision**: Inbucket
**Why**:
- Captures all emails locally
- Web UI for viewing
- No external dependencies
- Docker-based setup

## Current Issues

### 1. **Local Development Friction**
```typescript
// Current: Environment detection is clunky
const isLocal = process.env.NODE_ENV === 'development';
const emailEndpoint = isLocal 
  ? 'http://localhost:54321/functions/v1/send-email'
  : 'https://project.supabase.co/functions/v1/send-email';
```

### 2. **Template Management**
- Templates hardcoded in Edge Function
- No versioning or A/B testing
- Difficult to update

### 3. **Error Handling**
- Generic error messages
- No retry mechanism
- Limited debugging info

### 4. **Tracking**
- No open rates
- No click tracking
- No delivery confirmation

## What Needs to Be Done

### Immediate (Production Ready)

1. **Implement Retry Logic**
   ```typescript
   async function sendEmailWithRetry(
     params: EmailParams,
     maxRetries = 3
   ): Promise<EmailResponse> {
     let lastError: Error;
     
     for (let attempt = 0; attempt < maxRetries; attempt++) {
       try {
         return await sendEmail(params);
       } catch (error) {
         lastError = error;
         
         // Exponential backoff
         if (attempt < maxRetries - 1) {
           await new Promise(r => 
             setTimeout(r, Math.pow(2, attempt) * 1000)
           );
         }
       }
     }
     
     throw lastError;
   }
   ```

2. **Add Email Queue**
   ```sql
   CREATE TABLE email_queue (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     to_email TEXT NOT NULL,
     subject TEXT NOT NULL,
     template TEXT NOT NULL,
     params JSONB,
     status TEXT DEFAULT 'pending',
     attempts INT DEFAULT 0,
     created_at TIMESTAMPTZ DEFAULT now(),
     sent_at TIMESTAMPTZ
   );
   ```

3. **Template System**
   ```typescript
   interface EmailTemplate {
     name: string;
     subject: string;
     html: string;
     text?: string;
     variables: string[];
   }
   
   const templates: Record<string, EmailTemplate> = {
     invitation: {
       name: 'Member Invitation',
       subject: 'You\'ve been invited to {{organizationName}}',
       html: loadTemplate('invitation.html'),
       variables: ['organizationName', 'inviterName', 'acceptUrl']
     }
   };
   ```

4. **Better Error Messages**
   ```typescript
   class EmailError extends Error {
     constructor(
       message: string,
       public code: string,
       public details?: any
     ) {
       super(message);
     }
   }
   
   // Usage
   throw new EmailError(
     'Invalid recipient email',
     'INVALID_RECIPIENT',
     { email: to }
   );
   ```

### Future Enhancements

1. **Email Analytics**
   - Open tracking pixels
   - Click tracking redirects
   - Bounce handling
   - Unsubscribe management

2. **Advanced Templates**
   - MJML for better compatibility
   - Dynamic content blocks
   - Personalization engine
   - A/B testing framework

3. **Notification Preferences**
   ```sql
   CREATE TABLE notification_preferences (
     user_id UUID REFERENCES users(id),
     email_invitations BOOLEAN DEFAULT true,
     email_comments BOOLEAN DEFAULT true,
     email_mentions BOOLEAN DEFAULT true,
     email_weekly_digest BOOLEAN DEFAULT false,
     PRIMARY KEY (user_id)
   );
   ```

4. **Webhook Processing**
   ```typescript
   // Handle Resend webhooks
   export async function handleEmailWebhook(
     event: ResendWebhookEvent
   ) {
     switch (event.type) {
       case 'email.sent':
         await markEmailSent(event.data.email_id);
         break;
       case 'email.bounced':
         await handleBounce(event.data);
         break;
     }
   }
   ```

## Testing Strategy

### Unit Tests
```typescript
describe('Email Service', () => {
  it('sends invitation email', async () => {
    const mockSend = jest.fn().mockResolvedValue({ id: '123' });
    
    await sendInvitationEmail({
      to: '<EMAIL>',
      organizationName: 'Test Org',
      inviterName: 'Admin User',
      acceptUrl: 'https://app.com/accept/abc'
    });
    
    expect(mockSend).toHaveBeenCalledWith({
      to: '<EMAIL>',
      subject: 'You\'ve been invited to Test Org',
      html: expect.stringContaining('Admin User')
    });
  });
});
```

### Integration Tests
```typescript
test('invitation flow sends email', async ({ page }) => {
  // Monitor Inbucket API
  const emails = await getInbucketEmails();
  const beforeCount = emails.length;
  
  // Trigger invitation
  await page.goto('/organization/members');
  await page.click('text=Invite Member');
  await page.fill('[name=email]', '<EMAIL>');
  await page.click('text=Send Invitation');
  
  // Verify email sent
  await page.waitForTimeout(1000);
  const afterEmails = await getInbucketEmails();
  expect(afterEmails.length).toBe(beforeCount + 1);
});
```

## Email Templates Best Practices

### Structure
```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{subject}}</title>
  <!--[if mso]>
  <noscript>
    <xml>
      <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  </noscript>
  <![endif]-->
</head>
<body style="margin: 0; padding: 0; background-color: #f3f4f6;">
  <!-- Content -->
</body>
</html>
```

### Styling
```css
/* Use inline styles */
style="
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 16px;
  line-height: 24px;
  color: #1f2937;
"
```

### Accessibility
- Semantic HTML structure
- Alt text for images
- Sufficient color contrast
- Clear call-to-action buttons

## Performance Considerations

### Optimize Delivery
1. **Batch sending** for multiple recipients
2. **Priority queues** for time-sensitive emails
3. **Regional endpoints** for global delivery
4. **Connection pooling** for high volume

### Monitor Performance
```typescript
interface EmailMetrics {
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  bounced: number;
  complained: number;
}

async function getEmailMetrics(
  timeRange: TimeRange
): Promise<EmailMetrics> {
  // Query metrics from database or Resend API
}
```

## Security Considerations

1. **API Key Rotation**
   - Regular key rotation
   - Separate keys per environment
   - Audit key usage

2. **Input Validation**
   ```typescript
   function validateEmail(email: string): boolean {
     const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
     return regex.test(email);
   }
   ```

3. **Rate Limiting**
   ```typescript
   const rateLimiter = new RateLimiter({
     points: 10, // emails
     duration: 60, // per minute
     blockDuration: 600, // 10 min block
   });
   ```

4. **Content Security**
   - Sanitize user inputs
   - Prevent email injection
   - Validate template variables

## Cost Management

### Resend Pricing
- Free: 100 emails/day
- Pro: $20/mo for 10k emails
- Scale: Custom pricing

### Optimization Strategies
1. Batch similar emails
2. Deduplicate recipients
3. Implement unsubscribe
4. Monitor bounce rates

## Related Documentation
- [Member Invitation System](./01-member-invitation-system.md)
- [Environment Management](./20-environment-management.md)
- [Security Best Practices](./11-rls-policies-security.md)