# Role System Simplification

## What We Built

A unified single-role system replacing the previous dual-role architecture, where roles are stored only in the `users` table and organization membership is tracked separately.

## What Changed

### Before: Dual-Role System
```typescript
// Roles in two places
users.role: 'admin' | 'user'
organization_members.role: 'admin' | 'member' | 'viewer'
```

### After: Single-Role System
```typescript
// Single source of truth
users.role: 'platform_super' | 'platform_admin' | 'brand_admin' | 'brand_member' | 'external_retoucher' | 'external_prompter'

// Organization membership is just association
organization_members: { user_id, organization_id } // no role field
```

### Code Changes
- Removed role field from `organization_members` table
- Updated all RLS policies to check `users.role`
- Created `useUserRole` hook for consistent role checking
- Simplified permission logic throughout the app

## Why We Changed It

### Problems with Dual-Role System
1. **Confusion**: Which role takes precedence?
2. **Conflicts**: User could be admin in users table but member in organization
3. **Maintenance**: Two places to update roles
4. **Bugs**: Inconsistent role checks across codebase

### Benefits of Single-Role System
1. **Clarity**: One source of truth
2. **Simplicity**: Easier to understand and maintain
3. **Performance**: Fewer joins in queries
4. **Security**: Clearer permission boundaries

## Design Decisions

### 1. **Hierarchical Role Structure**
```
platform_super
  └── platform_admin
       └── brand_admin
            └── brand_member
                 └── external_retoucher
                      └── external_prompter
```

**Why**: Clear hierarchy makes permission checks intuitive

### 2. **Organization Association Only**
```sql
-- organization_members table
CREATE TABLE organization_members (
  user_id UUID REFERENCES users(id),
  organization_id UUID REFERENCES organizations(id),
  joined_at TIMESTAMPTZ DEFAULT now(),
  PRIMARY KEY (user_id, organization_id)
);
```

**Why**: Membership is binary - you're either in or out

### 3. **Role-Based UI Components**
```typescript
const { isBrandAdmin, isPlatformUser } = useUserRole();

if (isBrandAdmin) {
  // Show admin features
}
```

**Why**: Consistent role checking across components

## Current Issues

### 1. **Migration Complexity**
- Some RLS policies still reference old dual-role logic
- Need to audit all database functions
- Legacy data might have inconsistencies

### 2. **External User Permissions**
- External roles (retoucher, prompter) need clearer boundaries
- Cross-organization access patterns undefined

### 3. **Role Elevation**
- No clear path for role upgrades
- Platform admins must manually change roles

## What Needs to Be Done

### Immediate (Critical Path)
1. **Audit all RLS policies** for dual-role references
   ```sql
   -- Find policies checking organization_members.role
   SELECT * FROM pg_policies 
   WHERE definition LIKE '%organization_members%role%';
   ```

2. **Update remaining components** using old role checks
   ```bash
   grep -r "organization_members.*role" src/
   ```

3. **Create role migration script** for existing data
   ```sql
   -- Migrate brand admins
   UPDATE users u
   SET role = 'brand_admin'
   FROM organization_members om
   WHERE u.id = om.user_id
   AND om.role = 'admin';
   ```

4. **Document role permissions** clearly
   - What each role can do
   - Which UI elements they see
   - API endpoint access

### Future Improvements
1. **Granular Permissions**
   - Feature flags per organization
   - Custom permission sets
   
2. **Role Templates**
   - Pre-defined permission sets
   - Easy role creation

3. **Audit Trail**
   - Track all role changes
   - Who changed what and when

4. **Temporary Permissions**
   - Time-limited elevated access
   - Project-based permissions

## Testing Requirements

### Unit Tests
```typescript
describe('useUserRole', () => {
  it('correctly identifies brand admin', () => {
    const user = { role: 'brand_admin' };
    const { isBrandAdmin } = useUserRole(user);
    expect(isBrandAdmin).toBe(true);
  });
});
```

### E2E Tests
- User with each role can access appropriate pages
- Permission denials show correct messages
- Role changes take effect immediately

### RLS Policy Tests
```sql
-- Test brand member can only see their org
SET LOCAL role TO 'authenticated';
SET LOCAL request.jwt.claims TO '{"sub": "user-uuid", "role": "brand_member"}';
SELECT * FROM collections; -- Should only return their org's collections
```

## Security Considerations

1. **Role Enumeration**: Don't expose all users' roles publicly
2. **Privilege Escalation**: Validate role changes server-side
3. **Session Management**: Clear cache on role change
4. **API Security**: Double-check roles in Edge Functions

## Performance Impact

### Positive
- Fewer joins in queries
- Simpler RLS policies = faster checks
- Cached role data in React context

### Negative
- More complex role hierarchy checks
- Need to fetch user data more often

### Optimization Opportunities
```typescript
// Cache role checks
const roleCache = new Map();

function checkRole(userId: string): Role {
  if (roleCache.has(userId)) {
    return roleCache.get(userId);
  }
  // Fetch and cache
}
```

## Code Patterns

### Good Pattern
```typescript
// Centralized role checking
const canEditCollection = (user: User, collection: Collection) => {
  return isPlatformUser(user) || 
    (isBrandAdmin(user) && user.organizationId === collection.organizationId);
};
```

### Bad Pattern
```typescript
// Scattered role logic
if (user.role === 'platform_super' || user.role === 'platform_admin' || 
    (user.role === 'brand_admin' && user.orgId === collection.orgId)) {
  // Edit collection
}
```

## Migration Checklist

- [ ] Remove role column from organization_members
- [ ] Update all TypeScript types
- [ ] Fix all RLS policies
- [ ] Update all React components
- [ ] Migrate existing user data
- [ ] Update documentation
- [ ] Train support team on new system

## Rollback Plan

If issues arise:
1. Keep old role column (don't drop)
2. Dual-write during transition
3. Feature flag for new vs old system
4. Gradual rollout by organization

## Related Documentation
- [Database Architecture](./10-database-architecture.md)
- [RLS Policies](./11-rls-policies-security.md)
- [Testing Infrastructure](./12-testing-infrastructure.md)