# Coding Standards

## Overview

Based on this week's development experience, these coding standards will help maintain consistency, prevent common issues, and improve code quality across the FashionLab platform.

## TypeScript Standards

### 1. **Type Safety**
```typescript
// ❌ Bad: Any types
function processData(data: any) {
  return data.map((item: any) => item.name);
}

// ✅ Good: Explicit types
interface DataItem {
  id: string;
  name: string;
  value: number;
}

function processData(data: DataItem[]): string[] {
  return data.map(item => item.name);
}
```

### 2. **Null Handling**
```typescript
// ❌ Bad: Assuming values exist
function getUserName(user: User) {
  return user.profile.name.toUpperCase();
}

// ✅ Good: Safe access
function getUserName(user: User): string {
  return user.profile?.name?.toUpperCase() ?? 'Unknown';
}
```

### 3. **Enum vs Union Types**
```typescript
// ❌ Bad: String enums (runtime overhead)
enum UserRole {
  Admin = 'admin',
  Member = 'member'
}

// ✅ Good: Union types (type-only)
type UserRole = 'platform_super' | 'platform_admin' | 'brand_admin' | 'brand_member';

// Or const assertion for runtime access
const USER_ROLES = {
  PLATFORM_SUPER: 'platform_super',
  PLATFORM_ADMIN: 'platform_admin',
  BRAND_ADMIN: 'brand_admin',
  BRAND_MEMBER: 'brand_member'
} as const;

type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];
```

## React Standards

### 1. **Component Structure**
```typescript
// ✅ Good: Consistent component structure
interface AssetCardProps {
  asset: Asset;
  onSelect?: (asset: Asset) => void;
  isSelected?: boolean;
}

export function AssetCard({ 
  asset, 
  onSelect,
  isSelected = false 
}: AssetCardProps) {
  // 1. Hooks
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  
  // 2. Derived state
  const canEdit = useMemo(() => 
    user?.role === 'brand_admin', 
    [user?.role]
  );
  
  // 3. Event handlers
  const handleClick = useCallback(() => {
    onSelect?.(asset);
  }, [asset, onSelect]);
  
  // 4. Effects
  useEffect(() => {
    // Effect logic
  }, []);
  
  // 5. Render
  return (
    <div onClick={handleClick}>
      {/* Component JSX */}
    </div>
  );
}
```

### 2. **State Management**
```typescript
// ❌ Bad: Multiple useState for related data
const [name, setName] = useState('');
const [email, setEmail] = useState('');
const [role, setRole] = useState('');

// ✅ Good: Single state for related data
interface UserForm {
  name: string;
  email: string;
  role: UserRole;
}

const [formData, setFormData] = useState<UserForm>({
  name: '',
  email: '',
  role: 'brand_member'
});

// Update helper
const updateField = (field: keyof UserForm, value: string) => {
  setFormData(prev => ({ ...prev, [field]: value }));
};
```

### 3. **Custom Hooks**
```typescript
// ✅ Good: Reusable logic in custom hooks
function useAssetUpload(collectionId: string) {
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<Error | null>(null);
  
  const upload = useCallback(async (file: File) => {
    setIsUploading(true);
    setError(null);
    
    try {
      // Upload logic with progress updates
      const result = await uploadAsset(file, collectionId, setProgress);
      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsUploading(false);
    }
  }, [collectionId]);
  
  return { upload, isUploading, progress, error };
}
```

## Database Standards

### 1. **Query Patterns**
```typescript
// ❌ Bad: N+1 queries
const collections = await supabase.from('collections').select('*');
for (const collection of collections.data) {
  const assets = await supabase
    .from('assets')
    .select('*')
    .eq('collection_id', collection.id);
}

// ✅ Good: Single query with joins
const { data, error } = await supabase
  .from('collections')
  .select(`
    *,
    assets (
      id,
      title,
      workflow_stage
    )
  `)
  .eq('organization_id', orgId);
```

### 2. **Error Handling**
```typescript
// ✅ Good: Consistent error handling
async function fetchAssets(collectionId: string) {
  try {
    const { data, error } = await supabase
      .from('assets')
      .select('*')
      .eq('collection_id', collectionId)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Database error:', error);
      throw new Error(`Failed to fetch assets: ${error.message}`);
    }
    
    return data;
  } catch (error) {
    // Log to monitoring
    logError('fetchAssets', error, { collectionId });
    throw error;
  }
}
```

### 3. **Type Generation**
```typescript
// ✅ Good: Use generated types
import { Database } from '@/types/database.types';

type Asset = Database['public']['Tables']['assets']['Row'];
type AssetInsert = Database['public']['Tables']['assets']['Insert'];
type AssetUpdate = Database['public']['Tables']['assets']['Update'];
```

## API Standards

### 1. **Response Format**
```typescript
// ✅ Good: Consistent API responses
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    version: string;
  };
}

async function apiHandler<T>(
  handler: () => Promise<T>
): Promise<ApiResponse<T>> {
  try {
    const data = await handler();
    return {
      success: true,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }
    };
  } catch (error) {
    return {
      success: false,
      error: {
        code: error.code || 'UNKNOWN_ERROR',
        message: error.message || 'An error occurred',
        details: error.details
      }
    };
  }
}
```

### 2. **Input Validation**
```typescript
// ✅ Good: Validate inputs
import { z } from 'zod';

const CreateAssetSchema = z.object({
  title: z.string().min(1).max(255),
  collection_id: z.string().uuid(),
  metadata: z.record(z.any()).optional()
});

async function createAsset(input: unknown) {
  // Validate input
  const validated = CreateAssetSchema.parse(input);
  
  // Process with validated data
  return await processAsset(validated);
}
```

## File Organization

### 1. **Folder Structure**
```
src/
├── components/          # Reusable UI components
│   ├── common/         # Generic components
│   ├── forms/          # Form components
│   └── layout/         # Layout components
├── features/           # Feature-specific components
│   ├── assets/
│   ├── collections/
│   └── organizations/
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── types/              # TypeScript types
├── lib/                # External library configs
└── pages/              # Route components
```

### 2. **File Naming**
```typescript
// Components: PascalCase
AssetCard.tsx
BulkUploadDialog.tsx

// Utilities: camelCase
imageProcessor.ts
dateHelpers.ts

// Types: PascalCase with .types suffix
Database.types.ts
Asset.types.ts

// Hooks: camelCase with use prefix
useAssets.ts
useOrganization.ts
```

### 3. **Export Patterns**
```typescript
// ✅ Good: Named exports for components
export function AssetCard() { }
export function AssetGrid() { }

// ✅ Good: Default export for pages
export default function DashboardPage() { }

// ✅ Good: Barrel exports for features
// features/assets/index.ts
export * from './AssetCard';
export * from './AssetGrid';
export * from './AssetUpload';
```

## Testing Standards

### 1. **Test Structure**
```typescript
// ✅ Good: Descriptive test structure
describe('AssetCard', () => {
  describe('when user is admin', () => {
    it('should show edit button', () => {
      // Arrange
      const asset = createMockAsset();
      const user = createMockUser({ role: 'brand_admin' });
      
      // Act
      const { getByRole } = render(
        <AssetCard asset={asset} />,
        { user }
      );
      
      // Assert
      expect(getByRole('button', { name: 'Edit' })).toBeInTheDocument();
    });
  });
});
```

### 2. **Test Utilities**
```typescript
// ✅ Good: Reusable test utilities
export function createMockAsset(overrides?: Partial<Asset>): Asset {
  return {
    id: 'test-asset-id',
    title: 'Test Asset',
    collection_id: 'test-collection-id',
    workflow_stage: 'draft',
    created_at: new Date().toISOString(),
    ...overrides
  };
}

export function renderWithProviders(
  ui: React.ReactElement,
  options?: RenderOptions
) {
  return render(
    <QueryClient>
      <AuthProvider>
        {ui}
      </AuthProvider>
    </QueryClient>,
    options
  );
}
```

## Performance Standards

### 1. **Memoization**
```typescript
// ✅ Good: Memoize expensive computations
const expensiveFilter = useMemo(() => {
  return assets.filter(asset => 
    asset.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
    asset.workflow_stage === filterStage
  );
}, [assets, searchTerm, filterStage]);

// ✅ Good: Memoize callbacks
const handleSelect = useCallback((asset: Asset) => {
  onSelect?.(asset);
}, [onSelect]);
```

### 2. **Lazy Loading**
```typescript
// ✅ Good: Lazy load heavy components
const BulkUploadDialog = lazy(() => 
  import('./BulkUploadDialog')
);

// With loading boundary
<Suspense fallback={<Spinner />}>
  <BulkUploadDialog />
</Suspense>
```

### 3. **Image Optimization**
```typescript
// ✅ Good: Progressive image loading
function ProgressiveImage({ asset }: { asset: Asset }) {
  const [loaded, setLoaded] = useState(false);
  
  return (
    <>
      {!loaded && (
        <img 
          src={getAssetUrl(asset, 'thumbnail')}
          alt={asset.title}
          className="blur-sm"
        />
      )}
      <img
        src={getAssetUrl(asset, 'compressed')}
        alt={asset.title}
        onLoad={() => setLoaded(true)}
        className={loaded ? 'opacity-100' : 'opacity-0'}
      />
    </>
  );
}
```

## Security Standards

### 1. **Input Sanitization**
```typescript
// ✅ Good: Sanitize user input
import DOMPurify from 'isomorphic-dompurify';

function sanitizeHtml(dirty: string): string {
  return DOMPurify.sanitize(dirty, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a'],
    ALLOWED_ATTR: ['href']
  });
}
```

### 2. **Authentication Checks**
```typescript
// ✅ Good: Verify auth at multiple levels
async function deleteAsset(assetId: string) {
  // 1. Frontend check
  if (!user || user.role !== 'brand_admin') {
    throw new Error('Unauthorized');
  }
  
  // 2. API will verify via RLS
  const { error } = await supabase
    .from('assets')
    .delete()
    .eq('id', assetId);
  
  if (error) {
    // Handle authorization error
  }
}
```

## Documentation Standards

### 1. **Component Documentation**
```typescript
/**
 * AssetCard displays a single asset with preview and actions.
 * 
 * @example
 * <AssetCard 
 *   asset={asset}
 *   onSelect={handleSelect}
 *   isSelected={selectedIds.includes(asset.id)}
 * />
 */
interface AssetCardProps {
  /** The asset to display */
  asset: Asset;
  /** Called when asset is selected */
  onSelect?: (asset: Asset) => void;
  /** Whether the asset is currently selected */
  isSelected?: boolean;
}
```

### 2. **Function Documentation**
```typescript
/**
 * Processes an image file to generate compressed and thumbnail versions
 * 
 * @param file - The original image file
 * @param options - Processing options
 * @returns Object containing processed image URLs
 * @throws {Error} If file is not a valid image
 * 
 * @example
 * const processed = await processImage(file, {
 *   maxWidth: 1200,
 *   quality: 0.8
 * });
 */
async function processImage(
  file: File, 
  options: ProcessOptions
): Promise<ProcessedImage> {
  // Implementation
}
```

## Git Standards

### 1. **Commit Messages**
```bash
# ✅ Good: Descriptive commits
git commit -m "Fix asset upload error handling for large files"
git commit -m "Add bulk delete functionality to asset management"
git commit -m "Refactor role checking to use single source of truth"

# ❌ Bad: Vague commits
git commit -m "Fix bug"
git commit -m "Update files"
git commit -m "WIP"
```

### 2. **Branch Naming**
```bash
# Features
feature/bulk-upload-wizard
feature/member-invitation-system

# Fixes
fix/asset-upload-memory-leak
fix/rls-policy-errors

# Refactoring
refactor/single-role-system
refactor/storage-bucket-structure
```

## Code Review Checklist

Before submitting PR:
- [ ] TypeScript types are explicit (no `any`)
- [ ] Error handling is comprehensive
- [ ] Component follows structure pattern
- [ ] Database queries are optimized
- [ ] Tests are included
- [ ] Documentation is updated
- [ ] No console.log statements
- [ ] Security considerations addressed
- [ ] Performance impact considered
- [ ] Accessibility requirements met

## Related Documentation
- [Testing Infrastructure](./12-testing-infrastructure.md)
- [Database Architecture](./10-database-architecture.md)
- [Component Best Practices](./04-ui-ux-improvements.md)