# Environment Management

## Overview

Managing multiple environments (local, staging, production) with different configurations, databases, and services. This week revealed several environment-specific issues that need systematic solutions.

## Current Environment Setup

### Local Development
```bash
# Supabase local instance
http://localhost:54321     # Supabase Studio
http://localhost:54322     # PostgreSQL
http://localhost:54323     # Supabase Auth
http://localhost:54324     # Inbucket (email testing)

# Frontend
http://localhost:8080      # Vite dev server (configured in vite.config.ts)
```

### Staging
```bash
# Supabase project
https://qnfmiotatmkoumlymynq.supabase.co
# Deployed app
https://fashionlab-staging.vercel.app
```

### Production
```bash
# Not yet configured
# Reserved: https://app.fashionlab.com
```

## Environment Variables

### Current Structure
```bash
# .env.local
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_RESEND_API_KEY=your-resend-key

# .env.staging  
VITE_SUPABASE_URL=https://qnfmiotatmkoumlymynq.supabase.co
VITE_SUPABASE_ANON_KEY=staging-anon-key

# Missing: Service role keys, proper env separation
```

### Required Structure
```bash
# .env.local
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=  # For migrations
VITE_APP_URL=http://localhost:8080
VITE_ENVIRONMENT=local

# .env.staging
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=
VITE_APP_URL=https://fashionlab-staging.vercel.app
VITE_ENVIRONMENT=staging
VITE_ENABLE_ANALYTICS=false

# .env.production
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=
VITE_APP_URL=https://app.fashionlab.com
VITE_ENVIRONMENT=production
VITE_ENABLE_ANALYTICS=true
VITE_SENTRY_DSN=
```

## Database Management

### Local Database
```bash
# Start local Supabase
supabase start

# Reset database (applies all migrations)
supabase db reset

# Direct connection
PGPASSWORD=postgres psql -h localhost -p 54322 -U postgres -d postgres

# View logs
supabase logs --follow
```

### Staging Database
```bash
# Connect via Supabase CLI
supabase db remote commit --project-ref qnfmiotatmkoumlymynq

# Direct connection (requires password)
psql postgresql://postgres:[password]@db.qnfmiotatmkoumlymynq.supabase.co:5432/postgres

# Run migrations
supabase db push --project-ref qnfmiotatmkoumlymynq
```

### Database Sync Issues
```sql
-- Staging has different state than local
-- Missing indexes, different constraints
-- Solution: Full migration audit needed
```

## Email Service Configuration

### Local (Inbucket)
```typescript
// Automatically catches all emails
const emailUrl = 'http://localhost:54324';
// View at: http://localhost:54324
```

### Staging/Production (Resend)
```typescript
const resend = new Resend(process.env.RESEND_API_KEY);
// Different API keys per environment
// Different "from" addresses
```

## Storage Configuration

### Local Storage
- Files stored in `.supabase/storage`
- Automatically cleared on reset
- No S3 compatibility

### Staging/Production Storage
- Supabase Storage (S3 compatible)
- CDN enabled
- Different bucket policies

## Current Issues

### 1. **Migration Drift**
Local and staging databases out of sync:
```sql
-- Local has all migrations
-- Staging missing some migrations
-- Production not yet created
```

### 2. **Configuration Sprawl**
```typescript
// Hardcoded environment checks everywhere
if (window.location.hostname === 'localhost') {
  // local logic
} else {
  // production logic
}
```

### 3. **Secret Management**
- Secrets in .env files
- No rotation strategy
- Shared across team

### 4. **Data Inconsistency**
- Test data in staging
- No data sync strategy
- Manual seed scripts

## Solutions Implemented

### 1. **Environment Detection**
```typescript
// utils/environment.ts
export const env = {
  isLocal: import.meta.env.VITE_ENVIRONMENT === 'local',
  isStaging: import.meta.env.VITE_ENVIRONMENT === 'staging',
  isProduction: import.meta.env.VITE_ENVIRONMENT === 'production',
  isDevelopment: import.meta.env.DEV,
  
  // Feature flags
  features: {
    emailEnabled: !import.meta.env.VITE_DISABLE_EMAIL,
    debugMode: import.meta.env.VITE_DEBUG === 'true',
    maintenanceMode: import.meta.env.VITE_MAINTENANCE === 'true'
  }
};
```

### 2. **Database Connection Helper**
```typescript
// scripts/db-connect.ts
export function getDbUrl(env: 'local' | 'staging' | 'production') {
  switch (env) {
    case 'local':
      return 'postgresql://postgres:postgres@localhost:54322/postgres';
    case 'staging':
      return process.env.STAGING_DATABASE_URL;
    case 'production':
      return process.env.DATABASE_URL;
  }
}
```

### 3. **Migration Management**
```bash
# scripts/sync-staging.sh
#!/bin/bash
echo "Syncing staging database..."

# Export local schema
supabase db dump --local > local-schema.sql

# Apply to staging
supabase db push --project-ref qnfmiotatmkoumlymynq

# Verify
supabase db diff --project-ref qnfmiotatmkoumlymynq
```

## What Needs to Be Done

### Immediate (Environment Stability)

1. **Environment Parity**
   ```yaml
   # docker-compose.yml for exact parity
   version: '3.8'
   services:
     postgres:
       image: supabase/postgres:*********
       environment:
         - POSTGRES_PASSWORD=postgres
     
     storage:
       image: supabase/storage-api:v0.40.4
   ```

2. **Configuration Service**
   ```typescript
   class ConfigService {
     private config: EnvironmentConfig;
     
     constructor() {
       this.config = this.loadConfig();
       this.validateConfig();
     }
     
     get(key: string): any {
       return this.config[key];
     }
     
     private validateConfig() {
       const required = ['SUPABASE_URL', 'SUPABASE_ANON_KEY'];
       for (const key of required) {
         if (!this.config[key]) {
           throw new Error(`Missing required config: ${key}`);
         }
       }
     }
   }
   ```

3. **Secret Management**
   ```bash
   # Use Vercel secrets for staging/prod
   vercel secrets add supabase-url "https://..."
   vercel secrets add supabase-anon-key "eyJ..."
   
   # Reference in vercel.json
   {
     "env": {
       "VITE_SUPABASE_URL": "@supabase-url",
       "VITE_SUPABASE_ANON_KEY": "@supabase-anon-key"
     }
   }
   ```

### Future Improvements

1. **Infrastructure as Code**
   ```terraform
   # infrastructure/main.tf
   resource "supabase_project" "main" {
     name = "fashionlab-${var.environment}"
     region = "us-east-1"
     
     database_password = var.db_password
   }
   ```

2. **Environment Promotion**
   ```bash
   # Automated promotion pipeline
   npm run promote:staging-to-prod
   # 1. Run tests
   # 2. Backup production
   # 3. Apply migrations
   # 4. Deploy code
   # 5. Smoke tests
   # 6. Rollback if needed
   ```

3. **Feature Flags**
   ```typescript
   // Dynamic feature control
   const features = await getFeatureFlags(userId);
   
   if (features.enabled('new-upload-flow')) {
     return <NewBulkUpload />;
   } else {
     return <LegacyUpload />;
   }
   ```

## Monitoring & Logging

### Local Development
```typescript
// Enhanced console logging
if (env.isLocal) {
  console.log('%c[API]', 'color: blue', request);
  console.log('%c[DB]', 'color: green', query);
}
```

### Staging/Production
```typescript
// Structured logging
import { logger } from './utils/logger';

logger.info('User action', {
  userId: user.id,
  action: 'asset.upload',
  metadata: { fileSize, fileType }
});

// Sentry for errors
Sentry.init({
  dsn: env.SENTRY_DSN,
  environment: env.ENVIRONMENT,
  beforeSend(event) {
    if (env.isLocal) return null;
    return event;
  }
});
```

## Database Backup Strategy

### Local
```bash
# Backup before major changes
pg_dump -h localhost -p 54322 -U postgres postgres > backup-$(date +%Y%m%d).sql
```

### Staging
```bash
# Daily automated backups
supabase db dump --project-ref qnfmiotatmkoumlymynq > staging-$(date +%Y%m%d).sql
```

### Production
- Automated daily backups
- Point-in-time recovery
- Cross-region replication
- Tested restore procedures

## Deployment Checklist

### Pre-deployment
- [ ] All tests passing
- [ ] Migrations tested on staging
- [ ] Environment variables set
- [ ] Secrets rotated if needed
- [ ] Backup created

### Deployment
- [ ] Deploy database migrations
- [ ] Deploy edge functions
- [ ] Deploy frontend
- [ ] Run smoke tests
- [ ] Monitor error rates

### Post-deployment
- [ ] Verify all features working
- [ ] Check performance metrics
- [ ] Monitor error logs
- [ ] Update status page
- [ ] Notify team

## Emergency Procedures

### Database Rollback
```bash
# 1. Stop application
vercel --prod --env production scale 0

# 2. Restore backup
psql $DATABASE_URL < backup.sql

# 3. Restart application
vercel --prod --env production scale 1
```

### Feature Rollback
```typescript
// Quick disable via env var
if (process.env.VITE_DISABLE_FEATURE_X === 'true') {
  return <Fallback />;
}
```

## Cost Management

### Tracking by Environment
- Local: $0 (Docker resources)
- Staging: ~$25/month
  - Database: $25
  - Storage: Minimal
  - Functions: Free tier
- Production: ~$200-500/month
  - Database: $150
  - Storage: $50-200
  - Functions: $0-100
  - CDN: $0-50

### Optimization Strategies
1. Archive old data
2. Compress images aggressively
3. Cache API responses
4. Use connection pooling
5. Monitor usage alerts

## Related Documentation
- [Deployment Workflow](./21-deployment-workflow.md)
- [Database Architecture](./10-database-architecture.md)
- [Monitoring & Debugging](./22-debugging-techniques.md)