# Deployment Workflow

## Current Deployment Setup

### Automated Deployments
- **Staging**: Auto-deploy from `main` branch
- **Production**: Manual deploy from `production` branch (not yet active)
- **Preview**: PR deployments on Vercel

### Infrastructure
- **Frontend**: Vercel
- **Backend**: Supabase
- **Email**: Resend (via Edge Functions)
- **Monitoring**: Browser console (needs improvement)

## Deployment Pipeline

### Current Flow
```mermaid
graph LR
    A[Local Dev] -->|git push| B[GitHub Main]
    B -->|webhook| C[Vercel Build]
    C -->|success| D[Staging Deploy]
    
    E[Database Changes] -->|manual| F[Supabase CLI]
    F -->|supabase db push| G[Staging DB]
```

### Issues with Current Flow
1. Database migrations are manual
2. No automated tests before deploy
3. No rollback strategy
4. Environment variables managed separately

## Deployment Process

### 1. Frontend Deployment (Vercel)

#### Automatic (Staging)
```bash
# Any push to main triggers deployment
git push origin main

# Vercel automatically:
# 1. Installs dependencies
# 2. Runs build
# 3. Deploys to staging
# 4. Updates DNS
```

#### Manual (Production)
```bash
# Not yet configured, but would be:
git checkout production
git merge main
git push origin production
```

### 2. Database Deployment (Supabase)

#### Local to Staging
```bash
# 1. Test migrations locally
supabase db reset

# 2. Generate migration
supabase migration new feature_name

# 3. Push to staging
supabase db push --project-ref qnfmiotatmkoumlymynq

# 4. Verify
supabase db remote status --project-ref qnfmiotatmkoumlymynq
```

#### Migration Best Practices
```sql
-- Always use IF EXISTS/IF NOT EXISTS
DROP POLICY IF EXISTS "old_policy" ON table_name;
CREATE TABLE IF NOT EXISTS new_table (...);

-- Make migrations idempotent
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'new_column'
  ) THEN
    ALTER TABLE users ADD COLUMN new_column TEXT;
  END IF;
END $$;
```

### 3. Edge Functions Deployment

```bash
# Deploy all functions
supabase functions deploy --project-ref qnfmiotatmkoumlymynq

# Deploy specific function
supabase functions deploy send-email --project-ref qnfmiotatmkoumlymynq

# Set secrets
supabase secrets set RESEND_API_KEY=your-key --project-ref qnfmiotatmkoumlymynq
```

## What Needs to Be Done

### Immediate (Production-Ready Pipeline)

1. **Automated Testing Gate**
   ```yaml
   # .github/workflows/deploy.yml
   name: Deploy Pipeline
   on:
     push:
       branches: [main]
   
   jobs:
     test:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         - run: npm ci
         - run: npm run test
         - run: npm run test:e2e
     
     deploy:
       needs: test
       if: success()
       steps:
         - name: Trigger Vercel Deploy
           run: |
             curl -X POST $VERCEL_DEPLOY_HOOK
   ```

2. **Database Migration Automation**
   ```yaml
   # .github/workflows/migrate.yml
   name: Database Migrations
   on:
     push:
       paths:
         - 'supabase/migrations/**'
   
   jobs:
     migrate-staging:
       steps:
         - uses: supabase/setup-cli@v1
         - name: Run migrations
           run: |
             supabase db push \
               --project-ref ${{ secrets.STAGING_PROJECT_REF }} \
               --password ${{ secrets.STAGING_DB_PASSWORD }}
   ```

3. **Environment Promotion**
   ```bash
   #!/bin/bash
   # scripts/promote-to-production.sh
   
   echo "🚀 Promoting staging to production..."
   
   # 1. Run production tests
   npm run test:production
   
   # 2. Backup production database
   supabase db dump --project-ref $PROD_REF > backup-$(date +%Y%m%d).sql
   
   # 3. Apply migrations
   supabase db push --project-ref $PROD_REF
   
   # 4. Deploy functions
   supabase functions deploy --project-ref $PROD_REF
   
   # 5. Update Vercel
   vercel --prod
   
   # 6. Run smoke tests
   npm run test:smoke
   
   echo "✅ Deployment complete!"
   ```

### Deployment Checklist

#### Pre-Deployment
- [ ] All PRs approved and merged
- [ ] Tests passing on main
- [ ] No critical bugs in staging
- [ ] Database migrations reviewed
- [ ] Release notes prepared

#### Database Changes
- [ ] Migrations tested locally
- [ ] Backwards compatible changes
- [ ] Indexes added for new queries
- [ ] RLS policies updated
- [ ] Backup created

#### Code Deployment
- [ ] Feature flags configured
- [ ] Environment variables set
- [ ] API version compatible
- [ ] Bundle size checked
- [ ] Error tracking configured

#### Post-Deployment
- [ ] Smoke tests passed
- [ ] Key features verified
- [ ] Performance metrics normal
- [ ] Error rate acceptable
- [ ] Team notified

## Rollback Procedures

### Frontend Rollback
```bash
# Vercel instant rollback
vercel rollback

# Or via dashboard
# 1. Go to Vercel dashboard
# 2. Select deployment
# 3. Click "Promote to Production"
```

### Database Rollback
```sql
-- Create rollback migration
-- supabase/migrations/20250524000000_rollback_feature.sql

-- Undo the changes
DROP TABLE IF EXISTS new_feature_table;
ALTER TABLE existing_table DROP COLUMN IF EXISTS new_column;

-- Restore old policies
DROP POLICY IF EXISTS "new_policy" ON table_name;
CREATE POLICY "old_policy" ON table_name ...;
```

### Emergency Rollback
```bash
# 1. Revert to previous commit
git revert HEAD
git push origin main

# 2. Vercel auto-deploys previous version

# 3. If database issues, restore backup
psql $DATABASE_URL < backup.sql
```

## Monitoring Deployment

### Health Checks
```typescript
// api/health endpoint
export async function GET() {
  const checks = {
    database: await checkDatabase(),
    storage: await checkStorage(),
    email: await checkEmail(),
    version: process.env.VITE_APP_VERSION
  };
  
  const healthy = Object.values(checks).every(c => c.status === 'ok');
  
  return new Response(JSON.stringify({
    status: healthy ? 'healthy' : 'degraded',
    checks
  }), {
    status: healthy ? 200 : 503
  });
}
```

### Deployment Notifications
```typescript
// Slack webhook
async function notifyDeployment(status: 'started' | 'success' | 'failed') {
  await fetch(SLACK_WEBHOOK, {
    method: 'POST',
    body: JSON.stringify({
      text: `Deployment ${status}: ${version} to ${environment}`
    })
  });
}
```

### Error Tracking
```typescript
// Deployment-specific error tracking
Sentry.setTag('deployment_id', process.env.VERCEL_DEPLOYMENT_ID);
Sentry.setTag('git_commit', process.env.VERCEL_GIT_COMMIT_SHA);
```

## Blue-Green Deployment (Future)

```nginx
# nginx config for zero-downtime deployments
upstream app {
    server blue.app.com weight=100;
    server green.app.com weight=0;
}

# Gradual rollout
# 1. Deploy to green
# 2. Test green
# 3. Shift traffic: 10% -> 50% -> 100%
# 4. Monitor metrics
# 5. Complete or rollback
```

## CI/CD Improvements Needed

1. **Automated Testing**
   - Unit tests before deploy
   - E2E tests on staging
   - Performance tests
   - Security scans

2. **Progressive Rollout**
   - Feature flags
   - Canary deployments
   - A/B testing infrastructure
   - Gradual rollout

3. **Observability**
   - Deployment tracking
   - Performance monitoring
   - Error tracking
   - User analytics

4. **Security**
   - Dependency scanning
   - Secret rotation
   - Security headers
   - CSP policies

## Deployment Metrics

Track these KPIs:
- Deployment frequency
- Lead time for changes  
- Mean time to recovery (MTTR)
- Change failure rate
- Deployment success rate

```typescript
interface DeploymentMetrics {
  frequency: number; // deploys per day
  leadTime: number; // hours from commit to production
  mttr: number; // minutes to recover from failure
  failureRate: number; // percentage of failed deploys
  rollbackRate: number; // percentage requiring rollback
}
```

## Cost Optimization

### Vercel
- Use ISR for static pages
- Optimize bundle size
- Enable compression
- Cache headers

### Supabase
- Connection pooling
- Query optimization
- Archive old data
- Storage cleanup

## Documentation Updates

After each deployment:
1. Update CHANGELOG.md
2. Update API documentation
3. Update user guides
4. Notify customers of changes

## Related Documentation
- [Environment Management](./20-environment-management.md)
- [Testing Infrastructure](./12-testing-infrastructure.md)
- [Database Architecture](./10-database-architecture.md)