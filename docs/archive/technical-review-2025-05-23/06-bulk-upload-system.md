# Bulk Upload System

## What We Built

A wizard-based bulk upload system allowing users to upload multiple assets simultaneously with progress tracking, automatic image processing, and metadata management.

## What Changed

### New Components
- `BulkUploadWizard.tsx` - Multi-step upload wizard
- `BulkUploadDialog.tsx` - Quick bulk upload modal
- `bulkUploadProcessor.ts` - Processing logic
- Progress tracking throughout

### Upload Flow
1. **File Selection**: Drag-and-drop or browse
2. **Product Assignment**: Link assets to products
3. **Processing**: Compression and thumbnail generation
4. **Confirmation**: Review before final upload

### Database Changes
```sql
CREATE TABLE bulk_uploads (
  id UUID PRIMARY KEY,
  collection_id UUID REFERENCES collections(id),
  user_id UUID REFERENCES users(id),
  status TEXT DEFAULT 'pending',
  total_files INTEGER,
  processed_files INTEGER DEFAULT 0,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT now()
);
```

## Why We Changed It

### User Pain Points
1. **Time Consuming**: Upload 100 images = 100 separate uploads
2. **No Progress Visibility**: Users unsure if uploads working
3. **Manual Organization**: Assign products after upload
4. **Browser Limits**: Page refreshes lost uploads

### Business Benefits
1. **Efficiency**: 90% time reduction for large uploads
2. **User Satisfaction**: Clear progress = less support
3. **Data Quality**: Consistent metadata application
4. **Scalability**: Handle fashion shoot volumes

## Design Decisions

### 1. **Wizard vs Single Page**
**Decision**: Multi-step wizard
```typescript
const steps = [
  { id: 'upload', title: 'Upload Files', component: UploadStep },
  { id: 'organize', title: 'Organize', component: OrganizeStep },
  { id: 'process', title: 'Process', component: ProcessStep },
  { id: 'complete', title: 'Complete', component: CompleteStep }
];
```
**Why**: 
- Less overwhelming
- Clear progress
- Allows interruption
- Better error recovery

### 2. **Client vs Server Processing**
**Decision**: Client-side (temporary)
**Why**: 
- Faster MVP delivery
- No server infrastructure needed
- Direct feedback to user
**Future**: Server-side for quality

### 3. **Progress Tracking**
```typescript
interface UploadProgress {
  totalFiles: number;
  processedFiles: number;
  currentFile: string;
  stage: 'uploading' | 'processing' | 'complete';
  errors: UploadError[];
}
```
**Why**: Users need confidence during long operations

### 4. **Chunked Processing**
```typescript
// Process in batches to avoid overwhelming browser
const BATCH_SIZE = 5;

for (let i = 0; i < files.length; i += BATCH_SIZE) {
  const batch = files.slice(i, i + BATCH_SIZE);
  await Promise.all(batch.map(processFile));
}
```
**Why**: Prevents browser crashes and allows progress updates

## Current Issues

### 1. **Memory Management**
```typescript
// Current: All files in memory
const processedFiles = await Promise.all(
  files.map(file => processImage(file))
);

// Problem: 100 x 10MB images = 1GB RAM
```

### 2. **Error Recovery**
- Single failure can break entire upload
- No retry mechanism
- Lost progress on refresh

### 3. **Product Assignment**
- Manual selection tedious for many files
- No smart grouping
- No bulk operations

### 4. **Performance**
- Client-side processing slow
- No concurrent upload optimization
- Canvas resizing quality issues

## What Needs to Be Done

### Immediate (Critical Path)

1. **Add Resume Capability**
   ```typescript
   interface ResumableUpload {
     id: string;
     files: File[];
     progress: UploadProgress;
     checkpoint: number;
   }
   
   // Save to localStorage
   function saveUploadState(upload: ResumableUpload) {
     localStorage.setItem(
       `upload_${upload.id}`,
       JSON.stringify(upload)
     );
   }
   
   // Resume on page load
   function resumeUpload(uploadId: string) {
     const saved = localStorage.getItem(`upload_${uploadId}`);
     if (saved) {
       const upload = JSON.parse(saved);
       continueFromCheckpoint(upload);
     }
   }
   ```

2. **Implement Error Recovery**
   ```typescript
   class UploadRetryManager {
     private retryQueue: Map<string, RetryItem> = new Map();
     
     async retryFailed() {
       for (const [fileId, item] of this.retryQueue) {
         if (item.attempts < 3) {
           await this.retry(fileId, item);
         }
       }
     }
   }
   ```

3. **Add Smart Grouping**
   ```typescript
   // Group by filename patterns
   function autoGroupFiles(files: File[]): FileGroup[] {
     const groups = new Map<string, File[]>();
     
     files.forEach(file => {
       // Extract SKU from filename
       const match = file.name.match(/([A-Z0-9]+)_/);
       const sku = match ? match[1] : 'ungrouped';
       
       if (!groups.has(sku)) {
         groups.set(sku, []);
       }
       groups.get(sku)!.push(file);
     });
     
     return Array.from(groups.entries()).map(([sku, files]) => ({
       sku,
       files,
       suggestedProduct: findProductBySku(sku)
     }));
   }
   ```

### Future Enhancements

1. **Server-Side Processing**
   ```typescript
   // Edge Function for processing
   export async function processUploadBatch(request: Request) {
     const { files, options } = await request.json();
     
     // Queue processing jobs
     const jobs = files.map(file => ({
       id: generateJobId(),
       file,
       operations: ['resize', 'compress', 'thumbnail']
     }));
     
     await queueJobs(jobs);
     
     return new Response(JSON.stringify({ 
       jobIds: jobs.map(j => j.id) 
     }));
   }
   ```

2. **Advanced Features**
   - ZIP file extraction
   - Folder structure preservation
   - Duplicate detection
   - Auto-tagging from EXIF
   - Facial recognition grouping

3. **Workflow Integration**
   ```typescript
   interface BulkUploadWorkflow {
     preProcess?: (files: File[]) => File[];
     validate?: (file: File) => ValidationResult;
     assign?: (file: File) => Assignment;
     postProcess?: (assets: Asset[]) => void;
   }
   ```

## Performance Optimization

### Current Performance
- 10 images (50MB): ~45 seconds
- 50 images (250MB): ~4 minutes
- 100 images (500MB): ~10 minutes

### Target Performance
- 10 images: < 20 seconds
- 50 images: < 2 minutes  
- 100 images: < 5 minutes

### Optimization Strategies

1. **Parallel Uploads**
   ```typescript
   const uploadQueue = new PQueue({ 
     concurrency: 4,
     interval: 1000,
     intervalCap: 10 
   });
   ```

2. **Stream Processing**
   ```typescript
   // Don't load entire file into memory
   async function* processFileStream(file: File) {
     const reader = file.stream().getReader();
     const decoder = new TextDecoder();
     
     while (true) {
       const { done, value } = await reader.read();
       if (done) break;
       yield decoder.decode(value);
     }
   }
   ```

3. **Web Workers**
   ```typescript
   // offload-processor.worker.ts
   self.addEventListener('message', async (e) => {
     const { file, options } = e.data;
     const processed = await processImage(file, options);
     self.postMessage({ processed });
   });
   ```

## User Experience Improvements

### Visual Feedback
```typescript
function FileUploadProgress({ file, progress }: Props) {
  return (
    <div className="flex items-center space-x-4">
      <FileIcon type={file.type} />
      <div className="flex-1">
        <p className="text-sm font-medium">{file.name}</p>
        <Progress value={progress} className="h-2" />
      </div>
      <span className="text-xs text-muted-foreground">
        {progress}%
      </span>
    </div>
  );
}
```

### Error Communication
```typescript
interface UploadError {
  file: string;
  error: 'size' | 'type' | 'network' | 'processing';
  message: string;
  recoverable: boolean;
}

function ErrorSummary({ errors }: { errors: UploadError[] }) {
  const grouped = groupBy(errors, 'error');
  
  return (
    <Alert variant="destructive">
      <AlertTitle>Upload Issues</AlertTitle>
      <AlertDescription>
        {Object.entries(grouped).map(([type, errs]) => (
          <div key={type}>
            <strong>{type}:</strong> {errs.length} files
          </div>
        ))}
      </AlertDescription>
    </Alert>
  );
}
```

## Testing Requirements

### Unit Tests
```typescript
describe('Bulk Upload Processor', () => {
  it('handles batch processing correctly', async () => {
    const files = createMockFiles(10);
    const processor = new BulkUploadProcessor();
    
    const results = await processor.processFiles(files);
    
    expect(results).toHaveLength(10);
    expect(results.every(r => r.success)).toBe(true);
  });
  
  it('recovers from individual failures', async () => {
    const files = createMockFiles(5);
    files[2] = createCorruptFile(); // Will fail
    
    const results = await processor.processFiles(files);
    
    expect(results.filter(r => r.success)).toHaveLength(4);
    expect(results[2].success).toBe(false);
  });
});
```

### E2E Tests
```typescript
test('bulk upload workflow', async ({ page }) => {
  await page.goto('/collection/123');
  await page.click('text=Bulk Upload');
  
  // Upload files
  const fileInput = page.locator('input[type="file"]');
  await fileInput.setInputFiles([
    'test-image-1.jpg',
    'test-image-2.jpg',
    'test-image-3.jpg'
  ]);
  
  // Verify progress
  await expect(page.locator('.upload-progress')).toBeVisible();
  await expect(page.locator('text=3 files')).toBeVisible();
  
  // Complete upload
  await page.waitForSelector('text=Upload Complete', { 
    timeout: 30000 
  });
});
```

## Monitoring & Analytics

Track:
- Upload success rate
- Average processing time
- Error types and frequencies
- File size distributions
- User abandonment points

```typescript
interface BulkUploadMetrics {
  totalUploads: number;
  successRate: number;
  averageFileSize: number;
  averageProcessingTime: number;
  errorsByType: Record<string, number>;
  abandonmentRate: number;
}
```

## Related Documentation
- [Storage Bucket Restructuring](./03-storage-bucket-restructuring.md)
- [UI/UX Improvements](./04-ui-ux-improvements.md)
- [Performance Optimization](./31-lessons-learned.md)