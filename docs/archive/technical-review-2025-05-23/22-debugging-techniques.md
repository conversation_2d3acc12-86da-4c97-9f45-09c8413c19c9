# Debugging Techniques

## Overview

This week involved extensive debugging across multiple layers of the stack. Here are the techniques, tools, and patterns that proved most effective for identifying and resolving issues.

## Database Debugging

### 1. **Direct SQL Inspection**
```bash
# Quick connection for debugging
PGPASSWORD=postgres psql -h localhost -p 54322 -U postgres -d postgres

# One-liner queries
PGPASSWORD=postgres psql -h localhost -p 54322 -U postgres -d postgres -c "SELECT * FROM users LIMIT 5;"
```

### 2. **RLS Policy Testing**
```sql
-- Test policies as specific user
SET LOCAL role TO 'authenticated';
SET LOCAL request.jwt.claims TO '{"sub": "user-uuid-here", "email": "<EMAIL>"}';

-- Now test queries
SELECT * FROM organizations; -- Should only show user's orgs

-- Reset session
RESET role;
RESET request.jwt.claims;
```

### 3. **Migration Debugging**
```sql
-- Check migration status
SELECT * FROM supabase_migrations.schema_migrations ORDER BY version DESC;

-- Verify table structure
\d+ table_name

-- Check constraints
SELECT conname, pg_get_constraintdef(oid) 
FROM pg_constraint 
WHERE conrelid = 'table_name'::regclass;

-- Find missing indexes
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE schemaname = 'public'
AND n_distinct > 100
AND correlation < 0.1
ORDER BY n_distinct DESC;
```

### 4. **Query Performance**
```sql
-- Enable query timing
\timing on

-- Analyze query plan
EXPLAIN ANALYZE
SELECT a.*, c.name 
FROM assets a
JOIN collections c ON a.collection_id = c.id
WHERE c.organization_id = 'uuid';

-- Find slow queries
SELECT query, mean_exec_time, calls
FROM pg_stat_statements
WHERE mean_exec_time > 100
ORDER BY mean_exec_time DESC;
```

## Frontend Debugging

### 1. **React Query DevTools**
```typescript
// Add to main.tsx for development
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

function App() {
  return (
    <>
      <Routes />
      {import.meta.env.DEV && <ReactQueryDevtools />}
    </>
  );
}
```

### 2. **Network Request Logging**
```typescript
// Intercept all Supabase requests
const supabaseWithLogging = createClient(url, key, {
  global: {
    fetch: async (url, options) => {
      console.group(`🔍 ${options?.method || 'GET'} ${url}`);
      console.log('Headers:', options?.headers);
      console.log('Body:', options?.body);
      console.groupEnd();
      
      const response = await fetch(url, options);
      
      console.group(`✅ Response ${response.status}`);
      const clone = response.clone();
      console.log('Data:', await clone.json());
      console.groupEnd();
      
      return response;
    }
  }
});
```

### 3. **Component Render Tracking**
```typescript
// Debug excessive re-renders
function useWhyDidYouUpdate(name: string, props: Record<string, any>) {
  const previousProps = useRef<Record<string, any>>();
  
  useEffect(() => {
    if (previousProps.current) {
      const allKeys = Object.keys({ ...previousProps.current, ...props });
      const changedProps: Record<string, any> = {};
      
      allKeys.forEach(key => {
        if (previousProps.current![key] !== props[key]) {
          changedProps[key] = {
            from: previousProps.current![key],
            to: props[key]
          };
        }
      });
      
      if (Object.keys(changedProps).length) {
        console.log('[why-did-you-update]', name, changedProps);
      }
    }
    
    previousProps.current = props;
  });
}

// Usage
function MyComponent(props) {
  useWhyDidYouUpdate('MyComponent', props);
  // ...
}
```

### 4. **State Debugging**
```typescript
// Global state inspector
window.__inspectState = () => {
  console.log('Auth State:', supabase.auth.getSession());
  console.log('User Role:', getUserRole());
  console.log('Organization:', getCurrentOrganization());
  console.log('Router:', window.location);
};
```

## Email Debugging

### 1. **Local Email Testing (Inbucket)**
```bash
# View all emails
open http://localhost:54324

# API access
curl http://localhost:54324/api/v1/mailbox/<EMAIL>
```

### 2. **Edge Function Debugging**
```typescript
// Add console logs (visible in Supabase logs)
export async function sendEmail(req: Request) {
  console.log('📧 Email Request:', {
    headers: Object.fromEntries(req.headers.entries()),
    url: req.url
  });
  
  try {
    const body = await req.json();
    console.log('📧 Email Body:', body);
    
    // Send email...
    
    console.log('✅ Email sent successfully');
  } catch (error) {
    console.error('❌ Email error:', error);
    throw error;
  }
}
```

### 3. **Email Template Testing**
```html
<!-- Test template rendering -->
<script>
  // Inject test data
  const testData = {
    organizationName: 'Test Brand',
    inviterName: 'John Doe',
    acceptUrl: 'http://localhost:8080/accept/test'
  };
  
  // Replace variables
  document.body.innerHTML = document.body.innerHTML
    .replace(/{{(\w+)}}/g, (match, key) => testData[key] || match);
</script>
```

## Storage Debugging

### 1. **Bucket Inspection**
```sql
-- List all buckets
SELECT * FROM storage.buckets;

-- Check bucket policies
SELECT * FROM storage.objects 
WHERE bucket_id = 'media-compressed' 
LIMIT 10;

-- Find orphaned files
SELECT o.* FROM storage.objects o
LEFT JOIN assets a ON o.name LIKE '%' || a.id || '%'
WHERE a.id IS NULL;
```

### 2. **Upload Debugging**
```typescript
// Enhanced upload with debugging
async function debugUpload(file: File, bucket: string, path: string) {
  console.group(`📤 Uploading ${file.name}`);
  console.log('Size:', (file.size / 1024 / 1024).toFixed(2), 'MB');
  console.log('Type:', file.type);
  console.log('Bucket:', bucket);
  console.log('Path:', path);
  
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: '3600',
        upsert: true
      });
    
    if (error) {
      console.error('Upload failed:', error);
    } else {
      console.log('Upload successful:', data);
    }
  } finally {
    console.groupEnd();
  }
}
```

## Common Debugging Patterns

### 1. **Binary Search Debugging**
```typescript
// When something breaks, isolate the issue
async function debugComplexOperation() {
  console.log('Step 1: Starting');
  const step1Result = await step1();
  console.log('Step 1 complete:', step1Result);
  
  console.log('Step 2: Processing');
  const step2Result = await step2(step1Result);
  console.log('Step 2 complete:', step2Result);
  
  // Comment out steps to isolate failure
}
```

### 2. **Error Boundary Debugging**
```typescript
class DebugErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.group('🔥 React Error Boundary');
    console.error('Error:', error);
    console.error('Component Stack:', errorInfo.componentStack);
    console.error('Props:', this.props);
    console.groupEnd();
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded">
          <h2>Something went wrong</h2>
          <details>
            <summary>Error details</summary>
            <pre>{this.state.error?.toString()}</pre>
          </details>
        </div>
      );
    }
    
    return this.props.children;
  }
}
```

### 3. **Performance Debugging**
```typescript
// Measure operation time
function measureTime(label: string) {
  const start = performance.now();
  
  return () => {
    const duration = performance.now() - start;
    console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`);
  };
}

// Usage
const done = measureTime('Database query');
const result = await supabase.from('assets').select('*');
done();
```

## Debugging Tools Setup

### VS Code Extensions
```json
{
  "recommendations": [
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "bradlc.vscode-tailwindcss",
    "Prisma.prisma",
    "ms-vscode.vscode-typescript-next",
    "christian-kohler.path-intellisense"
  ]
}
```

### Chrome DevTools Settings
1. Enable "Preserve log" in Network tab
2. Set up Workspace for source editing
3. Add custom formatters for Supabase objects
4. Use Logpoints instead of console.log

### Debugging Shortcuts
```bash
# Alias for common debugging commands
alias dblocal='PGPASSWORD=postgres psql -h localhost -p 54322 -U postgres -d postgres'
alias supalogs='supabase logs --follow'
alias checkmigrations='supabase db remote status'
```

## Production Debugging

### 1. **Safe Production Queries**
```sql
-- Always use transactions for debugging
BEGIN;
-- Your debugging queries
SELECT * FROM sensitive_table WHERE condition;
-- Always rollback
ROLLBACK;
```

### 2. **Feature Flag Debugging**
```typescript
// Enable debug mode for specific users
if (user.email.endsWith('@fashionlab.com')) {
  window.__DEBUG_MODE = true;
  
  // Extra logging
  console.log('Debug mode enabled for:', user.email);
}
```

### 3. **Error Reporting**
```typescript
// Capture context for errors
window.addEventListener('error', (event) => {
  const context = {
    user: getCurrentUser(),
    organization: getCurrentOrganization(),
    route: window.location.pathname,
    timestamp: new Date().toISOString()
  };
  
  console.error('Global error:', {
    error: event.error,
    context
  });
  
  // Send to monitoring service
  Sentry.captureException(event.error, { extra: context });
});
```

## Debugging Checklist

When debugging an issue:

1. **Reproduce**
   - [ ] Can you reproduce locally?
   - [ ] What are exact steps?
   - [ ] Does it happen consistently?

2. **Isolate**
   - [ ] Frontend or backend issue?
   - [ ] Which component/function?
   - [ ] What changed recently?

3. **Investigate**
   - [ ] Check browser console
   - [ ] Check network requests
   - [ ] Check database state
   - [ ] Check server logs

4. **Fix**
   - [ ] Identify root cause
   - [ ] Implement fix
   - [ ] Add test to prevent regression

5. **Verify**
   - [ ] Test the fix locally
   - [ ] Test edge cases
   - [ ] Test on staging

## Related Documentation
- [Testing Infrastructure](./12-testing-infrastructure.md)
- [Environment Management](./20-environment-management.md)
- [Database Architecture](./10-database-architecture.md)