# RLS Policies & Security

## Current State: CRITICAL SECURITY ISSUE ⚠️

All Row Level Security (RLS) policies are currently **DISABLED** across the entire database for demo purposes. This means:
- Any authenticated user can read ALL data
- No data isolation between organizations
- No role-based access control
- **MUST BE FIXED BEFORE PRODUCTION**

## What Happened

### Timeline of RLS Changes
1. **Initial Implementation**: Complex dual-role policies
2. **Single Role Migration**: Simplified but broke policies
3. **Debugging Attempts**: Temporary permissive policies
4. **Current State**: All RLS disabled for demo

### Why RLS Was Disabled
```sql
-- Migrations show progressive disabling
"TEMPORARY: Disable RLS on pending_invitations for demo"
"TEMPORARY: Disable RLS on all tables for demo"
"TEMPORARY: Disable RLS on storage.objects for demo"
```

Issues encountered:
- Invitation flow broke with strict policies
- Storage uploads failed
- Complex policy interactions
- Time pressure for demo

## Security Architecture Design

### Multi-Tenant Isolation Pattern
```sql
-- Every table should follow this pattern
CREATE POLICY "Tenant isolation"
ON table_name
FOR ALL
USING (
  -- Platform users bypass isolation
  auth.is_platform_user() OR
  -- Regular users see only their org data
  organization_id IN (
    SELECT organization_id 
    FROM organization_members 
    WHERE user_id = auth.uid()
  )
);
```

### Role Hierarchy
```
platform_super        → Full system access
  ├── platform_admin  → Cross-org read, limited write
  └── brand_admin     → Full org access
      └── brand_member → Read/write own content
          └── external_retoucher → Limited write
              └── external_prompter → Read only
```

### Policy Types by Table

#### Organizations Table
```sql
-- Read: Members can see their org
CREATE POLICY "Members read own org"
ON organizations FOR SELECT
USING (
  id IN (
    SELECT organization_id 
    FROM organization_members 
    WHERE user_id = auth.uid()
  )
);

-- Update: Only brand admins
CREATE POLICY "Admins update org"
ON organizations FOR UPDATE
USING (
  auth.uid() IN (
    SELECT user_id 
    FROM organization_members om
    JOIN users u ON om.user_id = u.id
    WHERE om.organization_id = organizations.id
    AND u.role = 'brand_admin'
  )
);
```

#### Collections Table
```sql
-- Inherit org permissions
CREATE POLICY "Collection access follows org"
ON collections FOR ALL
USING (
  organization_id IN (
    SELECT organization_id
    FROM organization_members
    WHERE user_id = auth.uid()
  )
);
```

#### Assets Table
```sql
-- Complex: Check via collection → org
CREATE POLICY "Asset access via collection"
ON assets FOR SELECT
USING (
  collection_id IN (
    SELECT c.id 
    FROM collections c
    JOIN organization_members om ON c.organization_id = om.organization_id
    WHERE om.user_id = auth.uid()
  )
);
```

## Current Security Vulnerabilities

### 1. **No Data Isolation** 🚨
Any user can:
```sql
-- See all organizations
SELECT * FROM organizations;

-- Access any asset
SELECT * FROM assets;

-- Read all user data
SELECT * FROM users;
```

### 2. **No Permission Checks** 🚨
```typescript
// Frontend checks exist but backend allows everything
if (isBrandAdmin) {
  // Show delete button
}
// But API allows delete regardless!
```

### 3. **Invitation Security Holes** 🚨
- Anyone can accept any invitation
- No email verification required
- Expired invitations still work

### 4. **Storage Access** 🚨
- All storage buckets publicly accessible
- No signed URLs
- No access logging

## Implementation Plan

### Phase 1: Critical Security (Day 1)
1. **Re-enable Basic RLS**
   ```sql
   -- Start with read-only policies
   ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
   
   CREATE POLICY "Users read own org"
   ON organizations FOR SELECT
   USING (
     auth.uid() IN (
       SELECT user_id FROM organization_members
       WHERE organization_id = organizations.id
     )
   );
   ```

2. **Fix Invitation Flow**
   ```sql
   -- Allow unauthenticated to read their invitation
   CREATE POLICY "Read invitation by email"
   ON pending_invitations FOR SELECT
   USING (
     -- Authenticated admin or matching email
     (auth.uid() IS NOT NULL AND exists_admin_check()) OR
     (auth.jwt() ->> 'email' = email AND expires_at > now())
   );
   ```

3. **Secure Storage**
   ```sql
   -- Enforce organization boundaries
   CREATE POLICY "Org storage isolation"
   ON storage.objects FOR ALL
   USING (
     bucket_id IN ('profiles', 'general-uploads') AND
     (storage.foldername(name))[1] = 'organizations' AND
     (storage.foldername(name))[2]::uuid IN (
       SELECT organization_id FROM organization_members
       WHERE user_id = auth.uid()
     )
   );
   ```

### Phase 2: Complete Implementation (Day 2-3)
1. Write comprehensive policies for all tables
2. Add helper functions for common checks
3. Implement policy unit tests
4. Add monitoring for policy violations

### Phase 3: Advanced Security (Week 2)
1. Implement signed URLs for private content
2. Add rate limiting at database level
3. Create audit triggers
4. Set up security monitoring

## Helper Functions

```sql
-- Reduce policy complexity with functions
CREATE OR REPLACE FUNCTION auth.is_platform_user()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users
    WHERE id = auth.uid()
    AND role IN ('platform_super', 'platform_admin')
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION auth.is_org_admin(org_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM organization_members om
    JOIN users u ON om.user_id = u.id
    WHERE om.user_id = auth.uid()
    AND om.organization_id = org_id
    AND u.role = 'brand_admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION auth.user_organizations()
RETURNS SETOF UUID AS $$
BEGIN
  RETURN QUERY
  SELECT organization_id
  FROM organization_members
  WHERE user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Testing RLS Policies

### Test Framework
```sql
-- Test different user scenarios
CREATE OR REPLACE FUNCTION test_rls_policies()
RETURNS TABLE(test_name TEXT, passed BOOLEAN) AS $$
DECLARE
  platform_user_id UUID := 'platform-user-uuid';
  brand_admin_id UUID := 'brand-admin-uuid';
  brand_member_id UUID := 'brand-member-uuid';
  other_org_user_id UUID := 'other-org-user-uuid';
BEGIN
  -- Test 1: Brand member can't see other orgs
  SET LOCAL role TO 'authenticated';
  SET LOCAL request.jwt.claims TO 
    jsonb_build_object('sub', brand_member_id);
  
  RETURN QUERY
  SELECT 
    'Brand member org isolation'::TEXT,
    NOT EXISTS (
      SELECT 1 FROM organizations
      WHERE id NOT IN (
        SELECT organization_id FROM organization_members
        WHERE user_id = brand_member_id
      )
    );
  
  -- Add more tests...
END;
$$ LANGUAGE plpgsql;
```

### Automated Testing
```typescript
// tests/rls-policies.test.ts
describe('RLS Policies', () => {
  test('users can only see their organizations', async () => {
    const { data: orgs } = await supabaseAs(testUser)
      .from('organizations')
      .select('*');
    
    expect(orgs).toHaveLength(1);
    expect(orgs[0].id).toBe(testUser.organizationId);
  });
  
  test('cannot access other org assets', async () => {
    const { data, error } = await supabaseAs(testUser)
      .from('assets')
      .select('*')
      .eq('collection_id', otherOrgCollectionId);
    
    expect(data).toHaveLength(0);
  });
});
```

## Security Monitoring

### Database-Level Monitoring
```sql
-- Log policy violations
CREATE TABLE security_violations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID,
  table_name TEXT,
  operation TEXT,
  violated_policy TEXT,
  request_data JSONB,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Trigger on RLS denial
CREATE OR REPLACE FUNCTION log_rls_violation()
RETURNS event_trigger AS $$
BEGIN
  -- Log violation details
END;
$$ LANGUAGE plpgsql;
```

### Application-Level Monitoring
```typescript
// Track suspicious patterns
interface SecurityMetrics {
  failedAuthAttempts: number;
  crossOrgAccessAttempts: number;
  unusualQueryPatterns: string[];
  elevatedPrivilegeUse: number;
}

// Alert on thresholds
if (metrics.crossOrgAccessAttempts > 10) {
  await alertSecurityTeam({
    severity: 'high',
    message: 'Possible data breach attempt',
    userId: context.userId
  });
}
```

## Security Best Practices

### 1. **Principle of Least Privilege**
- Start with no access
- Grant minimum required permissions
- Regular permission audits

### 2. **Defense in Depth**
- RLS policies (database)
- API validation (edge functions)
- UI restrictions (frontend)
- Network security (infrastructure)

### 3. **Audit Everything**
- Who accessed what
- When and from where
- What changes were made
- Retention policy compliance

### 4. **Regular Security Reviews**
- Monthly policy audits
- Penetration testing
- Code security scanning
- Dependency updates

## Emergency Response Plan

### If Breach Detected:
1. **Immediate Actions**
   ```sql
   -- Revoke all sessions
   TRUNCATE auth.sessions;
   
   -- Lock down database
   ALTER DATABASE postgres SET default_transaction_read_only TO on;
   ```

2. **Investigation**
   - Check security_activity table
   - Review access logs
   - Identify scope of breach

3. **Recovery**
   - Reset affected credentials
   - Notify affected users
   - Implement additional controls
   - Post-mortem analysis

## Compliance Considerations

### GDPR Requirements
- Data isolation by organization ✓
- Access logging ✓ 
- Right to deletion ⚠️ (needs soft delete)
- Data portability ⚠️ (needs export feature)

### SOC 2 Requirements
- Access controls ✓
- Encryption at rest ✓
- Audit trails ⚠️ (needs completion)
- Incident response ✓

## Next Steps Checklist

- [ ] Create RLS re-enablement plan
- [ ] Write comprehensive test suite
- [ ] Document each policy's purpose
- [ ] Set up monitoring alerts
- [ ] Train team on security model
- [ ] Schedule security audit
- [ ] Create incident response runbook

## Related Documentation
- [Database Architecture](./10-database-architecture.md)
- [Role System Simplification](./02-role-system-simplification.md)
- [Testing Infrastructure](./12-testing-infrastructure.md)