# Debugging Session: Workflow Stage Update Issue
**Date**: May 26, 2025  
**Issue**: Asset workflow stage updates not persisting in production  
**Resolution**: Fixed RLS policy WITH CHECK clause

## Problem Description

Users were unable to update the workflow stage of assets in production, even though:
- The user was `platform_super` with full permissions
- Updates worked locally
- No error messages were returned
- The API returned 200 OK status

## Symptoms

1. Workflow stage updates appeared to succeed (no errors)
2. UI showed old values after refresh
3. Verification queries showed updates were not applied
4. <PERSON><PERSON><PERSON> showed: `actual: 'upload'` when expecting `'draft'`

## Root Cause Analysis

### Initial Hypothesis (Incorrect)
- Cache issues
- Invalid query syntax for cache busting

### Actual Issue
The RLS policy's `WITH CHECK` clause was preventing the `.select()` from returning data after updates:

```sql
-- Problem: WITH CHECK was filtering returned data
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM public.collections c
    JOIN public.organization_memberships om ON c.organization_id = om.organization_id
    WHERE c.id = assets.collection_id 
      AND om.user_id = auth.uid()
  )
  OR
  EXISTS (
    SELECT 1
    FROM public.users
    WHERE id = auth.uid() 
      AND role IN ('platform_super', 'platform_admin')
  )
);
```

Even though platform users could update (USING clause allowed it), the WITH CHECK clause was preventing the updated data from being returned, causing `.select()` to return an empty array.

## Debugging Steps

### 1. Added Cache-Busting (Failed)
```typescript
// This caused 400 errors - invalid syntax
query = query.or(`id.neq.${forceRefresh}_never_matches`);
```

### 2. Added Update Verification
```typescript
const { data: updateData, error } = await supabase
  .from('assets')
  .update(updates)
  .in('id', assetIds)
  .select();  // This returned []
```

### 3. Enhanced Logging
Revealed that updates returned empty arrays despite no errors:
```
[bulkUpdateAssets] Update response: {updateData: Array(0), expectedUpdates: {…}, returnedCount: 0}
```

### 4. RLS Policy Analysis
Discovered the WITH CHECK clause was the issue.

## Solution

Created a separate policy for platform users with unrestricted WITH CHECK:

```sql
-- New policy specifically for platform users
CREATE POLICY "Platform users can update any asset"
ON public.assets FOR UPDATE
USING (
  auth.uid() IN (
    SELECT id FROM public.users 
    WHERE role IN ('platform_super', 'platform_admin')
  )
)
WITH CHECK (true);  -- This allows all data to be returned
```

## Key Learnings

1. **WITH CHECK affects returned data**: Not just whether an operation is allowed
2. **Platform users need separate policies**: To avoid WITH CHECK restrictions
3. **Always use .select() on updates**: To verify operations and catch RLS issues
4. **Empty array !== error**: Supabase returns `[]` for RLS-blocked queries, not errors

## Prevention Measures

1. **Test with .select()**: Always verify updates return data
2. **Check both USING and WITH CHECK**: They serve different purposes
3. **Use debug functions**: Create SECURITY DEFINER functions for testing
4. **Monitor production logs**: Add comprehensive logging for update operations

## Code Changes Made

1. Fixed invalid cache-busting query syntax
2. Added `bulkUpdateAssetsWithVerification` utility function
3. Enhanced logging throughout update process
4. Created RLS policy fix migration
5. Added comprehensive troubleshooting documentation

## Testing Checklist

- [ ] Test updates return data with `.select()`
- [ ] Verify different user roles can update appropriately
- [ ] Check returned data matches expected updates
- [ ] Test in both local and production environments
- [ ] Verify no performance impact from policy changes

## References

- [PR/Commit]: Fix asset workflow stage updates with proper verification
- [Migration]: `20250526100000_fix_assets_update_policy_return_data.sql`
- [Documentation]: `/docs/troubleshooting/supabase-common-issues.md`