# RLS Permissions Matrix - Quick Reference

## Permission Symbols
- ✅ Full access
- 📝 Limited access (with conditions)
- ❌ No access
- 👤 Own data only

## Organizations
| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ | ✅ | ✅ | ✅ |
| platform_admin | ✅ | ✅ | ✅ | ❌ |
| brand_admin | 📝 Own | ❌ | 📝 Own | ❌ |
| brand_member | 📝 Own | ❌ | ❌ | ❌ |
| external_* | 📝 Own | ❌ | ❌ | ❌ |

## Collections
| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ | ✅ | ✅ | ✅ |
| platform_admin | ✅ | ✅ | ✅ | ✅ |
| brand_admin | 📝 Own org | ✅ | 📝 Own org | 📝 Own org |
| brand_member | 📝 Own org | ✅ | ❌ | ❌ |
| external_retoucher | 📝 Own org | ✅ | ❌ | ❌ |
| external_prompter | 📝 Own org | ❌ | ❌ | ❌ |

## Assets
| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ | ✅ | ✅ | ✅ |
| platform_admin | ✅ | ✅ | ✅ | ✅ |
| brand_admin | 📝 Own org | ✅ | ✅ | ✅ |
| brand_member | 📝 Own org | ✅ | ✅ | ✅ |
| external_retoucher | 📝 Own org | ✅ | ✅ | ✅ |
| external_prompter | 📝 Own org | ❌ | ❌ | ❌ |

## Products
| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ | ✅ | ✅ | ✅ |
| platform_admin | ✅ | ✅ | ✅ | ✅ |
| brand_admin | 📝 Own org | ✅ | ✅ | 📝 Own org |
| brand_member | 📝 Own org | ✅ | ✅ | ❌ |
| external_retoucher | 📝 Own org | ✅ | ✅ | ❌ |
| external_prompter | 📝 Own org | ❌ | ❌ | ❌ |

## Comments
| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| All org members | 📝 Own org | ✅ | 👤 Own | 👤 Own |

## Tags
| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| All authenticated | ✅ | ✅ | ❌ | ❌ |
| platform_super | ✅ | ✅ | ✅ | ✅ |
| platform_admin | ✅ | ✅ | ✅ | ✅ |

## Invitations
| Role | View | Create | Update | Delete |
|------|------|--------|--------|--------|
| platform_super | ✅ | ✅ | ✅ | ✅ |
| platform_admin | ✅ | ✅ | ✅ | ✅ |
| brand_admin | 📝 Own org | 📝 Own org | 📝 Own org | 📝 Own org |
| brand_member | 📝 Own email | ❌ | 📝 Accept | ❌ |
| Anonymous | 📝 By token | ❌ | ❌ | ❌ |

## Quick Rules Summary

### Platform Users (platform_super, platform_admin)
- Can access all organizations' data
- Can create new organizations
- platform_super can delete organizations

### Brand Admins
- Full control within their organization
- Can manage team members
- Can update organization settings
- Cannot create new organizations

### Brand Members
- Can view and work with assets
- Can create collections and products
- Cannot manage team or organization settings
- Cannot delete collections

### External Retouchers
- Same as brand members for asset work
- Can upload and modify assets
- Cannot access admin functions

### External Prompters
- Read-only access to organization data
- Can add comments
- Cannot upload or modify assets

## Key Security Features

1. **Multi-tenant isolation**: Users can only access data from their organizations
2. **Role-based permissions**: Different roles have different access levels
3. **Personal data protection**: Users can only modify their own comments and profile
4. **Audit trail**: All actions logged in security_activity table
5. **Time-limited invitations**: 7-day expiry for security