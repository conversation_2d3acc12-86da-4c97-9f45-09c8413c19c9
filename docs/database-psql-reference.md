# Database PSQL Reference

Quick reference for direct database access and debugging.

## Connection Commands

```bash
# Local
psql -h localhost -p 54322 -d postgres -U postgres
# Password: postgres

# Staging (use pooler URL to avoid IPv6 issues)
psql "postgresql://postgres.qnfmiotatmkoumlymynq:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"

# Production (use pooler URL to avoid IPv6 issues)
psql "postgresql://postgres.cpelxqvcjnbpnphttzsn:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"

# Note: Direct URLs may fail with IPv6. See docs/troubleshooting/ipv6-database-connection-issue.md
```

## Useful Queries

### Check RLS Policies
```sql
-- All policies for a table
SELECT * FROM pg_policies 
WHERE tablename = 'assets' 
AND schemaname = 'public';

-- Check if R<PERSON> is enabled
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';
```

### Test User Permissions
```sql
-- Switch to user context
SET LOCAL ROLE authenticated;
SET LOCAL request.jwt.claims.sub TO 'user-uuid-here';

-- Test query
SELECT * FROM assets LIMIT 5;

-- Reset
RESET ROLE;
```

### Debug WITH CHECK Issues
```sql
-- Find UPDATE policies with restrictive WITH CHECK
SELECT 
  tablename,
  policyname,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
AND cmd = 'UPDATE'
AND with_check != 'true';
```

### Migration History
```sql
-- Check migration status
SELECT * FROM supabase_migrations.schema_migrations 
ORDER BY inserted_at DESC;

-- Manually mark migration as completed (emergency only)
INSERT INTO supabase_migrations.schema_migrations (version) 
VALUES ('20250526000000');
```

## Common psql Commands

```bash
# List tables
\dt public.*

# Describe table
\d public.assets

# List policies
\dp public.assets

# Exit
\q
```