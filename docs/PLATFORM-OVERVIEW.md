# Fashion Lab Platform Overview

## About Fashion Lab

Fashion Lab is a collaborative workspace where fashion companies and the Fashion Lab team work together on AI-generated fashion imagery projects. The platform provides tools for managing fashion campaigns from initial concept through final delivery.

## Tech Stack

### Frontend
- **Framework**: React 18.3.1 with TypeScript 5.8.3
- **UI Components**: shadcn/ui (built on Radix UI)
- **Styling**: Tailwind CSS 3.4.11
- **State Management**: React Query 5.56.2 + Context API
- **Routing**: React Router DOM 6.30.0
- **Notifications**: Sonner 1.5.0 (toast notifications)
- **Form Components**: React Hook Form 7.53.0 + Zod 3.23.8 (available but not actively used)
- **Charts**: Recharts 2.12.7 (minimal usage in organization overview)

### Backend & Infrastructure
- **Authentication**: Supabase Auth
- **Database**: PostgreSQL via Supabase
- **Storage**: Supabase Storage (5 dedicated buckets)
- **Email**: Resend 4.1.2 via Edge Functions
- **Build Tool**: Vite 5.4.1
- **Image Processing**: browser-image-compression 2.0.2

### Testing
- **Unit Tests**: Vitest 3.0.9 + React Testing Library
- **Test Coverage**: Comprehensive unit test suite

### External Services
- **AI API**: Fashion Lab AI services for image generation and refinement
- **File Handling**: JSZip 3.10.1 for bulk uploads

## Core Features

### 1. Campaign Management
- Create and manage fashion campaigns (collections)
- Set technical requirements (resolution, aspect ratios, formats)
- Define creative direction and style guides
- Track campaign progress through workflow stages
- Cover image and metadata management

### 2. Asset Management
- Single and bulk upload capabilities (ZIP file support)
- Automatic image compression (WebP) and thumbnail generation
- Multi-step bulk upload wizard with validation
- Bulk operations (download, tag, delete, workflow updates)
- Advanced filtering by workflow stage, product, time period
- Version control and asset history tracking

### 3. AI-Powered Workflow
- **Prepare**: Process images for AI training
- **Train**: Create custom fashion models (Loras)
- **Generate**: Create AI fashion imagery
- **Refine**: Mask and improve generated images
- **Upscale**: Enhance resolution for final delivery
- Workflow stage tracking and management
- Custom model library support

### 4. Collaboration Tools
- Multi-tenant architecture for brand isolation
- Role-based access control (6 distinct roles)
- Comments and visual annotations on assets
- Coordinate-based annotation system
- Review and approval workflows
- Real-time notifications and activity tracking
- Invitation system with email verification

### 5. Organization Structure
- **Organizations**: Fashion brands (clients)
- **Collections**: Individual campaigns/projects
- **Products**: Specific items within collections (optional grouping)
- **Assets**: Images at various workflow stages
- **Tags**: Flexible categorization system

### 6. User & Profile Management
- User profile editing with avatar support
- Password change functionality
- Email change with verification flow
- Security activity logging and audit trail
- Platform-wide user administration

### 7. Storage & Performance
- Organization-based storage quotas
- Usage tracking and visualization
- Optimized storage buckets for different asset types
- Automatic image optimization
- Efficient thumbnail generation

## Workflow Stages

Assets progress through defined stages:

### Core Workflow
1. **Upload**: Initial product images from brands
2. **Draft**: AI-generated concepts and variations
3. **Upscale**: High-resolution versions (4K+)
4. **Retouch**: Professionally edited images
5. **Final**: Approved deliverables ready for use

### Additional Stages
- **Brief**: Campaign briefs and creative direction
- **Input**: Reference materials and inspiration
- **Custom Models**: Training data for custom AI models
- **Library**: Reusable asset library/stock images
- **Raw**: Unprocessed source files

## User Roles

The platform uses a unified role system (single source of truth in `users.role`):

### Fashion Lab Team
- **platform_super**: 
  - Full platform access and control
  - Manage all organizations and users
  - Access platform administration tools
  - View cross-organization analytics
  
- **platform_admin**: 
  - Manage assigned organizations
  - Create and invite users
  - Access organization data
  - Limited platform administration

### Fashion Brands (Clients)
- **brand_admin**: 
  - Full control within their organization
  - Invite and manage team members
  - Create collections and manage settings
  - Access all organization features
  
- **brand_member**: 
  - Create and manage content
  - Upload and tag assets
  - Comment and collaborate
  - Limited to assigned collections

### External Contractors
- **external_prompter**: 
  - AI generation specialists
  - Access to generation tools
  - Limited to assigned collections
  
- **external_retoucher**: 
  - Image editing professionals
  - Upload retouched versions
  - Access to editing workflows

## Security & Permissions

### Database Security
- Row Level Security (RLS) enforced on all tables
- Multi-tenant data isolation via organization memberships
- Unified role system eliminates permission complexity
- Helper functions for consistent permission checks:
  - `auth.is_platform_user()`
  - `auth.is_admin_user()`
  - `auth.is_organization_member()`

### Application Security
- Frontend permission checks for improved UX
- Protected routes with role-based access
- Secure file uploads with type validation
- Session management via Supabase Auth
- Email verification for new accounts

### Audit & Compliance
- Security activity logging for sensitive operations
- Event types tracked:
  - Login/logout events
  - Password changes
  - Email changes
  - Profile updates
  - Failed login attempts
  - Invitation events
- IP address and user agent logging
- Comprehensive audit trail for compliance

## Key Database Tables

| Table | Purpose |
|-------|---------|
| **users** | User profiles with unified role system (6 roles) |
| **organizations** | Fashion brands with branding and settings |
| **organization_memberships** | Links users to their organizations |
| **collections** | Campaigns/seasons with metadata and status |
| **products** | Product groupings within collections (optional) |
| **assets** | Images with workflow stages and storage paths |
| **asset_tags** | Many-to-many asset categorization |
| **tags** | Flexible tagging system with categories |
| **comments** | Feedback and visual annotations on assets |
| **pending_invitations** | Email invitations with expiry tracking |
| **bulk_uploads** | ZIP file upload tracking and progress |
| **security_activity** | Audit log for security events |

### Storage Buckets

| Bucket | Purpose | Access |
|--------|---------|--------|
| **profiles** | User avatars and org logos | Public read |
| **assets** | Original uploaded files | Private |
| **thumbnails** | Auto-generated thumbnails | Public read |
| **compressed** | WebP compressed versions | Public read |
| **general-uploads** | Briefs, covers, misc files | Private |

## Development Workflow

### Local Development
```bash
npm install                  # Install dependencies
supabase start              # Start local Supabase (requires Docker)
npm run dev                 # Start dev server (http://localhost:8080)
```

### Database Management
```bash
supabase db reset           # Reset local database
supabase migration new <name> # Create new migration
npm run setup:test-data     # Load test data (local)
npm run setup:test-data:images # Include sample images
```

### Testing
```bash
# Unit Tests
npm test                    # Run unit tests
npm run test:ui            # Run with UI
npm run test:coverage      # Generate coverage

npm run test:all           # Run all unit tests
```

### Deployment
- **Staging**: Auto-deploys from `main` branch (Vercel)
- **Production**: Deploy from `production` branch
- **Database**: Use Supabase CLI for migrations
- **Edge Functions**: Deploy via Supabase Dashboard

## API Integration

### Fashion Lab AI Services
The platform integrates with Fashion Lab's proprietary AI API for:
- **Image Preparation**: Automated background removal and cropping
- **Model Training**: Custom Lora model generation
- **Image Generation**: AI fashion imagery creation
- **Refinement**: Masking and detail improvement
- **Upscaling**: Resolution enhancement (up to 4K+)

### Integration Features
- Async job queuing with status tracking
- Webhook notifications for job completion
- Automatic retry logic for failed operations
- Result caching for performance
- Progress tracking for long-running operations

### External Services
- **Resend**: Transactional email delivery
- **Supabase Edge Functions**: Serverless compute
- **Vercel**: Frontend hosting and deployment

## Architecture Patterns

### State Management
- **React Query**: Server state and caching
- **Context Providers**:
  - `SupabaseContext`: Authentication state
  - `UserRoleContext`: Permission management
  - `OrganizationContext`: Current organization
  - `ProfileContext`: User profile data
  - `FilterContext`: Global filter state

### Data Flow
```
User Action → React Component → Custom Hook → Supabase Client → Database
                                     ↓
                              React Query Cache → UI Update
```

### Key Utilities
- `getAssetUrl()`: Intelligent URL generation
- `useAssetOperations()`: Bulk asset management
- `useOrganizationRole()`: Permission checking
- `useStorageUsage()`: Quota tracking

## Future Enhancements

### Planned Features
- Collection-level permissions for contractors
- Time-based access control
- Real-time collaboration features
- Advanced workflow automation
- Comprehensive analytics dashboard
- Mobile application
- API for third-party integrations

### Technical Improvements
- WebSocket support for real-time updates
- Enhanced caching strategies
- Progressive Web App (PWA) capabilities
- Internationalization (i18n) support
- Advanced search with filters
- Batch processing optimization

## Environment Configuration

### Required Environment Variables
```bash
# Supabase
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_anon_key

# Email Service
RESEND_API_KEY=your_resend_key

# AI Services
VITE_API_BASE_URL=fashion_lab_api_url
```

### Staging Environment
- Project Ref: `qnfmiotatmkoumlymynq`
- Auto-deploys from `main` branch
- Full feature parity with production
- Test data isolation

## Performance Characteristics

- **Image Processing**: WebP compression reduces file sizes by ~70%
- **Thumbnail Generation**: Automatic 200x200 thumbnails for grid views
- **Bulk Operations**: Handles 100+ files per upload batch
- **Query Optimization**: React Query caching reduces API calls
- **Storage**: 5 dedicated buckets for optimized asset delivery