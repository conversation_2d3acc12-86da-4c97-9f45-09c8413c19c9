# Fashion Lab Reference

## Tech Stack

**Frontend**: React 18 + TypeScript + Vite + shadcn/ui + Tailwind CSS  
**Backend**: Supabase (PostgreSQL + Auth + Storage + Edge Functions)  
**Deployment**: Vercel (frontend) + Supabase CLI (database)

## Database Schema

### Core Tables
```
organizations → collections → assets → comments
         ↓                      ↓
    users (via memberships)   products (optional grouping)
                               tags (categorization)
```

### User Roles
- `platform_super` - Full system access
- `platform_admin` - Support access  
- `brand_admin` - Organization admin
- `brand_member` - Regular member
- `external_retoucher` - Contractor
- `external_prompter` - Read-only external

### Workflow Stages
`upload` → `raw_ai_images` → `selected` → `refined` → `upscale` → `retouch` → `final`

### Storage Buckets
- `profiles` - Avatars and logos (public)
- `assets` - Original files (private)
- `thumbnails` - Auto-generated (public)
- `compressed` - WebP versions (public)
- `general-uploads` - Misc files (private)

## Environment Variables

```bash
VITE_ENVIRONMENT=development|staging|production
VITE_SUPABASE_URL=<supabase_url>
VITE_SUPABASE_ANON_KEY=<anon_key>
SUPABASE_ACCESS_TOKEN=<personal_token>  # For CLI operations
```

## Common Queries

```sql
-- Check user permissions
SELECT u.role, om.organization_id
FROM users u
LEFT JOIN organization_memberships om ON u.id = om.user_id
WHERE u.id = auth.uid();

-- Get collection assets
SELECT a.*, array_agg(t.name) as tags
FROM assets a
LEFT JOIN asset_tags at ON a.id = at.asset_id
LEFT JOIN tags t ON at.tag_id = t.id
WHERE a.collection_id = $1
GROUP BY a.id;
```

## Migration Patterns

```sql
-- Always use IF EXISTS/IF NOT EXISTS
DROP POLICY IF EXISTS "policy_name" ON table_name;
CREATE TABLE IF NOT EXISTS table_name (...);

-- RLS policy pattern
CREATE POLICY "policy_name" ON table_name
FOR ALL USING (
  (SELECT role FROM public.users WHERE id = auth.uid()) IN ('platform_super', 'platform_admin')
  OR
  organization_id IN (
    SELECT organization_id FROM public.organization_memberships
    WHERE user_id = auth.uid()
  )
);
```

## URLs

- **Local**: http://localhost:8080
- **Staging**: https://staging.fashionlab.tech
- **Production**: https://app.fashionlab.tech