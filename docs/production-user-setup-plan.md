# Production User Setup Plan

## Overview
This document outlines the strategy for setting up platform users in production, starting with superadmins and extending to the Fashionlab team.

## Phase 1: Platform Superadmins Setup

### Users to Create
1. **<PERSON>** - <EMAIL> (Platform Super Admin)
2. **As<PERSON>** - <EMAIL> (Platform Super Admin)

### Recommended Approach

#### Option A: Manual Sign-up (RECOMMENDED)
This is the cleanest approach that ensures proper auth setup:

1. **Enable Sign-ups Temporarily**
   - In Supabase Dashboard > Authentication > Providers
   - Enable "Allow new users to sign up"
   
2. **Have Users Sign Up**
   - <PERSON> and <PERSON><PERSON> sign up through the app at `/login`
   - They'll receive confirmation emails automatically
   - They confirm their emails
   
3. **Upgrade to Superadmin**
   - Run the SQL script to upgrade their roles:
   ```sql
   UPDATE public.users 
   SET role = 'platform_super' 
   WHERE email IN ('<EMAIL>', '<EMAIL>');
   ```
   
4. **Disable Public Sign-ups**
   - Return to Supabase Dashboard
   - Disable "Allow new users to sign up"

#### Option B: Direct Database Creation
If you need to create users directly:

1. **Create Auth Users**
   ```sql
   -- This must be done in Supabase Dashboard SQL editor with service role
   INSERT INTO auth.users (
     id,
     email,
     encrypted_password,
     email_confirmed_at,
     created_at,
     updated_at,
     raw_app_meta_data,
     raw_user_meta_data,
     is_super_admin,
     role
   ) VALUES 
   (
     gen_random_uuid(),
     '<EMAIL>',
     crypt('temporary_password_123!', gen_salt('bf')),
     NOW(), -- Auto-confirm email
     NOW(),
     NOW(),
     '{"provider": "email", "providers": ["email"]}',
     '{}',
     false,
     'authenticated'
   ),
   (
     gen_random_uuid(),
     '<EMAIL>',
     crypt('temporary_password_123!', gen_salt('bf')),
     NOW(), -- Auto-confirm email
     NOW(),
     NOW(),
     '{"provider": "email", "providers": ["email"]}',
     '{}',
     false,
     'authenticated'
   );
   ```
   
2. **Then run the setup script**

## Phase 2: Adding Fashionlab Team Members

### Best Approach: Use the Invitation System

**Why this is best:**
- Users receive proper welcome emails
- They set their own passwords
- Proper auth flow is maintained
- You can track who accepted invitations
- No manual SQL needed

**Process:**
1. **As a superadmin, navigate to Organization Members**
   - Go to any organization (or create Fashionlab organization)
   - Click "Invite Member"
   
2. **Send Invitations**
   - Enter team member emails
   - Set role as `platform_admin`
   - Add a welcome message
   - They'll receive invitation emails

3. **Team Members Accept**
   - Click link in email
   - Create password
   - Automatically added with correct role

### Alternative: Batch SQL Import

If you need to add many users at once without sending emails:

```sql
-- First, create auth users (requires service role)
-- Then create public user records
INSERT INTO public.users (id, email, role, created_at, updated_at)
SELECT 
    au.id,
    au.email,
    'platform_admin',
    NOW(),
    NOW()
FROM auth.users au
WHERE au.email IN (
    '<EMAIL>',
    '<EMAIL>'
    -- Add more emails
)
ON CONFLICT (id) DO UPDATE
SET role = 'platform_admin';
```

**Note:** This approach:
- ❌ Does NOT send confirmation emails
- ❌ Users must reset passwords to gain access
- ❌ No welcome/onboarding flow
- ✅ Fast for bulk operations

## Phase 3: Production Security Checklist

### Before Going Live:
- [ ] Disable public sign-ups in Supabase Auth settings
- [ ] Set up proper email templates in Supabase
- [ ] Configure custom SMTP if needed (for fashionlab.tech emails)
- [ ] Test invitation flow with a test account
- [ ] Ensure password policies are configured
- [ ] Set up 2FA requirements for platform admins (optional)

### Auth Configuration Settings:
In your Supabase Dashboard, ensure these settings:

1. **Email Settings**
   - Sender name: "FashionLab"
   - Sender email: <EMAIL> (if custom SMTP)
   
2. **URL Configuration**
   - Site URL: https://your-production-domain.com
   - Redirect URLs: Include your production domain

3. **Security Settings**
   - Require email confirmation: Yes
   - Secure password requirements: Enabled
   - Session timeout: 7 days (adjust as needed)

## Recommended Timeline

### Day 1: Superadmin Setup
1. Enable sign-ups temporarily
2. Have Lars and Asger sign up and confirm emails
3. Run SQL to upgrade to platform_super
4. Disable public sign-ups
5. Test superadmin access

### Day 2-3: Platform Admin Setup
1. Create Fashionlab organization (if needed)
2. Use invitation system to invite team
3. Monitor invitation acceptance
4. Verify all team members can access

### Day 4: Security Hardening
1. Review all RLS policies
2. Audit user permissions
3. Set up monitoring/alerts
4. Document admin procedures

## SQL Script for Superadmin Setup

Save this as `scripts/setup-production-superadmins.sql`:

```sql
-- Run this AFTER users have signed up and confirmed emails
BEGIN;

-- Update existing users to platform_super
UPDATE public.users 
SET 
    role = 'platform_super',
    updated_at = NOW()
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Verify the update
SELECT 
    u.id,
    u.email,
    u.role,
    au.email_confirmed_at,
    au.last_sign_in_at
FROM public.users u
JOIN auth.users au ON u.id = au.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>');

COMMIT;
```

## Important Notes

1. **Email Confirmation**: 
   - SQL-created users do NOT receive confirmation emails
   - The invitation system DOES send proper emails
   
2. **Password Management**:
   - SQL-created users need password reset links
   - Invited users set passwords during acceptance

3. **Audit Trail**:
   - Invitations are tracked in pending_invitations table
   - User creation via SQL has no audit trail

4. **Best Practice**:
   - Use invitation system for all non-emergency user creation
   - Reserve SQL creation for initial superadmins only