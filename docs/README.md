# FashionLab Platform Documentation

Welcome to the FashionLab platform documentation. This guide will help you find the right documentation for your needs.

## 🚀 Quick Start

- **New Developer**: Start with [Getting Started Guide](./GETTING-STARTED.md)
- **API Integration**: See [Fashion Lab API Integration](./FASHIONLAB-API-INTEGRATION.md)
- **Database Setup**: Check [Database Schema](./database-schema-final.md)
- **Deployment**: Follow [Deployment Workflow](./deployment/deployment-workflow.md)

## 📚 Documentation Categories

### Core Platform
- [Platform Overview](./PLATFORM-OVERVIEW.md) - High-level platform architecture
- [Getting Started Guide](./GETTING-STARTED.md) - Setup and development guide
- [Database Schema](./database-schema-final.md) - Complete database documentation
- [Authentication Flow](./AUTHENTICATION-FLOW.md) - User authentication system

### API Integrations
- **[Fashion Lab API Integration](./FASHIONLAB-API-INTEGRATION.md)** - ⭐ Main integration guide
- [API Testing](./API-TESTING.md) - Testing API endpoints
- [Integration Examples](./development/integrations/fashionlab-api-examples.md)

### Development
- [Development Guide](./development/README.md) - Development best practices
- [Database Workflow](./development/database-workflow-recommendations.md)
- [Feature Flags](./FEATURE-FLAGS.md) - Feature flag system

### Architecture
- [Platform Architecture](./architecture/platform-architecture.md) - System design
- [Single Role System](./architecture/single-role-system-architecture.md)
- [Terminology Guide](./architecture/terminology-guide.md)

### Deployment
- [Deployment Workflow](./deployment/deployment-workflow.md) - CI/CD process
- [Staging Setup](./deployment/staging-setup.md) - Staging environment
- [Database Migrations](./deployment/supabase-cli-migration-guide.md)

### Troubleshooting
- [Common Issues](./troubleshooting/supabase-common-issues.md) - General troubleshooting
- **[Fashion Lab Auth Fix](./troubleshooting/fashion-lab-authentication-fix.md)** - ⚠️ Critical fix
- [Database Connection Issues](./troubleshooting/ipv6-database-connection-issue.md)

### Security
- [Security Guide](./security/SECURITY-REMEDIATION-GUIDE.md) - Security best practices
- [Threat Model](./security/THREAT_MODEL.md) - Security analysis
- [RLS Policies](./rls-policies-overview.md) - Row Level Security

## 🔧 Quick Reference

### Fashion Lab API
- **Authentication**: Use `jwt ${token}` format (NOT `Bearer ${token}`)
- **Main Functions**: `generate-images`, `queue-status`, `fashion-lab-proxy`
- **Storage**: Images stored in `ai-generated` bucket
- **Database**: Records in `ai_generated_images` table

### Common Commands
```bash
# Start development environment
supabase start

# Deploy functions
supabase functions deploy generate-images
supabase functions deploy queue-status

# Run tests
node test-complete-image-generation-flow.js

# Check database
node check-ai-images.js
```

### Environment Setup
```env
# Required in .env.local
FASHION_LAB_API_URL=https://fashionlab.notfirst.rodeo
FASHIONLAB_JWT_SECRET=2ms4LQBtkbvJ8RwFmBht

# Required in Supabase secrets
FASHIONLAB_JWT_SECRET=2ms4LQBtkbvJ8RwFmBht
```

## 🚨 Known Issues

### Resolved Issues
- ✅ **Fashion Lab Authentication**: Fixed JWT format issue (Jan 2025)
- ✅ **Image Storage**: Fixed storage pipeline (Jan 2025)
- ✅ **Database Permissions**: Fixed RLS policies

### Active Issues
- None currently

## 📞 Support

### Getting Help
1. **Check Documentation**: Start with relevant guide above
2. **Search Issues**: Look in troubleshooting section
3. **Run Diagnostics**: Use provided test scripts
4. **Check Logs**: Review function logs in Supabase dashboard

### Common Solutions
- **Images not storing**: Check [Fashion Lab Auth Fix](./troubleshooting/fashion-lab-authentication-fix.md)
- **Database errors**: Check [Common Issues](./troubleshooting/supabase-common-issues.md)
- **Deployment issues**: Check [Deployment Guide](./deployment/deployment-workflow.md)

## 📝 Contributing

When updating documentation:
1. Keep guides practical and example-heavy
2. Include troubleshooting sections
3. Add testing instructions
4. Update this README if adding new guides
5. Cross-reference related documentation

---

**Last Updated**: January 2025  
**Status**: ✅ All critical issues resolved
