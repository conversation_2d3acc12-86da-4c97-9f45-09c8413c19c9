# API Testing Guide

This guide provides curl commands that can be imported into Postman for testing the Fashion Lab API.

## Prerequisites

1. You need a Supabase account and must be logged in
2. Your edge functions must be deployed or running locally
3. You need to set the following environment variables in your edge functions:
   - `FASHIONLAB_JWT_SECRET`
   - `FASHION_LAB_API_URL`
   - `FASHION_LAB_API_KEY`

## Step 1: Get Your Supabase Auth Token

First, you need to get your Supabase session token. You can get this from the browser's developer tools:

1. Log into your Fashion Lab app
2. Open Developer Tools (F12)
3. Go to Application/Storage → Local Storage
4. Find the key that contains `auth-token` 
5. Copy the `access_token` value

## Step 2: Generate JWT Token

Use this curl command to get a JWT token for the Fashion Lab API:

```bash
curl --location --request POST 'http://localhost:54321/functions/v1/fashionlab-jwt' \
--header 'Authorization: Bearer YOUR_SUPABASE_ACCESS_TOKEN' \
--header 'Content-Type: application/json'
```

For production:
```bash
curl --location --request POST 'https://qnfmiotatmkoumlymynq.supabase.co/functions/v1/fashionlab-jwt' \
--header 'Authorization: Bearer YOUR_SUPABASE_ACCESS_TOKEN' \
--header 'Content-Type: application/json'
```

Response:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## Step 3: Test Image Generation

Use this curl command to test image generation:

```bash
curl --location --request POST 'http://localhost:54321/functions/v1/generate-images' \
--header 'Authorization: Bearer YOUR_SUPABASE_ACCESS_TOKEN' \
--header 'Content-Type: application/json' \
--data-raw '{
  "prompt": "A scandinavian fashion model, blonde hair, young woman in her twenties, thin and petite build, natural features, full body shot, facing camera directly, standing pose, wearing sleek black leather jacket with silver zippers, fitted silhouette, cropped length, pure white seamless backdrop, studio lighting, high fashion editorial style, dramatic poses, artistic composition, ratio:9:16, format:jpeg, cfg:7.5",
  "model_id": "S",
  "lora_name": "bubbleroom_model_s_v1",
  "lora_weight": 1.0,
  "angle": "Full body face on",
  "seed": 42,
  "cfg": 7.5,
  "flux_guidance": 0.7,
  "num_images": 1,
  "aspect_ratio": "9:16",
  "format": "jpeg",
  "collection_id": "YOUR_COLLECTION_ID",
  "product_id": "1",
  "metadata": {
    "modelName": "Model S - Scandinavian",
    "angleName": "Full body face on",
    "productSKU": "137240235",
    "productName": "Sleek Leather Biker Jacket"
  }
}'
```

For production:
```bash
curl --location --request POST 'https://qnfmiotatmkoumlymynq.supabase.co/functions/v1/generate-images' \
--header 'Authorization: Bearer YOUR_SUPABASE_ACCESS_TOKEN' \
--header 'Content-Type: application/json' \
--data-raw '{
  "prompt": "A scandinavian fashion model, blonde hair, young woman in her twenties, thin and petite build, natural features, full body shot, facing camera directly, standing pose, wearing sleek black leather jacket with silver zippers, fitted silhouette, cropped length, pure white seamless backdrop, studio lighting, high fashion editorial style, dramatic poses, artistic composition, ratio:9:16, format:jpeg, cfg:7.5",
  "model_id": "S",
  "lora_name": "bubbleroom_model_s_v1",
  "lora_weight": 1.0,
  "angle": "Full body face on",
  "seed": 42,
  "cfg": 7.5,
  "flux_guidance": 0.7,
  "num_images": 1,
  "aspect_ratio": "9:16",
  "format": "jpeg",
  "collection_id": "YOUR_COLLECTION_ID",
  "product_id": "1",
  "metadata": {
    "modelName": "Model S - Scandinavian",
    "angleName": "Full body face on",
    "productSKU": "137240235",
    "productName": "Sleek Leather Biker Jacket"
  }
}'
```

## Importing to Postman

### Method 1: Import as cURL

1. Copy any of the curl commands above
2. In Postman, click "Import" → "Raw text"
3. Paste the curl command
4. Click "Continue" → "Import"

### Method 2: Create Collection

Create a new Postman collection with these requests:

#### 1. Get JWT Token
- **Method**: POST
- **URL**: `{{SUPABASE_URL}}/functions/v1/fashionlab-jwt`
- **Headers**:
  - `Authorization`: Bearer {{SUPABASE_ACCESS_TOKEN}}
  - `Content-Type`: application/json
- **Body**: None

#### 2. Generate Images
- **Method**: POST
- **URL**: `{{SUPABASE_URL}}/functions/v1/generate-images`
- **Headers**:
  - `Authorization`: Bearer {{SUPABASE_ACCESS_TOKEN}}
  - `Content-Type`: application/json
- **Body** (raw JSON):
```json
{
  "prompt": "A scandinavian fashion model, blonde hair, young woman in her twenties, thin and petite build, natural features, full body shot, facing camera directly, standing pose, wearing sleek black leather jacket with silver zippers, fitted silhouette, cropped length, pure white seamless backdrop, studio lighting, high fashion editorial style, dramatic poses, artistic composition, ratio:9:16, format:jpeg, cfg:7.5",
  "model_id": "S",
  "lora_name": "bubbleroom_model_s_v1",
  "lora_weight": 1.0,
  "angle": "Full body face on",
  "seed": 42,
  "cfg": 7.5,
  "flux_guidance": 0.7,
  "num_images": 1,
  "aspect_ratio": "9:16",
  "format": "jpeg",
  "collection_id": "{{COLLECTION_ID}}",
  "product_id": "1",
  "metadata": {
    "modelName": "Model S - Scandinavian",
    "angleName": "Full body face on",
    "productSKU": "137240235",
    "productName": "Sleek Leather Biker Jacket"
  }
}
```

### Environment Variables

Create a Postman environment with these variables:

```
SUPABASE_URL: http://localhost:54321 (or https://qnfmiotatmkoumlymynq.supabase.co for staging)
SUPABASE_ACCESS_TOKEN: Your access token from browser
COLLECTION_ID: Your collection ID (e.g., b2b18cf5-edca-418f-be28-2bc720a3fb4d)
```

## Testing Workflow

1. **Get your Supabase access token** from the browser
2. **Call the JWT endpoint** to get a Fashion Lab JWT token (if needed by the external API)
3. **Call the generate-images endpoint** with your image generation parameters
4. **Check the response** for queue_id and status

## Model and Angle Options

### Available Models:
- `S` - Scandinavian (lora: `bubbleroom_model_s_v1`)
- `M` - European (lora: `bubbleroom_model_m_v1`)
- `L` - African American (lora: `bubbleroom_model_l_v1`)
- `XL` - Latino (lora: `bubbleroom_model_xl_v1`)

### Available Angles:
- `Full body face on`
- `Full body backside`
- `Half body front`
- `Half body backside`

## Troubleshooting

### 401 Unauthorized
- Check that your Supabase access token is valid and not expired
- Make sure you're including the "Bearer " prefix in the Authorization header

### 500 Internal Server Error
- Check the edge function logs: `supabase functions logs generate-images`
- Verify environment variables are set correctly
- Check that the collection_id exists and you have access to it

### CORS Errors
- If testing from a browser, make sure CORS is properly configured
- Use Postman or curl for testing to avoid CORS issues

## Local Development

To test locally:

1. Start Supabase:
   ```bash
   supabase start
   ```

2. Serve the edge functions with environment variables:
   ```bash
   supabase functions serve --env-file .env.local
   ```

3. Use `http://localhost:54321` as your base URL

## Direct Fashion Lab API Testing (Advanced)

If you want to test the Fashion Lab API directly (bypassing our edge functions):

1. First get a JWT token using our edge function
2. Then call the Fashion Lab API directly:

```bash
curl --location --request POST 'https://fashionlab.notfirst.rodeo/api/v1/generate' \
--header 'Authorization: Bearer YOUR_JWT_TOKEN_FROM_STEP_1' \
--header 'X-API-Key: YOUR_FASHION_LAB_API_KEY' \
--header 'Content-Type: application/json' \
--data-raw '{
  "prompt": "Your prompt here",
  "model_config": {
    "model_id": "S",
    "lora_name": "bubbleroom_model_s_v1",
    "lora_weight": 1.0
  },
  "generation_params": {
    "seed": 42,
    "cfg": 7.5,
    "flux_guidance": 0.7,
    "num_images": 1,
    "aspect_ratio": "9:16",
    "format": "jpeg"
  }
}'
```

Note: The exact API format may vary - check with the Fashion Lab API documentation for the correct request structure.