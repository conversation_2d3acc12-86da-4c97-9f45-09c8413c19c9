# Quick Reference

## Emergency Contacts

- **Staging Issues**: Check https://staging.fashionlab.tech
- **Production Issues**: Check https://app.fashionlab.tech
- **Database Access**: Use pooler URLs (see CLAUDE.md)

## Critical Commands

```bash
# Rollback production
git revert HEAD && git push origin production

# Check production logs
mcp__supabase__get_logs --project_id cpelxqvcjnbpnphttzsn --service "api"

# Emergency database access
psql "postgresql://postgres.cpelxqvcjnbpnphttzsn:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"
```

## Common Fixes

**Deployment failed**: Check Vercel dashboard  
**Migration failed**: Use `mcp__supabase__list_migrations` to check status  
**Auth issues**: Check Supabase Auth logs  
**Storage issues**: Verify bucket policies in Supabase dashboard

## Environment Files

- `.env.local` - Local development
- `.env.staging` - Staging (in Vercel)
- `.env.production` - Production (in Vercel)