# GitHub Secrets Setup Guide [DEPRECATED]

> **⚠️ DEPRECATED**: This guide is no longer relevant as we have removed all GitHub Actions workflows. 
> 
> Frontend deployments are handled automatically by Vercel, and database migrations are pushed manually via the Supabase CLI.

## Current Deployment Setup

### Frontend (Vercel)
- Automatic deployments triggered by Git pushes
- `main` branch → staging.fashionlab.tech
- `production` branch → app.fashionlab.tech
- No GitHub secrets required

### Database (Supabase)
- Manual migrations via Supabase CLI
- Requires Supabase access token (stored locally, not in GitHub)
- See [Deployment Guide](./DEPLOYMENT.md) for current process

## Environment Variables

Environment variables are now managed directly in:
- **Vercel Dashboard**: For frontend environment variables
- **Local `.env` files**: For development
- **Supabase Dashboard**: For database configuration

## Migration from GitHub Actions

If you previously had GitHub Actions set up:
1. All GitHub secrets can be safely removed from repository settings
2. Ensure Vercel projects are properly connected to Git branches
3. Set up Supabase CLI locally for manual migration deployment

For current deployment instructions, see:
- [Simple Deployment Guide](./DEPLOYMENT-SIMPLE.md)
- [Full Deployment Guide](./DEPLOYMENT.md)