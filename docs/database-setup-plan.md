# FashionLab Database Setup Plan

## Executive Summary

This document outlines the complete plan to fix and properly structure the FashionLab database. The goal is to create a clean, secure, and maintainable database schema that properly leverages Supabase Auth and implements correct multi-tenant isolation.

## Current State

### What's Working
- Table structure is mostly correct
- User role enum has proper values
- Foreign key relationships are in place
- Basic functionality works with RLS disabled

### What's Broken
1. **All RLS policies are disabled** (critical security issue)
2. **Migration with auth.config reference** (causes errors)
3. **No proper auth integration** (mixing auth concerns)
4. **Inconsistent migration history** (many temporary fixes)

## Target Architecture

### Data Hierarchy
```
Organizations (Brands)
├── Collections (Campaigns) 
│   ├── Assets (Images/Files)
│   │   ├── Products (optional association)
│   │   ├── Comments (feedback/annotations)
│   │   └── Tags (workflow/categories)
│   └── Products (can group assets)
└── Members (Users with roles)
```

### Authentication Model
```
Supabase Auth (auth schema)
├── Handles: Login, Signup, Sessions, Tokens
├── Provides: auth.uid(), <PERSON><PERSON><PERSON> claims
└── Triggers: on_auth_user_created

Application (public schema)  
├── users table: Profiles, Roles, Preferences
├── organization_memberships: User-Org relationships
└── RLS Policies: Use auth.uid() for access control
```

## Implementation Steps

### Phase 1: Clean Migration History (Day 1 Morning)

1. **Backup Current State**
   ```bash
   pg_dump -h localhost -p 54322 -U postgres -d postgres > backup_before_cleanup.sql
   ```

2. **Create Consolidated Migration**
   - Combine all working migrations into one clean file
   - Remove all temporary/debug migrations
   - Fix auth.config reference

3. **Reset and Test**
   ```bash
   supabase db reset
   npm run test:e2e
   ```

### Phase 2: Implement Proper RLS (Day 1 Afternoon)

1. **Create Helper Functions**
   ```sql
   -- Check if user is in organization
   CREATE FUNCTION auth.user_in_organization(org_id UUID)
   RETURNS BOOLEAN AS $$
     SELECT EXISTS (
       SELECT 1 FROM organization_memberships
       WHERE user_id = auth.uid() 
       AND organization_id = org_id
     );
   $$ LANGUAGE sql SECURITY DEFINER;

   -- Get user's role
   CREATE FUNCTION auth.user_role()
   RETURNS user_role AS $$
     SELECT role FROM users WHERE id = auth.uid();
   $$ LANGUAGE sql SECURITY DEFINER;
   ```

2. **Enable RLS Table by Table**
   - Start with organizations
   - Test each policy before moving to next table
   - Use consistent patterns

3. **Policy Patterns**
   ```sql
   -- Read: Members can see their org's data
   CREATE POLICY "read_own_org_data" ON table_name
   FOR SELECT USING (
     auth.user_in_organization(organization_id) OR
     auth.user_role() IN ('platform_super', 'platform_admin')
   );

   -- Write: Role-based permissions
   CREATE POLICY "admin_write" ON table_name
   FOR INSERT, UPDATE, DELETE USING (
     (auth.user_in_organization(organization_id) AND 
      auth.user_role() = 'brand_admin') OR
     auth.user_role() = 'platform_super'
   );
   ```

### Phase 3: Fix Auth Integration (Day 2 Morning)

1. **Fix handle_new_user Function**
   ```sql
   CREATE OR REPLACE FUNCTION handle_new_user()
   RETURNS TRIGGER AS $$
   BEGIN
     INSERT INTO public.users (id, email, role)
     VALUES (
       NEW.id,
       NEW.email,
       'brand_member' -- Default role
     );
     RETURN NEW;
   END;
   $$ LANGUAGE plpgsql SECURITY DEFINER;
   ```

2. **Remove Auth Schema Dependencies**
   - No direct modifications to auth tables
   - Use Supabase client for auth operations
   - Store only user_id references

### Phase 4: Test Everything (Day 2 Afternoon)

1. **E2E Test Coverage**
   - Login/Signup flow
   - Invitation acceptance
   - Multi-tenant isolation
   - Role-based permissions

2. **Security Audit**
   - Try accessing other org's data
   - Test each role's permissions
   - Verify invitation security

## Migration Strategy

### New Clean Migration Structure
```
migrations/
├── 001_initial_schema.sql          # All tables, types, functions
├── 002_auth_integration.sql        # Triggers, auth helpers
├── 003_rls_policies.sql           # All RLS policies
├── 004_seed_data.sql              # Initial data
└── 005_storage_buckets.sql        # Storage setup
```

### Rollback Plan
1. Keep backup of current state
2. Document each change
3. Test in local environment first
4. Deploy to staging before production

## Success Criteria

1. **All RLS policies enabled and working**
2. **Clean migration history**
3. **Proper auth integration**
4. **All E2E tests passing**
5. **No security vulnerabilities**

## Timeline

- **Day 1**: Migration cleanup, RLS implementation
- **Day 2**: Auth fixes, comprehensive testing
- **Day 3**: Documentation, staging deployment

## Next Steps

1. Review this plan
2. Create backup
3. Start implementation
4. Test thoroughly
5. Deploy to staging