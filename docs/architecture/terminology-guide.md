# Terminology Guide

This guide explains the terminology used in Fashion Lab Platform to ensure consistency across code, documentation, and user interfaces.

## Code vs User-Facing Terminology

| Code/Database | User-Facing | Context |
|--------------|-------------|---------|
| `organization` | **Brand** | Fashion brands (H&M, Fila, etc.) that use the platform |
| `brand_admin` | **Brand Admin** | Administrator for a specific fashion brand |
| `brand_member` | **Brand Member** | Regular user within a fashion brand |
| `platform_admin` | **Platform Admin** | Platform administrator |
| `platform_super` | **Platform Super Admin** | Platform owner with full access |
| `external_retoucher` | **External Retoucher** | External contractor for image retouching |
| `external_prompter` | **External Prompter** | External contractor with read-only access |
| `collection` | **Campaign** | Fashion photography project or marketing campaign |
| `collection_id` | **Campaign** | Identifier for a campaign |

## Why Different Terms?

1. **Technical Consistency**: The codebase uses `organization` and `collection` throughout (database tables, API calls, React contexts)
2. **User Understanding**: 
   - Fashion industry professionals understand "Brand" better than "Organization"
   - Creative teams and photographers think in terms of "Campaigns" rather than "Collections"
3. **Future-Proofing**: This separation allows us to change user-facing terms without refactoring code
4. **Avoiding Confusion**: "Collection" in fashion often means a seasonal line of clothing, while we mean a photography/marketing project

## Implementation Guidelines

### In Code
```typescript
// Always use 'organization' in code
const { organizationId } = useOrganizationContext();
const userRole = user.role; // Single role system
```

### In UI
```tsx
// Use 'Brand' and 'Campaign' in user-facing content
<h1>Select Your Brand</h1>
<p>You are a Brand Admin for {brandName}</p>
<h2>Create New Campaign</h2>
<p>View all campaigns for this brand</p>
```

### In Documentation
- Technical docs: Use `organization` when referring to code/database
- User guides: Use "Brand" when explaining features to users
- Architecture docs: Explain both terms and why they differ

## Common Terms

### Platform Entities
- **Brand** (org): Fashion company using the platform
- **Campaign** (collection): A photography/marketing project for a brand
- **Asset**: An image or file in the system
- **Product**: Individual fashion item within a campaign

### User Types
- **Platform Super Admin**: Full system access
- **Platform Admin**: Cross-organization support access
- **Brand Admin**: Can manage everything within their brand
- **Brand Member**: Can view and work with brand assets
- **External Retoucher**: External contractor for image retouching
- **External Prompter**: External contractor with read-only access

### Workflow Stages
- **Upload**: Initial asset upload
- **Draft**: Asset being prepared
- **Upscale**: AI enhancement processing
- **Retouch**: Manual editing phase
- **Final**: Completed and approved asset

## Consistency Checklist

When creating new features or documentation:

1. ✅ Use `organization` and `collection` in all code and database references
2. ✅ Use "Brand" and "Campaign" in all UI text and user communications
3. ✅ Explain the distinction in technical documentation
4. ✅ Keep API endpoints using `/organizations/` and `/collections/` for consistency
5. ✅ Update this guide if new terminology is introduced
6. ✅ Remember: Collections = Campaigns in user-facing content