# Single Role System Architecture

## Overview

FashionLab uses a unified single role system that replaced the previous dual role architecture (user_role + organization_role) for better clarity, performance, and maintainability.

## Role Hierarchy

```mermaid
graph TD
    A[Platform Level] --> B[platform_super]
    A --> C[platform_admin]
    
    D[Brand Level] --> E[brand_admin]
    D --> F[brand_member]
    
    G[External Level] --> H[external_retoucher]
    G --> I[external_prompter]
    
    B --> |Full System Access| J[All Organizations]
    C --> |Cross-Org Access| J
    E --> |Org Admin| K[Own Organization]
    F --> |Org Member| K
    H --> |Edit Assets| K
    I --> |View/Comment| K
```

## Database Schema

### Core Tables

```mermaid
erDiagram
    users {
        uuid id PK
        text email
        user_role role
        text display_name
        text first_name
        text last_name
        text avatar_url
        timestamp created_at
        timestamp updated_at
    }
    
    organizations {
        uuid id PK
        text name
        text description
        text logo_url
        timestamp created_at
        timestamp updated_at
    }
    
    organization_memberships {
        uuid id PK
        uuid user_id FK
        uuid organization_id FK
        timestamp created_at
    }
    
    collections {
        uuid id PK
        uuid organization_id FK
        text name
        text description
        timestamp created_at
        timestamp updated_at
    }
    
    assets {
        uuid id PK
        uuid collection_id FK
        text name
        text file_path
        workflow_stage stage
        timestamp created_at
        timestamp updated_at
    }
    
    users ||--o{ organization_memberships : "member of"
    organizations ||--o{ organization_memberships : "has members"
    organizations ||--o{ collections : "owns"
    collections ||--o{ assets : "contains"
```

### Role Definitions

| Role | Description | Access Level |
|------|-------------|--------------|
| `platform_super` | Platform superadmin | Full system access, can delete users |
| `platform_admin` | Platform admin | Cross-organization access, limited platform features |
| `brand_admin` | Brand administrator | Full access within their organization(s) |
| `brand_member` | Brand member | Standard access within their organization(s) |
| `external_retoucher` | External retoucher | Asset editing permissions only |
| `external_prompter` | External prompter | Asset viewing and commenting only |

## Permission Matrix

```mermaid
graph LR
    subgraph "Platform Roles"
        PS[platform_super]
        PA[platform_admin]
    end
    
    subgraph "Brand Roles"
        BA[brand_admin]
        BM[brand_member]
    end
    
    subgraph "External Roles"
        ER[external_retoucher]
        EP[external_prompter]
    end
    
    subgraph "Resources"
        ORGS[Organizations]
        COLLS[Collections]
        ASSETS[Assets]
        USERS[Users]
        PLATFORM[Platform]
    end
    
    PS --> |Full Access| ORGS
    PS --> |Full Access| COLLS
    PS --> |Full Access| ASSETS
    PS --> |Manage All| USERS
    PS --> |Full Access| PLATFORM
    
    PA --> |All Orgs| ORGS
    PA --> |All Orgs| COLLS
    PA --> |All Orgs| ASSETS
    PA --> |View All| USERS
    PA --> |Limited| PLATFORM
    
    BA --> |Own Org| ORGS
    BA --> |Own Org| COLLS
    BA --> |Own Org| ASSETS
    BA --> |Org Members| USERS
    
    BM --> |Own Org| ORGS
    BM --> |Own Org| COLLS
    BM --> |Own Org| ASSETS
    BM --> |View Org| USERS
    
    ER --> |Own Org| ORGS
    ER --> |Own Org| COLLS
    ER --> |Edit Only| ASSETS
    
    EP --> |Own Org| ORGS
    EP --> |Own Org| COLLS
    EP --> |View/Comment| ASSETS
```

## RLS (Row Level Security) Architecture

### Helper Functions

```sql
-- Check if user has platform-level access
auth.is_platform_user() -> boolean

-- Check if user has admin privileges
auth.is_admin_user() -> boolean

-- Check if user is member of organization
auth.is_organization_member(org_id uuid) -> boolean
```

### Policy Patterns

```mermaid
flowchart TD
    A[Request] --> B{Platform User?}
    B -->|Yes| C[Grant Access]
    B -->|No| D{Admin User?}
    D -->|Yes| E{In Organization?}
    E -->|Yes| C
    E -->|No| F[Deny Access]
    D -->|No| G{Organization Member?}
    G -->|Yes| H{Role Permits Action?}
    H -->|Yes| C
    H -->|No| F
    G -->|No| F
```

## Data Flow

### User Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant A as Auth
    participant DB as Database
    participant RLS as RLS Policies
    
    U->>A: Login
    A->>DB: Get user profile
    DB->>DB: Check users.role
    DB->>A: Return user + role
    A->>U: Authenticated with role
    
    U->>DB: Request resource
    DB->>RLS: Check permissions
    RLS->>RLS: Evaluate user role + membership
    RLS->>DB: Allow/Deny
    DB->>U: Response
```

### Organization Access Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant DB as Database
    
    U->>F: Request organization data
    F->>DB: Query with user context
    
    alt Platform User
        DB->>DB: Return all organizations
    else Brand User
        DB->>DB: Check organization_memberships
        DB->>DB: Return user's organizations only
    end
    
    DB->>F: Filtered results
    F->>U: Display accessible organizations
```

## Migration Strategy

### From Dual Role System

The migration from the previous dual role system involved:

1. **Schema Changes**:
   - Created new unified `user_role` enum
   - Migrated existing data based on role combinations
   - Removed `organization_role` enum and column
   - Simplified `organization_memberships` table

2. **Role Mapping**:
   ```
   user_role='user' + org_role='org_member' → brand_member
   user_role='user' + org_role='org_admin' → brand_admin
   user_role='admin' → platform_admin
   user_role='superadmin' → platform_super
   ```

3. **RLS Policy Updates**:
   - Replaced complex dual-role checks with single role lookups
   - Added helper functions for common permission patterns
   - Optimized policy performance with direct role queries

## Performance Benefits

### Before (Dual Role System)
```sql
-- Complex permission check
EXISTS (
  SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'admin'
) OR EXISTS (
  SELECT 1 FROM public.organization_memberships 
  WHERE user_id = auth.uid() 
    AND organization_id = target_org_id 
    AND role = 'org_admin'
)
```

### After (Single Role System)
```sql
-- Simple permission check
auth.is_platform_user() OR 
(auth.is_admin_user() AND auth.is_organization_member(target_org_id))
```

### Improvements
- **50% reduction** in permission logic complexity
- **Fewer database joins** in RLS policies
- **Single source of truth** for user roles
- **Clearer debugging** and maintenance

## Frontend Integration

### Role Context

```typescript
// New unified role types
type UserRole = 
  | 'platform_super' 
  | 'platform_admin' 
  | 'brand_admin' 
  | 'brand_member' 
  | 'external_retoucher' 
  | 'external_prompter';

// Enhanced context with granular checks
const {
  userRole,
  isPlatformSuper,
  isPlatformAdmin,
  isBrandAdmin,
  isBrandMember,
  isExternalRetoucher,
  isExternalPrompter,
  isAdmin, // Helper: any admin level
  isPlatformUser // Helper: platform level
} = useUserRole();
```

### Organization Access

```typescript
// Simplified organization role checking
const {
  userRole, // Role from users table
  isOrgAdmin, // Computed: platform user OR (brand_admin + member)
  isOrgMember, // Simple membership check
  isLoading
} = useOrganizationRole(organizationId);
```

## Security Considerations

### Role Elevation
- Only `platform_super` can delete users
- Only `platform_admin` and `platform_super` can create organizations
- `brand_admin` role is scoped to specific organizations via membership

### Data Isolation
- Multi-tenant isolation enforced at database level via RLS
- Platform users bypass tenant isolation for administrative access
- External users are restricted to specific asset operations only

### Audit Trail
- All role changes logged in `security_activity` table
- User role is immutable except by platform administrators
- Organization membership changes are tracked with timestamps

## Best Practices

### Role Assignment
1. Start users as `brand_member` by default
2. Promote to `brand_admin` for organization management
3. Use external roles for temporary/limited access
4. Reserve platform roles for FashionLab staff only

### Permission Checks
1. Use helper functions in RLS policies for consistency
2. Check `isPlatformUser` first for performance
3. Combine role checks with membership checks for brand roles
4. Cache role information in frontend contexts

### Development Guidelines
1. Always test permission changes with multiple role types
2. Use role-specific test users for comprehensive testing
3. Document permission requirements for new features
4. Review RLS policies during code reviews

## Future Extensibility

The single role system is designed for easy extension:

### Adding New Roles
```sql
-- Example: Adding external_photographer role
ALTER TYPE user_role ADD VALUE 'external_photographer';

-- Update helper functions if needed
CREATE OR REPLACE FUNCTION auth.is_external_user()
RETURNS boolean AS $$
BEGIN
  RETURN (
    SELECT role FROM public.users WHERE id = auth.uid()
  ) IN ('external_retoucher', 'external_prompter', 'external_photographer');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Role Hierarchies
The system can be extended with role hierarchies by updating helper functions while maintaining the simple enum structure.

### Dynamic Permissions
Future features could add permission overrides or temporary access grants while keeping the core role system intact.