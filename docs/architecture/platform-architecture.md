# Platform Architecture

## Technology Stack
- **Frontend**: React 18 with TypeScript
- **UI Library**: shadcn/ui (built on Radix UI)
- **Styling**: Tailwind CSS
- **State Management**: React Query (Tanstack Query) + Context API
- **Database/Auth/Storage**: Supabase (PostgreSQL, Auth, Storage)
- **Build Tool**: Vite
- **Testing**: Vitest (unit) + Playwright (E2E)

## Architecture Pattern
This is a multi-tenant SaaS platform with role-based access control:

1. **Multi-Tenancy**: Organizations (clients) have isolated data
2. **User Roles**: Single role system - platform_super, platform_admin, brand_admin, brand_member, external_retoucher, external_prompter
3. **Asset Pipeline**: Storage buckets for client assets and product assets with automatic image processing
4. **Workflow System**: Assets progress through stages (upload → draft → upscale → retouch → final)

## Key Architectural Components

### Authentication & Authorization
- Supabase Auth handles authentication
- Role-based access control (RBAC) at database level
- Context providers wrap the app:
  - `SupabaseContext`: Manages auth state and session
  - `UserRoleContext`: Manages user role fetching and caching
  - `OrganizationContext`: Manages organization membership and selection

### Database Schema
- **organizations**: Multi-tenant support
- **users**: User accounts with role assignments
- **clients**: Fashion brand clients (Fila, H&M, etc.)
- **collections**: Projects/campaigns for clients
- **assets**: Images with workflow stages and metadata
- **products**: Individual products within collections
- **tags**: Asset categorization system

### Storage Architecture
Supabase Storage with two main buckets:
- `client-assets`: Direct client uploads
- `product-assets`: Processed fashion product images
- `avatars`: User avatars

Images are automatically processed into:
- Original
- Compressed (WebP)
- Thumbnail versions

### Component Organization
- `/components/ui/`: shadcn/ui components (Button, Dialog, etc.)
- `/components/layout/`: App-wide layout components
- `/components/auth/`: Authentication wrappers
- `/components/assets/`: Asset management components
- `/pages/`: Route-level components
- `/contexts/`: React Context providers
- `/hooks/`: Custom React hooks
- `/lib/`: Utility functions and Supabase client

### Important Notes
- Always check user roles and permissions before operations
- Use TypeScript types from `/types/database.types.ts`
- Handle loading and error states in components
- Image processing happens automatically on upload
- Bulk operations (ZIP downloads, bulk delete) should show progress