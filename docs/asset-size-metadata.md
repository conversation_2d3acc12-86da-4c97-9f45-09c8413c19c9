# Asset Size Metadata Guide

## Overview

The FashionLab platform uses a standardized size system for all assets. Assets can be filtered by size in both the Collection View and Asset Compare View. Size information is stored in the asset's metadata field.

## Standard Sizes

The platform uses a fixed set of standard sizes:
- **XS** - Extra Small
- **S** - Small  
- **M** - Medium
- **L** - Large
- **XL** - Extra Large

## How Size Filtering Works

1. All assets have size metadata stored in their `metadata.size` field
2. The platform provides fixed size options (XS, S, M, L, XL) for filtering
3. Users can filter assets by selecting one or more sizes
4. The compare view and collection view both support size filtering

## Adding Size to Asset Metadata

### Database Structure

Assets have a `metadata` field (JSONB) where size information should be stored:

```json
{
  "size": "M",
  // or
  "productSize": "M",
  // other metadata...
}
```

### Methods to Add Size Metadata

#### 1. Through the UI (Recommended)

Use the Bulk Size Manager in the collection view:
1. Select one or more assets
2. Click the "Size" button in the floating action bar
3. Choose from the standard sizes (XS, S, M, L, XL)
4. Click "Assign Size"

#### 2. During Upload

When uploading assets, include size in the metadata:

```typescript
const assetData = {
  collection_id: collectionId,
  product_id: productId,
  file_name: file.name,
  file_path: filePath,
  file_type: file.type,
  file_size: file.size,
  workflow_stage: 'upload',
  metadata: {
    size: 'M',  // Add size here (must be one of: XS, S, M, L, XL)
    // other metadata...
  }
};
```

#### 3. Bulk Update via SQL

Update existing assets with size information:

```sql
-- Update assets for a specific product with size
UPDATE assets 
SET metadata = jsonb_set(
  COALESCE(metadata, '{}'::jsonb),
  '{size}',
  '"M"'::jsonb
)
WHERE product_id = 'product-uuid-here'
AND file_name LIKE '%_M_%';  -- Example: matching size from filename

-- Update multiple assets based on filename patterns
UPDATE assets 
SET metadata = jsonb_set(
  COALESCE(metadata, '{}'::jsonb),
  '{size}',
  CASE 
    WHEN file_name LIKE '%_S_%' THEN '"S"'::jsonb
    WHEN file_name LIKE '%_M_%' THEN '"M"'::jsonb
    WHEN file_name LIKE '%_L_%' THEN '"L"'::jsonb
    WHEN file_name LIKE '%_XL_%' THEN '"XL"'::jsonb
    ELSE metadata->'size'
  END
)
WHERE product_id IN (
  SELECT id FROM products 
  WHERE collection_id = 'collection-uuid-here'
);
```

#### 4. Via Supabase Dashboard

1. Navigate to the `assets` table
2. Find the asset you want to update
3. Edit the `metadata` column
4. Add or update the size field:
   ```json
   {
     "size": "L"
   }
   ```

#### 5. Programmatically via API

```typescript
// Update single asset
const { error } = await supabase
  .from('assets')
  .update({ 
    metadata: { 
      ...existingMetadata,
      size: 'M' 
    } 
  })
  .eq('id', assetId);

// Bulk update assets
const assetIds = ['id1', 'id2', 'id3'];
const { error } = await supabase
  .from('assets')
  .update({ 
    metadata: { size: 'L' } 
  })
  .in('id', assetIds);
```

## Best Practices

1. **Use Standard Sizes**: Always use one of the standard sizes: XS, S, M, L, XL
2. **Naming Convention**: Use `size` as the metadata key (not `productSize`)
3. **Validation**: Ensure size values match exactly (case-sensitive)
4. **Bulk Operations**: Use the UI bulk size manager for updating multiple assets
5. **Consistency**: Apply sizes consistently across all assets in a product

## Example Workflow

1. Upload assets to a collection

2. Select multiple assets and assign sizes:
   - Select assets in the collection view
   - Click "Size" in the floating action bar
   - Choose the appropriate size
   - Click "Assign Size"

3. Filter by size in the Collection View:
   - Open the filter sidebar
   - Select sizes under "Filter by Size"
   - View only assets matching selected sizes

4. Use size filtering in Compare View:
   - Navigate to Compare View
   - Select sizes in the left sidebar
   - Compare only assets of selected sizes across workflow stages

## Troubleshooting

- **Assets not showing when filter applied**: Check that assets have size in metadata
- **Size filter not matching**: Ensure exact case match (e.g., "M" not "m")
- **Missing assets**: Verify the metadata field contains valid JSON with size property

## UI Features

### Collection View
- **Filter Sidebar**: Fixed size options (XS, S, M, L, XL) for filtering
- **Bulk Size Manager**: Assign sizes to multiple assets at once
- **Floating Action Bar**: Access size management via the "Size" button

### Compare View  
- **Size Filter**: Filter assets by size in the left sidebar
- **Size Badges**: Visual size indicators on asset cards
- **Multi-size Selection**: Compare multiple sizes simultaneously

## Database Migration

A migration has been created to ensure all existing assets have size metadata. The migration:
- Assigns random sizes to assets without size metadata
- Uses the standard size set (XS, S, M, L, XL)
- Preserves existing size data if present

Migration file: `supabase/migrations/20250113_add_asset_sizes.sql`