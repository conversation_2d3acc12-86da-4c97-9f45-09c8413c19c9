# Deployment Workflow Diagrams

Visual representation of FashionLab deployment workflows.

## Standard Feature Deployment Flow

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Feature Branch │ ──> │   Main Branch   │ ──> │    Staging      │
│   (FAS-XXX)    │     │                 │     │  (Auto-Deploy)  │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                          │
                                                          ▼
                                                 ┌─────────────────┐
                                                 │    Testing      │
                                                 │  (QA Review)    │
                                                 └────────┬────────┘
                                                          │
                                                          ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Production    │ <── │  Production PR  │ <── │ Feature Flags   │
│   (Manual)      │     │  (from main)    │     │  Updated        │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Steps:
1. **Feature Development**: Create feature branch from main
2. **PR to Main**: Code review and merge
3. **Auto-Deploy**: Vercel automatically deploys to staging
4. **Testing**: QA and stakeholder review on staging
5. **Feature Flag**: Enable feature in production config
6. **Production PR**: Create PR from main to production
7. **Deploy**: Manual database migrations if needed

## Hotfix Deployment Flow

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Production    │ ──> │  Hotfix Branch  │ ──> │  Direct PR to   │
│    (current)    │     │   (FAS-XXX)     │     │   Production    │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                          │
                                                          ▼
                                                 ┌─────────────────┐
                                                 │   Production    │
                                                 │   (Updated)     │
                                                 └────────┬────────┘
                                                          │
                                                          ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Main Branch   │ <── │   Sync Merge    │ <── │  Staging Sync   │
│   (Updated)     │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Steps:
1. **Identify Issue**: Critical bug in production
2. **Hotfix Branch**: Create from production branch
3. **Fix & Test**: Make minimal fix, test locally
4. **Direct PR**: PR straight to production
5. **Deploy**: Immediate deployment after merge
6. **Sync Back**: Merge production back to main
7. **Verify**: Ensure staging has the fix too

## Feature Flag Lifecycle

```
┌─────────────────────────────────────────────────────────────────┐
│                        DEVELOPMENT                              │
│  isFeatureEnabled() returns: true (all features enabled)       │
└───────────────────────────┬─────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                         STAGING                                 │
│  Feature Flag: { staging: true, production: false }            │
│  Feature is VISIBLE to testers                                 │
└───────────────────────────┬─────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                       PRODUCTION                                │
│  Feature Flag: { staging: true, production: false }            │
│  Feature is HIDDEN from users                                  │
└───────────────────────────┬─────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                    PRODUCTION RELEASE                           │
│  Feature Flag: { staging: true, production: true }             │
│  Feature is VISIBLE to all users                               │
└─────────────────────────────────────────────────────────────────┘
```

## Decision Tree: Which Workflow to Use?

```
                    Is it a critical bug?
                           │
                    ┌──────┴──────┐
                    │             │
                   YES            NO
                    │             │
                    ▼             ▼
            Does it affect    Is it a new
            user data/auth?    feature?
                    │             │
            ┌───────┴───────┐    YES
            │               │     │
           YES              NO    ▼
            │               │   Standard
            ▼               ▼   Workflow
        Emergency       Hotfix   with
         Hotfix        Workflow  Feature
     (with CEO/CTO              Flag
       approval)
```

## Git Branch Strategy

```
production ─────●─────────────●────────────────────●──────>
                 ╲           ╱                    ╱
                  ╲         ╱                    ╱
                   hotfix-╱                     ╱
                                               ╱
main ──────●────●────●────●────●────●────●───╱─●──────>
            ╲  ╱      ╲  ╱      ╲  ╱         ╱
             ╲╱        ╲╱        ╲╱         ╱
          feature/  feature/  feature/     ╱
           FAS-1     FAS-2     FAS-3      ╱
                                         ╱
                              Production PR
```

## Environment Status Indicators

```
┌─────────────────────────────────────────────────────────────┐
│ DEVELOPMENT                                                 │
│ ● All features enabled                                      │
│ ● Local database                                            │
│ ● Hot reload active                                         │
│ ● Debug mode on                                             │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│ STAGING                                                     │
│ ● Selected features enabled via flags                       │
│ ● Staging database (qnfmiotatmkoumlymynq)                  │
│ ● Auto-deploy from main                                     │
│ ● Test data available                                       │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│ PRODUCTION                                                  │
│ ● Production features only                                  │
│ ● Production database (cpelxqvcjnbpnphttzsn)                │
│ ● Manual deploy from production branch                      │
│ ● Real user data - HANDLE WITH CARE                        │
└─────────────────────────────────────────────────────────────┘
```