# Feature Flags and Deployment Workflow

This document describes the feature flag system and deployment workflows for the FashionLab platform.

## Table of Contents
- [Feature Flag System](#feature-flag-system)
- [Deployment Workflows](#deployment-workflows)
- [Visual Diagrams](./deployment-workflow-diagram.md)
- [Examples](#examples)
- [Best Practices](#best-practices)

## Feature Flag System

### Overview
The feature flag system allows you to deploy features to staging for testing while keeping them hidden in production until they're ready for release.

### Configuration
Feature flags are defined in `src/utils/featureFlags.ts`:

```typescript
export const FEATURES = {
  ASSET_COMPARE_VIEW: {
    staging: true,
    production: false
  },
  ENHANCED_COLLECTION_BRIEF: {
    staging: true,
    production: false
  },
  // Add more features as needed
} as const;
```

### Usage

#### Basic Usage
Use `isFeatureEnabled()` to conditionally render components:

```tsx
import { isFeatureEnabled } from '../utils/featureFlags';

function MyComponent() {
  return (
    <div>
      {isFeatureEnabled('ASSET_COMPARE_VIEW') && (
        <AssetCompareButton />
      )}
    </div>
  );
}
```

#### With Helper Function
Use `withFeature()` for cleaner conditional rendering:

```tsx
import { withFeature } from '../utils/featureFlags';

function MyComponent() {
  return (
    <div>
      {withFeature('ASSET_COMPARE_VIEW', <AssetCompareButton />)}
    </div>
  );
}
```

#### In Routes
Conditionally render routes based on feature flags:

```tsx
{isFeatureEnabled('ASSET_COMPARE_VIEW') && (
  <Route 
    path="/organizations/:orgId/collections/:collectionId/compare" 
    element={<AssetCompareView />} 
  />
)}
```

### How It Works
1. In development: All features are enabled
2. In staging: Features with `staging: true` are enabled
3. In production: Features with `production: true` are enabled

### Promoting Features to Production
When a feature is ready for production:

1. Update the feature flag in `src/utils/featureFlags.ts`:
   ```typescript
   ASSET_COMPARE_VIEW: {
     staging: true,
     production: true  // Changed from false
   }
   ```

2. Commit and push the change:
   ```bash
   git add src/utils/featureFlags.ts
   git commit -m "feat: Enable ASSET_COMPARE_VIEW in production"
   git push
   ```

3. Deploy to production following the standard workflow

## Deployment Workflows

### Standard Feature Release Workflow

For regular feature development:

```mermaid
graph LR
    A[Feature Branch] --> B[Main Branch]
    B --> C[Staging Deploy]
    C --> D[Testing]
    D --> E[Production PR]
    E --> F[Production Deploy]
```

1. **Develop on feature branch**
   ```bash
   git checkout -b feature/fas-123-new-feature
   # Make changes
   git commit -m "feat: Implement new feature"
   git push
   ```

2. **Create PR to main**
   ```bash
   gh pr create --base main --title "FAS-123: New Feature"
   ```

3. **Merge to main** → Auto-deploys to staging

4. **Test on staging** → https://staging.fashionlab.tech

5. **Create PR to production**
   ```bash
   gh pr create --base production --head main --title "Deploy: New features"
   ```

6. **Deploy database migrations** (if any)
   ```bash
   npm run migrate:production
   ```

### Urgent Fix Workflow

For critical production fixes that need to bypass staging:

```mermaid
graph LR
    A[Production Branch] --> B[Hotfix Branch]
    B --> C[Direct PR to Production]
    C --> D[Production Deploy]
    D --> E[Sync to Main]
```

1. **Create hotfix branch from production**
   ```bash
   git checkout production
   git pull origin production
   git checkout -b hotfix/fas-123-urgent-fix
   ```

2. **Make the fix**
   ```bash
   # Make changes
   git commit -m "fix: Urgent fix for production issue"
   git push
   ```

3. **Create PR directly to production**
   ```bash
   gh pr create --base production --title "HOTFIX: FAS-123 - Critical fix"
   ```

4. **Deploy immediately after merge**

5. **Sync fix back to main**
   ```bash
   git checkout main
   git pull origin main
   git merge production
   git push
   ```

## Examples

### Example 1: Adding a New Staging-Only Feature

1. **Add feature flag**
   ```typescript
   // src/utils/featureFlags.ts
   export const FEATURES = {
     // ... existing features
     FREELANCER_MANAGEMENT: {
       staging: true,
       production: false
     }
   } as const;
   ```

2. **Use in component**
   ```tsx
   // src/components/layout/navigation-items.tsx
   import { isFeatureEnabled } from '../utils/featureFlags';

   export const navigationItems = [
     // ... existing items
     ...(isFeatureEnabled('FREELANCER_MANAGEMENT') ? [{
       name: 'Freelancers',
       href: '/freelancers',
       icon: Users
     }] : [])
   ];
   ```

3. **Add route conditionally**
   ```tsx
   // src/App.tsx
   {isFeatureEnabled('FREELANCER_MANAGEMENT') && (
     <Route path="/freelancers" element={<FreelancerManagement />} />
   )}
   ```

### Example 2: Production Hotfix

Scenario: Users can't upload assets in production

1. **Create hotfix branch**
   ```bash
   git checkout production
   git checkout -b hotfix/fas-99-fix-asset-upload
   ```

2. **Fix the issue**
   ```tsx
   // Fix in src/components/asset-management/AssetUpload.tsx
   ```

3. **Deploy directly to production**
   ```bash
   git push
   gh pr create --base production --title "HOTFIX: FAS-99 - Fix asset upload"
   # After merge, migrations if needed:
   npm run migrate:production
   ```

4. **Sync to main**
   ```bash
   git checkout main
   git merge production
   git push
   ```

## Best Practices

### Feature Flags
1. **Naming Convention**: Use SCREAMING_SNAKE_CASE for feature names
2. **Descriptive Names**: Choose names that clearly describe the feature
3. **Clean Up**: Remove feature flags once features are stable in production
4. **Document**: Add comments explaining what each feature flag controls

### Deployment
1. **Always Link to Linear**: Reference issue numbers in commits and PRs
2. **Test on Staging**: Never skip staging unless it's a critical hotfix
3. **Database Migrations**: Always run migrations before deploying frontend
4. **Monitor After Deploy**: Watch logs and metrics for 30 minutes post-deploy
5. **Rollback Plan**: Know how to revert changes if issues arise

### Communication
1. **Update Linear Issues**:
   - Move to "In Review" when deployed to staging
   - Move to "Done" only after production deployment
2. **Notify Team**: Post in Slack when deploying to production
3. **Document Changes**: Update CHANGELOG.md for significant features

### Emergency Procedures
If something goes wrong in production:

1. **Don't Panic**: Assess the severity and impact
2. **Rollback if Needed**: 
   ```bash
   # Revert to previous commit
   git revert HEAD
   git push
   ```
3. **Communicate**: Notify the team immediately
4. **Fix Forward**: Sometimes it's better to fix the issue than rollback
5. **Post-Mortem**: Document what happened and how to prevent it

## Environment Variables

The feature flag system uses the `VITE_ENVIRONMENT` variable:
- `development`: All features enabled
- `staging`: Staging features enabled
- `production`: Production features enabled

Ensure this is correctly set in your deployment environments.