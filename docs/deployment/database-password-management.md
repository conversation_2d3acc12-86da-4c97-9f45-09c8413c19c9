# Database Password Management

This guide explains how to securely store and use database passwords for deployments and migrations.

## Setting Up Passwords

### 1. Add to .env.local

Add your database passwords to `.env.local` (this file is gitignored):

```bash
# Database passwords for direct access
STAGING_DB_PASSWORD=your_staging_password_here
PRODUCTION_DB_PASSWORD=your_production_password_here
```

### 2. Get Passwords from Supabase

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Navigate to Settings → Database
4. Copy the database password

## Using Passwords with <PERSON>

Once you've set up the passwords in `.env.local`, <PERSON> can use them via the npm scripts:

### Run Single Migration
```bash
# Staging
npm run db:migrate:staging -- supabase/migrations/20250612065409_add_storage_buckets_select_policy.sql

# Production
npm run db:migrate:production -- supabase/migrations/20250612065409_add_storage_buckets_select_policy.sql
```

### Direct Database Access
The scripts automatically use the pooler URL to avoid IPv6 issues:
- Staging: `aws-0-eu-central-1.pooler.supabase.com`
- Production: `aws-0-eu-central-1.pooler.supabase.com`

## Security Best Practices

### DO ✅
- Store passwords in `.env.local` only
- Use the provided npm scripts
- Rotate passwords regularly
- Use different passwords for each environment

### DON'T ❌
- Commit passwords to Git
- Share passwords in chat/email
- Use production passwords locally
- Store passwords in plain text files

## Alternative: 1Password CLI Integration

For teams using 1Password, you can integrate with the CLI:

```bash
# Install 1Password CLI
brew install 1password-cli

# Set up references in .env.local
STAGING_DB_PASSWORD=op://Team/Staging Database/password
PRODUCTION_DB_PASSWORD=op://Team/Production Database/password

# Run with 1Password
op run -- npm run db:migrate:staging -- path/to/migration.sql
```

## Troubleshooting

### "Password not found" Error
- Check that `.env.local` exists
- Verify the environment variable names match exactly
- Ensure no extra spaces or quotes around the password

### "Connection refused" Error
- The script uses pooler URLs to avoid IPv6 issues
- If still failing, check network connectivity
- Verify the password is correct

### "Permission denied" Error
- Database passwords are case-sensitive
- Special characters may need escaping
- Try resetting the password in Supabase Dashboard

## Manual Migration (Fallback)

If the scripts don't work, you can manually run migrations:

```bash
# Set password as environment variable (temporary)
export DB_PASS="your_password_here"

# Run migration
psql "postgresql://postgres.PROJECT_REF:$<EMAIL>:5432/postgres" -f migration.sql

# Clear password from history
unset DB_PASS
```