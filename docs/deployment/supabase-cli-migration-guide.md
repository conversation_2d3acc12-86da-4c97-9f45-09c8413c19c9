# Supabase CLI Migration Management Plan

## Overview

This guide outlines how to properly use the Supabase CLI for database migrations, diffs, and troubleshooting.

## 1. Project Linking

First, ensure you're linked to the correct project:

```bash
# Link to staging project
supabase link --project-ref qnfmiotatmkoumlymynq

# Verify the link
supabase projects list
```

## 2. Check Current State

### A. List migrations
```bash
# See which migrations have been applied
supabase migration list --project-ref qnfmiotatmkoumlymynq
```

### B. Check database diff
```bash
# Compare local schema with remote
supabase db diff --project-ref qnfmiotatmkoumlymynq

# Generate a diff as SQL
supabase db diff --project-ref qnfmiotatmkoumlymynq --use-migra

# Save diff to a file
supabase db diff --project-ref qnfmiotatmkoumlymynq > staging-diff.sql
```

### C. Pull remote schema
```bash
# Pull the current remote schema to compare
supabase db pull --project-ref qnfmiotatmkoumlymynq
```

## 3. Diagnose Migration Issues

### A. Dry run migrations
```bash
# See what would happen without applying
supabase db push --dry-run --project-ref qnfmiotatmkoumlymynq
```

### B. Execute SQL directly via CLI
```bash
# Run SQL file directly on remote database
supabase db execute --project-ref qnfmiotatmkoumlymynq < scripts/fix-constraints.sql

# Run inline SQL
echo "SELECT * FROM migrations;" | supabase db execute --project-ref qnfmiotatmkoumlymynq
```

## 4. Fix Constraint Issues via CLI

Create a fix script and run it:

```bash
# Create constraint fix
cat > fix-constraints.sql << 'EOF'
-- Insert missing organizations for orphaned client_ids
INSERT INTO organizations (id, name, created_at, updated_at)
SELECT DISTINCT 
    c.client_id,
    COALESCE(cl.name, 'Organization for ' || c.client_id),
    COALESCE(cl.created_at, NOW()),
    NOW()
FROM collections c
LEFT JOIN organizations o ON c.client_id = o.id
LEFT JOIN clients cl ON c.client_id = cl.id
WHERE c.client_id IS NOT NULL
  AND o.id IS NULL
ON CONFLICT (id) DO NOTHING;
EOF

# Execute the fix
supabase db execute --project-ref qnfmiotatmkoumlymynq < fix-constraints.sql
```

## 5. Apply Migrations

After fixing constraints:

```bash
# Push all pending migrations
supabase db push --project-ref qnfmiotatmkoumlymynq

# Or push with verbose output
supabase db push --project-ref qnfmiotatmkoumlymynq --debug
```

## 6. Verify Success

```bash
# Check migration status again
supabase migration list --project-ref qnfmiotatmkoumlymynq

# Verify schema is correct
supabase db diff --project-ref qnfmiotatmkoumlymynq
```

## 7. Troubleshooting Commands

### Reset remote to specific migration
```bash
# Roll back to a specific migration (use with caution!)
supabase db reset --project-ref qnfmiotatmkoumlymynq --to-migration <migration-id>
```

### Inspect remote database
```bash
# Open a psql session
supabase db remote --project-ref qnfmiotatmkoumlymynq

# Or run a query directly
supabase db remote --project-ref qnfmiotatmkoumlymynq --query "SELECT version FROM schema_migrations ORDER BY version DESC LIMIT 5;"
```

### Check logs
```bash
# View migration logs
supabase db logs --project-ref qnfmiotatmkoumlymynq
```

## Complete Workflow for Our Current Issue

1. **Check current state**:
   ```bash
   supabase link --project-ref qnfmiotatmkoumlymynq
   supabase migration list
   supabase db diff
   ```

2. **Fix constraints**:
   ```bash
   supabase db execute --project-ref qnfmiotatmkoumlymynq < scripts/fix-staging-constraint-manual-v2.sql
   ```

3. **Apply migrations**:
   ```bash
   supabase db push --project-ref qnfmiotatmkoumlymynq
   ```

4. **Verify**:
   ```bash
   supabase migration list
   supabase db diff
   ```

## Environment Variables

You can set these to avoid repeating flags:
```bash
export SUPABASE_PROJECT_ID=qnfmiotatmkoumlymynq
export SUPABASE_DB_PASSWORD=WMYUyfkEhYdSl4we
```

Then commands become simpler:
```bash
supabase migration list
supabase db push
```

## Best Practices

1. Always check diff before pushing
2. Use dry-run for complex migrations
3. Keep local and remote schemas in sync
4. Document any manual SQL fixes
5. Test migrations locally first with `supabase db reset`