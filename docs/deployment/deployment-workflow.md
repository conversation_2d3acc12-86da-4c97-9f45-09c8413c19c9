# Deployment & Workflow Guide

## Overview

This document outlines the Git workflow, deployment processes, and Supabase project configurations used in the FashionLab project.

## Git Workflow

### Branching Strategy

1. **Feature Branches**
   - All new features and bug fixes are developed on dedicated feature branches
   - Branch naming convention: `feature/feature-name` or `bugfix/issue-description`

2. **Main Branches**
   - `main` - Staging branch (auto-deploys to staging)
   - `production` - Production branch (auto-deploys to production)

3. **Workflow**
   - Develop locally on feature branches
   - Create PR to merge feature branch into `main`
   - Test on staging after merge
   - Create PR from `main` to `production` for production deployment

```bash
# Creating a new feature branch
git checkout main
git pull
git checkout -b feature/new-feature

# After completing development
git push origin feature/new-feature
# Create PR on GitHub to merge into main
```

## Deployment Process

### Environments

| Environment | Branch | URL | Description |
|-------------|--------|-----|-------------|
| Development | N/A | localhost | Local development environment |
| Staging | `main` | staging.fashionlab.tech | Staging environment for testing |
| Production | `production` | app.fashionlab.tech | Production environment |

### Deployment Architecture

The project uses a simplified deployment approach:

1. **Frontend Deployment (Vercel)**
   - Automatic deployments triggered by Git pushes
   - No manual intervention required
   - `main` branch → staging.fashionlab.tech
   - `production` branch → app.fashionlab.tech

2. **Database Migrations (Supabase)**
   - Manual deployment via Supabase CLI
   - Must be pushed after frontend deployment
   - Requires authentication with Supabase access token

### Staging Deployment

```bash
# 1. Push code to main branch (triggers Vercel deployment)
git checkout main
git merge feature/your-feature
git push origin main

# 2. Wait for Vercel deployment to complete

# 3. Push database migrations manually
supabase link --project-ref qnfmiotatmkoumlymynq
supabase db push
```

### Production Deployment

```bash
# 1. Create PR from main to production
gh pr create --base production --head main --title "Deploy: description"

# 2. Merge PR (triggers Vercel deployment)

# 3. Push database migrations manually
supabase link --project-ref cpelxqvcjnbpnphttzsn
supabase db push
```

## Supabase Configuration

### Projects

| Environment | Project Name | Project ID | Description |
|-------------|--------------|------------|-------------|
| Development | Local Docker | N/A | Local Supabase instance for development |
| Staging | [STAGING] Fashionlab Platform | qnfmiotatmkoumlymynq | Staging database for testing |
| Production | [PRODUCTION] Fashionlab Platform | cpelxqvcjnbpnphttzsn | Production database |

### Local Development Setup

```bash
# Start local Supabase
supabase start

# Stop local Supabase
supabase stop

# Reset local database (applies all migrations)
supabase db reset
```

### Database Migration Workflow

1. **Create Migration**
   ```bash
   supabase migration new descriptive_name
   ```

2. **Test Locally**
   ```bash
   supabase db reset
   ```

3. **Deploy to Staging**
   ```bash
   supabase link --project-ref qnfmiotatmkoumlymynq
   supabase db push
   ```

4. **Deploy to Production** (after staging verification)
   ```bash
   supabase link --project-ref cpelxqvcjnbpnphttzsn
   supabase db push
   ```

### Connection Strings (for troubleshooting)

If standard linking fails, use direct database URLs:

**Staging:**
```bash
supabase db push --db-url "postgresql://postgres.qnfmiotatmkoumlymynq:<password>@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"
```

**Production:**
```bash
supabase db push --db-url "postgresql://postgres.cpelxqvcjnbpnphttzsn:<password>@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"
```

## Environment Variables

### Vercel Configuration

Environment variables are managed in Vercel project settings:

**Staging Variables:**
- `VITE_SUPABASE_URL`: Staging Supabase URL
- `VITE_SUPABASE_ANON_KEY`: Staging anon key
- `VITE_ENVIRONMENT`: `staging`

**Production Variables:**
- `VITE_SUPABASE_URL`: Production Supabase URL
- `VITE_SUPABASE_ANON_KEY`: Production anon key
- `VITE_ENVIRONMENT`: `production`

### Local Development

Create `.env.local` file:
```env
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=your_local_anon_key
VITE_ENVIRONMENT=development
```

## Deployment Checklist

### Before Staging Deployment
- [ ] All tests pass locally
- [ ] Database migrations tested with `supabase db reset`
- [ ] Code reviewed and approved
- [ ] Feature branch merged to `main`

### Before Production Deployment
- [ ] Feature tested thoroughly on staging
- [ ] Database migrations verified on staging
- [ ] No critical issues in staging logs
- [ ] PR created from `main` to `production`
- [ ] PR reviewed and approved

### After Deployment
- [ ] Verify frontend deployment in Vercel dashboard
- [ ] Push database migrations manually
- [ ] Check health endpoint (`/health.json`)
- [ ] Test critical user flows
- [ ] Monitor error logs

## Troubleshooting

### Common Issues

1. **Vercel Deployment Failures**
   - Check build logs in Vercel dashboard
   - Verify environment variables are set correctly
   - Test build locally with `npm run build`

2. **Database Migration Issues**
   - Ensure correct project is linked: `supabase status`
   - Use pooler connection string for SASL errors
   - Check migration syntax locally: `supabase db reset`

3. **Environment Mismatch**
   - Verify `VITE_ENVIRONMENT` is set correctly
   - Check Supabase project URLs match environment
   - Confirm correct branch is deployed

## Important Notes

1. **No GitHub Actions**: All CI/CD workflows have been removed
2. **Manual Migrations**: Database migrations must be pushed manually after each deployment
3. **Order Matters**: Always deploy frontend first, then database migrations
4. **Test First**: Always test on staging before production deployment

## Change Log

| Date | Version | Description |
|------|---------|-------------|
| 2025-06-10 | 2.0 | Removed GitHub Actions, simplified to Vercel + manual migrations |
| [Previous] | 1.0 | Initial documentation |