# Staging Setup Guide

## Test Users for Staging

Since staging doesn't receive seed data, test users must be created manually after deployment.

### Creating Test Users

1. Go to Supabase SQL Editor for staging: https://app.supabase.com/project/qnfmiotatmkoumlymynq
2. Run the script from `/scripts/create-complete-staging-user.sql`

### Available Test Users

| Email | Password | Role | Organization |
|-------|----------|------|--------------|
| <EMAIL> | password123 | brand_admin | Brand A |

### Why Manual Creation?

- Staging and production databases are both named 'postgres'
- Environment detection in migrations is unreliable
- Manual creation ensures test users exist only where needed
- Prevents accidental test data in production

### Important Notes

- These users are for testing only
- Never create test users in production
- Test users should use @fashionlab.test domain
- Always use strong passwords in production