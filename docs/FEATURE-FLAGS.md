# Feature Flags Guide

## Overview

Feature flags allow you to deploy code to production while controlling which features are visible in different environments. This is useful when:
- A feature is ready for testing on staging but not production
- You want to gradually roll out features
- You need to quickly disable a problematic feature

## Current Implementation

Feature flags are configured in `/src/utils/featureFlags.ts`:

```typescript
export const FEATURES = {
  ASSET_COMPARE_VIEW: {
    staging: true,      // Enabled on staging
    production: false   // Disabled on production
  },
  // Add more features here
}
```

## How It Works

1. **Environment Detection**: The system automatically detects the environment from `VITE_ENVIRONMENT`
2. **Development**: All features are enabled in local development
3. **Staging/Production**: Features follow their configured settings

## Managing Feature Deployments

### Scenario: Deploy other changes while keeping a feature on staging only

When you have a feature (like Asset Compare View) that's not ready for production:

1. **Keep the feature flag disabled for production**:
   ```typescript
   ASSET_COMPARE_VIEW: {
     staging: true,
     production: false
   }
   ```

2. **Deploy normally**:
   ```bash
   # Push to staging
   git push origin main
   
   # Merge to production (feature will be hidden)
   git checkout production
   git merge main
   git push origin production
   ```

3. **The feature code deploys but remains hidden in production**

### Enabling a Feature in Production

When the feature is ready:

1. **Update the feature flag**:
   ```typescript
   ASSET_COMPARE_VIEW: {
     staging: true,
     production: true  // Now enabled
   }
   ```

2. **Commit and deploy**:
   ```bash
   git add src/utils/featureFlags.ts
   git commit -m "feat: enable Asset Compare View in production"
   git push origin main
   
   # Deploy to production
   git checkout production
   git merge main
   git push origin production
   ```

## Usage Examples

### Conditional Route
```tsx
{isFeatureEnabled('ASSET_COMPARE_VIEW') && (
  <Route path="/compare" element={<CompareView />} />
)}
```

### Conditional UI Element
```tsx
{isFeatureEnabled('ASSET_COMPARE_VIEW') && (
  <Button onClick={handleCompare}>
    Compare Assets
  </Button>
)}
```

### Conditional Logic
```typescript
if (isFeatureEnabled('NEW_ALGORITHM')) {
  return processWithNewAlgorithm(data);
} else {
  return processWithOldAlgorithm(data);
}
```

## Alternative Approaches

### 1. Git Branching (More Complex)
- Maintain separate `staging` and `production` branches
- Cherry-pick specific commits to production
- Requires careful git management

### 2. Environment Variables
- Use environment variables like `VITE_ENABLE_COMPARE_VIEW`
- Set different values in Vercel for each environment
- Good for simple on/off switches

### 3. Database-Driven Flags
- Store feature flags in Supabase
- Allows runtime changes without deployment
- More complex but more flexible

## Best Practices

1. **Name flags clearly**: Use descriptive names like `ASSET_COMPARE_VIEW` not `FEATURE_1`
2. **Document features**: Add comments explaining what each flag controls
3. **Clean up old flags**: Remove flags for features that are fully rolled out
4. **Test both states**: Always test with flags both enabled and disabled
5. **Default to disabled**: New features should default to `false` in production

## Current Feature Flags

| Feature | Description | Staging | Production |
|---------|-------------|---------|------------|
| ASSET_COMPARE_VIEW | Asset comparison grid view | ✅ | ❌ |

## Adding a New Feature Flag

1. Add to `FEATURES` object in `/src/utils/featureFlags.ts`
2. Set appropriate values for each environment
3. Use `isFeatureEnabled()` to conditionally render/execute
4. Update this documentation