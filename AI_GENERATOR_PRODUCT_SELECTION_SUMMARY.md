# AI Generator Product Selection Implementation Summary

## Current Status
- The AI Generator UI is working with hardcoded mock products
- OpenAI integration for garment analysis is complete and working
- V2 API integration is functional with 4-image input system
- Need to implement real product selection from collection data

## Key Requirements from Valentyn's Write-up

### Image Slot Structure
1. **Image 1**: Always a model face/body (from Google Drive library)
2. **Image 2**: Main garment/product (THE KEY REQUIREMENT!)
3. **Image 3**: Additional garment
4. **Image 4**: Additional garment OR specific setting/background

### Main Product Placement
- The main selected product goes in slot 2 (first garment slot)
- When user selects a product with multiple images, pick the best one (likely front view)
- Images 3 & 4 are for complementary/styling garments

### Prompt Structure Requirements
1. Model description (face and body)
2. Main garment description
3. Additional garment 1 description
4. Additional garment 2 description (if used)
5. Setting/background
6. Camera angle

## Current Implementation Status

### What's Working
- ✅ Generate button functional with V2 API
- ✅ OpenAI integration for analyzing garment images
- ✅ Auto-add selected garments to the 3 garment input slots
- ✅ Multiple file upload support for garments
- ✅ Image compression to fix stream abort errors

### What Needs Implementation
1. **Product Selection UI** (IN PROGRESS)
   - Replace mock products with real collection products
   - Add product navigation/selection interface
   - Auto-populate main product in slot 2

2. **Test Collection Setup** (IN PROGRESS)
   - Create products table if missing
   - Copy production collection to staging:
     - Collection ID: d8422e7a-8f5f-4445-8e41-c1e9e59799b6
     - Organization ID: cb251999-232a-416a-94b6-d4e5aab425f9
   - Convert tags to products for proper testing

3. **Database Schema Needs**
   ```sql
   -- Products table structure needed
   CREATE TABLE public.products (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     collection_id UUID NOT NULL REFERENCES public.collections(id),
     sku TEXT NOT NULL,
     name TEXT NOT NULL,
     description TEXT,
     category TEXT,
     sizes TEXT[],
     colors TEXT[],
     metadata JSONB,
     created_at TIMESTAMPTZ DEFAULT NOW(),
     updated_at TIMESTAMPTZ DEFAULT NOW()
   );
   ```

## Implementation Plan

### Step 1: Database Setup
1. Apply products table migration to staging
2. Create test products from production collection assets
3. Link assets to products properly

### Step 2: UI Implementation
1. Update `ImageGeneratorDemo.tsx` to:
   - Fetch real products using `useProducts` hook
   - Show product selector UI
   - Auto-populate selected product in slot 2
   - Handle product image selection (best view)

### Step 3: Integration
1. When user selects a product:
   - Find the main product image (front view)
   - Auto-place it in slot 2
   - Update prompt with product description
   - Allow additional garment selection for slots 3 & 4

### Step 4: Testing Flow
1. User navigates to collection
2. Clicks "Generate AI" button
3. Sees product selector
4. Selects a product
5. Product auto-populates in slot 2
6. User can add additional garments
7. Generate images with proper prompt

## Code Locations
- Main component: `/src/pages/demo/ImageGeneratorDemo.tsx`
- Products hook: `/src/components/common/hooks/useProducts.ts`
- Migration: `/supabase/migrations/20250118000000_create_products_table.sql`

## Environment Details
- Staging Project ID: `qnfmiotatmkoumlymynq`
- Production Project ID: `cpelxqvcjnbpnphttzsn`
- Production collection to copy: https://app.fashionlab.tech/organizations/cb251999-232a-416a-94b6-d4e5aab425f9/collections/d8422e7a-8f5f-4445-8e41-c1e9e59799b6

## Next Steps
1. Set up Supabase MCP server with proper authentication
2. Apply products table migration to staging
3. Create script to copy production collection data
4. Update UI to use real products
5. Test the complete flow

## Notes
- The products table may already exist but needs verification
- Need to maintain backward compatibility with existing assets
- Consider using product metadata for storing multiple views/angles
- Ensure RLS policies match collection permissions