import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '../../../contexts/SupabaseContext';
import type { Product } from '../components/common/types/database.types';

export function useProducts(options?: { 
  enabled?: boolean;
  collectionId?: string;
  searchTerm?: string;
  sortBy?: 'name' | 'created_at';
  sortOrder?: 'asc' | 'desc';
}) {
  const { supabase } = useSupabase();
  const { 
    enabled = true, 
    collectionId,
    searchTerm = '', 
    sortBy = 'name', 
    sortOrder = 'asc' 
  } = options || {};

  return useQuery<Product[], Error>({
    queryKey: ['products', collectionId, searchTerm, sortBy, sortOrder],
    enabled,
    queryFn: async () => {
      let query = supabase
        .from('products')
        .select('*');
      
      // Filter by collection if provided
      if (collectionId) {
        query = query.eq('collection_id', collectionId);
      }
      
      // Apply search filter if provided
      if (searchTerm) {
        query = query.ilike('name', `%${searchTerm}%`);
      }
      
      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });
      
      const { data, error } = await query;
      
      if (error) {
        throw new Error(`Error fetching products: ${error.message}`);
      }
      
      return data || [];
    }
  });
}

// Hook to fetch a single product by ID
export function useProduct(id: string | undefined) {
  const { supabase } = useSupabase();
  
  return useQuery<Product, Error>({
    queryKey: ['product', id],
    enabled: !!id,
    queryFn: async () => {
      if (!id) throw new Error('Product ID is required');
      
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) {
        throw new Error(`Error fetching product: ${error.message}`);
      }
      
      if (!data) {
        throw new Error(`Product with ID ${id} not found`);
      }
      
      return data;
    }
  });
} 