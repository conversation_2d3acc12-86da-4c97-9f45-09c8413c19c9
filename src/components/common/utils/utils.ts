import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { Asset } from "../types/database.types";
import { AssetMetadata } from "../types/assetTypes";
import { supabase, STORAGE_BUCKETS } from "./supabase";
import { getOptimizedAssetUrl, generateAssetPath } from "./assetStorage";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format a file size in bytes to a human-readable string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format a date string to a human-readable format
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  
  // Check if date is valid
  if (isNaN(date.getTime())) {
    return 'Invalid date';
  }
  
  // Format: Month Day, Year at Hour:Minute AM/PM
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Checks if the application is running in a development environment
 * @returns {boolean} true if running on localhost or 127.0.0.1
 */
export function isDevelopmentEnvironment(): boolean {
  return (
    typeof window !== 'undefined' && 
    (window.location.hostname === 'localhost' || 
     window.location.hostname === '127.0.0.1')
  );
}

/**
 * Gets the URL for an asset using the optimized bucket structure
 * @param asset The asset object with id and collection_id
 * @param preferThumbnail Whether to prefer the thumbnail (true) or compressed (false)
 * @param forDownload Whether this URL is for download purposes (defaults to original if true)
 * @returns The public URL for the asset
 */
export function getAssetUrl(
  asset: {
    id: string;
    collection_id: string;
    file_path?: string;
    compressed_path?: string;
    original_path?: string;
    thumbnail_path?: string;
  },
  preferThumbnail: boolean = true,
  forDownload: boolean = false
): string {
  if (!asset || !asset.id || !asset.collection_id) {
    console.warn('URL_SERVICE: Invalid asset - missing id or collection_id', asset);
    return '';
  }

  // For downloads, always prefer original files (FAS-73)
  if (forDownload) {
    return getAssetUrlFromPaths(asset, 'original');
  }

  const assetType = preferThumbnail ? 'thumbnail' : 'compressed';
  return getAssetUrlFromPaths(asset, assetType);
}

/**
 * Gets the URL for an asset using stored database paths with fallback to generated paths
 * @param asset The asset object with stored paths
 * @param type The asset type to retrieve
 * @returns The public URL for the asset
 */
export function getAssetUrlFromPaths(
  asset: {
    id: string;
    collection_id: string;
    file_path?: string;
    compressed_path?: string;
    original_path?: string;
    thumbnail_path?: string;
  },
  type: 'thumbnail' | 'compressed' | 'original'
): string {
  if (!asset || !asset.id || !asset.collection_id) {
    console.warn('URL_SERVICE: Invalid asset - missing id or collection_id', asset);
    return '';
  }

  // Try to use stored paths first
  let storedPath: string | undefined;
  let bucketName: string;

  switch (type) {
    case 'original':
      storedPath = asset.original_path;
      bucketName = 'media-originals';
      break;
    case 'compressed':
      storedPath = asset.compressed_path || asset.file_path;
      bucketName = 'media-compressed';
      break;
    case 'thumbnail':
      storedPath = asset.thumbnail_path;
      bucketName = 'media-thumbnails';
      break;
    default:
      storedPath = asset.compressed_path || asset.file_path;
      bucketName = 'media-compressed';
  }

  // AI-generated images are now processed through standard compression pipeline
  // so they use the same buckets as regular assets

  // If we have a stored path, use it directly
  if (storedPath) {
    const { data: urlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(storedPath);

    if (urlData?.publicUrl) {
      return urlData.publicUrl;
    }
  }

  // Enhanced fallback logic - try other stored paths before generating paths
  if (type === 'original') {
    // Check if this is a new asset (has original_path) or legacy asset
    if (asset.original_path) {
      // New asset: use the stored original_path
      const { data: originalUrlData } = supabase.storage
        .from('media-originals')
        .getPublicUrl(asset.original_path);
      if (originalUrlData?.publicUrl) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[FAS-73] New asset: Using stored original_path for download`);
        }
        return originalUrlData.publicUrl;
      }
    } else if (asset.file_path) {
      // Legacy asset: file_path points to compressed version in media-compressed bucket
      const { data: compressedUrlData } = supabase.storage
        .from('media-compressed')
        .getPublicUrl(asset.file_path);
      if (compressedUrlData?.publicUrl) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[FAS-73] Legacy asset: Using best available quality (compressed) for original request`);
        }
        return compressedUrlData.publicUrl;
      }
    }

    // Try other specific paths
    if (asset.compressed_path) {
      const { data: urlData } = supabase.storage
        .from('media-compressed')
        .getPublicUrl(asset.compressed_path);
      if (urlData?.publicUrl) return urlData.publicUrl;
    }
    if (asset.thumbnail_path) {
      const { data: urlData } = supabase.storage
        .from('media-thumbnails')
        .getPublicUrl(asset.thumbnail_path);
      if (urlData?.publicUrl) return urlData.publicUrl;
    }
  } else if (type === 'compressed') {
    // If compressed not found, try original then thumbnail
    if (asset.original_path) {
      const { data: urlData } = supabase.storage
        .from('media-originals')
        .getPublicUrl(asset.original_path);
      if (urlData?.publicUrl) return urlData.publicUrl;
    }
    if (asset.thumbnail_path) {
      const { data: urlData } = supabase.storage
        .from('media-thumbnails')
        .getPublicUrl(asset.thumbnail_path);
      if (urlData?.publicUrl) return urlData.publicUrl;
    }
  } else if (type === 'thumbnail') {
    // If thumbnail not found, try compressed then original
    if (asset.compressed_path || asset.file_path) {
      const compressedPath = asset.compressed_path || asset.file_path;
      const { data: urlData } = supabase.storage
        .from('media-compressed')
        .getPublicUrl(compressedPath);
      if (urlData?.publicUrl) return urlData.publicUrl;
    }
    if (asset.original_path) {
      const { data: urlData } = supabase.storage
        .from('media-originals')
        .getPublicUrl(asset.original_path);
      if (urlData?.publicUrl) return urlData.publicUrl;
    }
  }

  // Final fallback to generated path approach
  return getOptimizedAssetUrl(asset.id, asset.collection_id, type);
}