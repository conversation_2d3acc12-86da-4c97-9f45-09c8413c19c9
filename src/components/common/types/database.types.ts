export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      asset_lineage: {
        Row: {
          asset_id: string
          created_at: string | null
          id: string
          lineage_id: string
          lineage_metadata: Json | null
          parent_asset_id: string | null
          workflow_stage: Database["public"]["Enums"]["workflow_stage"]
        }
        Insert: {
          asset_id: string
          created_at?: string | null
          id?: string
          lineage_id: string
          lineage_metadata?: Json | null
          parent_asset_id?: string | null
          workflow_stage: Database["public"]["Enums"]["workflow_stage"]
        }
        Update: {
          asset_id?: string
          created_at?: string | null
          id?: string
          lineage_id?: string
          lineage_metadata?: Json | null
          parent_asset_id?: string | null
          workflow_stage?: Database["public"]["Enums"]["workflow_stage"]
        }
        Relationships: [
          {
            foreignKeyName: "asset_lineage_asset_id_fkey"
            columns: ["asset_id"]
            isOneToOne: false
            referencedRelation: "assets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "asset_lineage_parent_asset_id_fkey"
            columns: ["parent_asset_id"]
            isOneToOne: false
            referencedRelation: "assets"
            referencedColumns: ["id"]
          },
        ]
      }
      asset_tags: {
        Row: {
          asset_id: string
          created_at: string | null
          tag_id: string
        }
        Insert: {
          asset_id: string
          created_at?: string | null
          tag_id: string
        }
        Update: {
          asset_id?: string
          created_at?: string | null
          tag_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "asset_tags_asset_id_fkey"
            columns: ["asset_id"]
            isOneToOne: false
            referencedRelation: "assets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "asset_tags_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "tags"
            referencedColumns: ["id"]
          },
        ]
      }
      asset_timeline_links: {
        Row: {
          created_at: string | null
          created_by: string | null
          id: string
          link_type: string
          linked_lineage_id: string
          metadata: Json | null
          primary_lineage_id: string
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          link_type: string
          linked_lineage_id: string
          metadata?: Json | null
          primary_lineage_id: string
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          link_type?: string
          linked_lineage_id?: string
          metadata?: Json | null
          primary_lineage_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "asset_timeline_links_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      asset_variant_groups: {
        Row: {
          collection_id: string
          created_at: string | null
          group_metadata: Json | null
          id: string
          product_id: string | null
          size: string | null
          updated_at: string | null
          view_type: string | null
        }
        Insert: {
          collection_id: string
          created_at?: string | null
          group_metadata?: Json | null
          id?: string
          product_id?: string | null
          size?: string | null
          updated_at?: string | null
          view_type?: string | null
        }
        Update: {
          collection_id?: string
          created_at?: string | null
          group_metadata?: Json | null
          id?: string
          product_id?: string | null
          size?: string | null
          updated_at?: string | null
          view_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "asset_variant_groups_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "collections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "asset_variant_groups_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "timeline_overview"
            referencedColumns: ["collection_id"]
          },
          {
            foreignKeyName: "asset_variant_groups_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      assets: {
        Row: {
          collection_id: string
          compressed_path: string | null
          created_at: string | null
          file_name: string
          file_path: string
          file_size: number
          file_type: string
          id: string
          metadata: Json | null
          original_path: string | null
          product_id: string | null
          retouch_substage:
            | Database["public"]["Enums"]["retouch_substage"]
            | null
          thumbnail_path: string | null
          updated_at: string | null
          variant_group_id: string | null
          workflow_stage: Database["public"]["Enums"]["workflow_stage"]
        }
        Insert: {
          collection_id: string
          compressed_path?: string | null
          created_at?: string | null
          file_name: string
          file_path: string
          file_size: number
          file_type: string
          id?: string
          metadata?: Json | null
          original_path?: string | null
          product_id?: string | null
          retouch_substage?:
            | Database["public"]["Enums"]["retouch_substage"]
            | null
          thumbnail_path?: string | null
          updated_at?: string | null
          variant_group_id?: string | null
          workflow_stage?: Database["public"]["Enums"]["workflow_stage"]
        }
        Update: {
          collection_id?: string
          compressed_path?: string | null
          created_at?: string | null
          file_name?: string
          file_path?: string
          file_size?: number
          file_type?: string
          id?: string
          metadata?: Json | null
          original_path?: string | null
          product_id?: string | null
          retouch_substage?:
            | Database["public"]["Enums"]["retouch_substage"]
            | null
          thumbnail_path?: string | null
          updated_at?: string | null
          variant_group_id?: string | null
          workflow_stage?: Database["public"]["Enums"]["workflow_stage"]
        }
        Relationships: [
          {
            foreignKeyName: "assets_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "collections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "assets_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "timeline_overview"
            referencedColumns: ["collection_id"]
          },
          {
            foreignKeyName: "assets_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "assets_variant_group_id_fkey"
            columns: ["variant_group_id"]
            isOneToOne: false
            referencedRelation: "asset_variant_groups"
            referencedColumns: ["id"]
          },
        ]
      }
      bulk_uploads: {
        Row: {
          collection_id: string
          completed_at: string | null
          created_at: string | null
          error_count: number
          error_log: Json | null
          id: string
          processed_files: number
          status: string
          success_count: number
          total_files: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          collection_id: string
          completed_at?: string | null
          created_at?: string | null
          error_count?: number
          error_log?: Json | null
          id?: string
          processed_files?: number
          status?: string
          success_count?: number
          total_files?: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          collection_id?: string
          completed_at?: string | null
          created_at?: string | null
          error_count?: number
          error_log?: Json | null
          id?: string
          processed_files?: number
          status?: string
          success_count?: number
          total_files?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "bulk_uploads_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "collections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bulk_uploads_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "timeline_overview"
            referencedColumns: ["collection_id"]
          },
          {
            foreignKeyName: "bulk_uploads_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      collections: {
        Row: {
          cover_image_url: string | null
          created_at: string | null
          description: string | null
          id: string
          metadata: Json | null
          name: string
          organization_id: string
          status: string
          updated_at: string | null
        }
        Insert: {
          cover_image_url?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          metadata?: Json | null
          name: string
          organization_id: string
          status?: string
          updated_at?: string | null
        }
        Update: {
          cover_image_url?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          metadata?: Json | null
          name?: string
          organization_id?: string
          status?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "collections_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      comment_mentions: {
        Row: {
          comment_id: string | null
          created_at: string | null
          id: string
          mentioned_user_id: string | null
        }
        Insert: {
          comment_id?: string | null
          created_at?: string | null
          id?: string
          mentioned_user_id?: string | null
        }
        Update: {
          comment_id?: string | null
          created_at?: string | null
          id?: string
          mentioned_user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "comment_mentions_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comment_mentions_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "comments_with_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comment_mentions_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "timeline_comments"
            referencedColumns: ["id"]
          },
        ]
      }
      comments: {
        Row: {
          asset_id: string | null
          author: string | null
          content: string
          coordinates: Json | null
          created_at: string | null
          id: string
          is_annotation: boolean
          parent_id: string | null
          status: Database["public"]["Enums"]["comment_status"]
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          asset_id?: string | null
          author?: string | null
          content: string
          coordinates?: Json | null
          created_at?: string | null
          id?: string
          is_annotation?: boolean
          parent_id?: string | null
          status?: Database["public"]["Enums"]["comment_status"]
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          asset_id?: string | null
          author?: string | null
          content?: string
          coordinates?: Json | null
          created_at?: string | null
          id?: string
          is_annotation?: boolean
          parent_id?: string | null
          status?: Database["public"]["Enums"]["comment_status"]
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "comments_asset_id_fkey"
            columns: ["asset_id"]
            isOneToOne: false
            referencedRelation: "assets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments_with_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "timeline_comments"
            referencedColumns: ["id"]
          },
        ]
      }
      invitation_organizations: {
        Row: {
          created_at: string
          id: string
          invitation_id: string
          organization_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          invitation_id: string
          organization_id: string
        }
        Update: {
          created_at?: string
          id?: string
          invitation_id?: string
          organization_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "invitation_organizations_invitation_id_fkey"
            columns: ["invitation_id"]
            isOneToOne: false
            referencedRelation: "pending_invitations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitation_organizations_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      model_images: {
        Row: {
          angle_prompt_text: string | null
          angle_type: string
          client_view_path: string | null
          created_at: string | null
          file_size: number | null
          id: string
          is_white_background: boolean | null
          mime_type: string | null
          model_id: string
          storage_path: string | null
          updated_at: string | null
        }
        Insert: {
          angle_prompt_text?: string | null
          angle_type: string
          client_view_path?: string | null
          created_at?: string | null
          file_size?: number | null
          id?: string
          is_white_background?: boolean | null
          mime_type?: string | null
          model_id: string
          storage_path?: string | null
          updated_at?: string | null
        }
        Update: {
          angle_prompt_text?: string | null
          angle_type?: string
          client_view_path?: string | null
          created_at?: string | null
          file_size?: number | null
          id?: string
          is_white_background?: boolean | null
          mime_type?: string | null
          model_id?: string
          storage_path?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "model_images_model_id_fkey"
            columns: ["model_id"]
            isOneToOne: false
            referencedRelation: "model_library"
            referencedColumns: ["id"]
          },
        ]
      }
      model_library: {
        Row: {
          code: string
          created_at: string | null
          created_by: string | null
          description: string | null
          display_order: number | null
          id: string
          is_active: boolean | null
          metadata: Json | null
          model_backstory: string | null
          model_persona_name: string | null
          name: string
          prompt_text: string | null
          updated_at: string | null
        }
        Insert: {
          code: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          display_order?: number | null
          id?: string
          is_active?: boolean | null
          metadata?: Json | null
          model_backstory?: string | null
          model_persona_name?: string | null
          name: string
          prompt_text?: string | null
          updated_at?: string | null
        }
        Update: {
          code?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          display_order?: number | null
          id?: string
          is_active?: boolean | null
          metadata?: Json | null
          model_backstory?: string | null
          model_persona_name?: string | null
          name?: string
          prompt_text?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "model_library_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      organization_memberships: {
        Row: {
          created_at: string
          id: string
          organization_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          organization_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          organization_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "organization_memberships_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      organizations: {
        Row: {
          company_domain: string | null
          created_at: string
          description: string | null
          id: string
          is_private: boolean | null
          logo_url: string | null
          name: string
          primary_color: string | null
          updated_at: string
        }
        Insert: {
          company_domain?: string | null
          created_at?: string
          description?: string | null
          id?: string
          is_private?: boolean | null
          logo_url?: string | null
          name: string
          primary_color?: string | null
          updated_at?: string
        }
        Update: {
          company_domain?: string | null
          created_at?: string
          description?: string | null
          id?: string
          is_private?: boolean | null
          logo_url?: string | null
          name?: string
          primary_color?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      pending_invitations: {
        Row: {
          accepted: boolean | null
          accepted_at: string | null
          created_at: string
          email: string
          expires_at: string
          id: string
          invited_at: string
          invited_by: string
          message: string | null
          organization_id: string
          role: Database["public"]["Enums"]["user_role"] | null
          token: string
          updated_at: string
        }
        Insert: {
          accepted?: boolean | null
          accepted_at?: string | null
          created_at?: string
          email: string
          expires_at?: string
          id?: string
          invited_at?: string
          invited_by: string
          message?: string | null
          organization_id: string
          role?: Database["public"]["Enums"]["user_role"] | null
          token: string
          updated_at?: string
        }
        Update: {
          accepted?: boolean | null
          accepted_at?: string | null
          created_at?: string
          email?: string
          expires_at?: string
          id?: string
          invited_at?: string
          invited_by?: string
          message?: string | null
          organization_id?: string
          role?: Database["public"]["Enums"]["user_role"] | null
          token?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "pending_invitations_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          collection_id: string | null
          created_at: string | null
          description: string | null
          id: string
          name: string
          sizes: Json | null
          sku: string | null
          updated_at: string | null
        }
        Insert: {
          collection_id?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          sizes?: Json | null
          sku?: string | null
          updated_at?: string | null
        }
        Update: {
          collection_id?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          sizes?: Json | null
          sku?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "products_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "collections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "timeline_overview"
            referencedColumns: ["collection_id"]
          },
        ]
      }
      security_activity: {
        Row: {
          created_at: string
          event_type: string
          id: string
          ip_address: string | null
          location: string | null
          user_agent: string | null
          user_id: string
        }
        Insert: {
          created_at?: string
          event_type: string
          id?: string
          ip_address?: string | null
          location?: string | null
          user_agent?: string | null
          user_id: string
        }
        Update: {
          created_at?: string
          event_type?: string
          id?: string
          ip_address?: string | null
          location?: string | null
          user_agent?: string | null
          user_id?: string
        }
        Relationships: []
      }
      tags: {
        Row: {
          category: Database["public"]["Enums"]["tag_category"]
          collection_id: string | null
          color: string
          created_at: string | null
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          category: Database["public"]["Enums"]["tag_category"]
          collection_id?: string | null
          color?: string
          created_at?: string | null
          id?: string
          name: string
          updated_at?: string | null
        }
        Update: {
          category?: Database["public"]["Enums"]["tag_category"]
          collection_id?: string | null
          color?: string
          created_at?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tags_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "collections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tags_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "timeline_overview"
            referencedColumns: ["collection_id"]
          },
        ]
      }
      user_collection_access: {
        Row: {
          collection_id: string | null
          created_at: string | null
          id: string
          permission_level: Database["public"]["Enums"]["permission_level"]
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          collection_id?: string | null
          created_at?: string | null
          id?: string
          permission_level?: Database["public"]["Enums"]["permission_level"]
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          collection_id?: string | null
          created_at?: string | null
          id?: string
          permission_level?: Database["public"]["Enums"]["permission_level"]
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_collection_access_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "collections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_collection_access_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "timeline_overview"
            referencedColumns: ["collection_id"]
          },
        ]
      }
      users: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          display_name: string | null
          email: string
          first_name: string | null
          id: string
          is_freelancer: boolean | null
          last_name: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          display_name?: string | null
          email: string
          first_name?: string | null
          id: string
          is_freelancer?: boolean | null
          last_name?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          display_name?: string | null
          email?: string
          first_name?: string | null
          id?: string
          is_freelancer?: boolean | null
          last_name?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      comments_with_users: {
        Row: {
          asset_id: string | null
          author: string | null
          content: string | null
          coordinates: Json | null
          created_at: string | null
          id: string | null
          is_annotation: boolean | null
          parent_id: string | null
          status: Database["public"]["Enums"]["comment_status"] | null
          updated_at: string | null
          user_avatar_url: string | null
          user_email: string | null
          user_id: string | null
          user_name: string | null
        }
        Relationships: [
          {
            foreignKeyName: "comments_asset_id_fkey"
            columns: ["asset_id"]
            isOneToOne: false
            referencedRelation: "assets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments_with_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "timeline_comments"
            referencedColumns: ["id"]
          },
        ]
      }
      timeline_comments: {
        Row: {
          asset_id: string | null
          author: string | null
          author_avatar: string | null
          author_name: string | null
          content: string | null
          coordinates: Json | null
          created_at: string | null
          id: string | null
          is_annotation: boolean | null
          lineage_id: string | null
          parent_id: string | null
          product_id: string | null
          size: string | null
          status: Database["public"]["Enums"]["comment_status"] | null
          updated_at: string | null
          user_id: string | null
          variant_group_id: string | null
          view_type: string | null
          workflow_stage: Database["public"]["Enums"]["workflow_stage"] | null
        }
        Relationships: [
          {
            foreignKeyName: "asset_variant_groups_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "assets_variant_group_id_fkey"
            columns: ["variant_group_id"]
            isOneToOne: false
            referencedRelation: "asset_variant_groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_asset_id_fkey"
            columns: ["asset_id"]
            isOneToOne: false
            referencedRelation: "assets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments_with_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "timeline_comments"
            referencedColumns: ["id"]
          },
        ]
      }
      timeline_overview: {
        Row: {
          collection_id: string | null
          collection_name: string | null
          lineage_id: string | null
          open_comments: number | null
          product_id: string | null
          product_name: string | null
          size: string | null
          timeline_started: string | null
          timeline_updated: string | null
          total_assets: number | null
          total_comments: number | null
          total_stages: number | null
          view_type: string | null
        }
        Relationships: [
          {
            foreignKeyName: "asset_variant_groups_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      debug_auth_config: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      debug_update_asset_workflow_stage: {
        Args: { p_asset_id: string; p_new_stage: string }
        Returns: {
          success: boolean
          updated_id: string
          old_stage: string
          new_stage: string
          error_message: string
        }[]
      }
      get_asset_timeline: {
        Args: {
          p_collection_id: string
          p_product_id?: string
          p_size?: string
          p_view_type?: string
        }
        Returns: {
          lineage_id: string
          asset_id: string
          parent_asset_id: string
          workflow_stage: Database["public"]["Enums"]["workflow_stage"]
          file_path: string
          thumbnail_path: string
          metadata: Json
          created_at: string
          created_by_id: string
          created_by_name: string
          comment_count: number
          variant_group_id: string
          product_name: string
        }[]
      }
      get_freelancer_organizations: {
        Args: { freelancer_id: string }
        Returns: {
          organization_id: string
          organization_name: string
          joined_at: string
        }[]
      }
      is_user_freelancer: {
        Args: { user_id: string }
        Returns: boolean
      }
      log_security_event: {
        Args: {
          p_user_id: string
          p_event_type: string
          p_ip_address?: string
          p_user_agent?: string
          p_location?: string
        }
        Returns: string
      }
      send_email_local: {
        Args: {
          recipient_email: string
          subject: string
          html_content: string
          text_content: string
        }
        Returns: boolean
      }
    }
    Enums: {
      asset_type:
        | "product-front"
        | "product-back"
        | "product-detail"
        | "product-lifestyle"
        | "marketing"
        | "social-media"
        | "catalog"
        | "website-banner"
        | "email-campaign"
        | "lookbook"
        | "advertisement"
        | "packaging"
        | "other"
      comment_status: "open" | "resolved" | "archived"
      permission_level: "view" | "edit" | "admin"
      retouch_substage:
        | "internal_review"
        | "revision_1"
        | "revision_2"
        | "extra_revisions"
      tag_category: "global" | "angles" | "styling" | "collection"
      user_role:
        | "platform_super"
        | "platform_admin"
        | "brand_admin"
        | "brand_member"
        | "external_retoucher"
        | "external_prompter"
      workflow_stage:
        | "upload"
        | "raw_ai_images"
        | "selected"
        | "refined"
        | "upscale"
        | "retouch"
        | "final"
      workflow_stage_enum:
        | "input_assets"
        | "raw"
        | "upscaled"
        | "retouched"
        | "final"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      asset_type: [
        "product-front",
        "product-back",
        "product-detail",
        "product-lifestyle",
        "marketing",
        "social-media",
        "catalog",
        "website-banner",
        "email-campaign",
        "lookbook",
        "advertisement",
        "packaging",
        "other",
      ],
      comment_status: ["open", "resolved", "archived"],
      permission_level: ["view", "edit", "admin"],
      retouch_substage: [
        "internal_review",
        "revision_1",
        "revision_2",
        "extra_revisions",
      ],
      tag_category: ["global", "angles", "styling", "collection"],
      user_role: [
        "platform_super",
        "platform_admin",
        "brand_admin",
        "brand_member",
        "external_retoucher",
        "external_prompter",
      ],
      workflow_stage: [
        "upload",
        "raw_ai_images",
        "selected",
        "refined",
        "upscale",
        "retouch",
        "final",
      ],
      workflow_stage_enum: [
        "input_assets",
        "raw",
        "upscaled",
        "retouched",
        "final",
      ],
    },
  },
} as const

