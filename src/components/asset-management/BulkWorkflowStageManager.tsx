import { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  Di<PERSON>Header, 
  <PERSON><PERSON>Title, 
  DialogFooter 
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../common/hooks/use-toast';
import { WorkflowStage } from '../common/types/database.types';
import { useQueryClient } from '@tanstack/react-query';
import { bulkUpdateAssetsWithVerification, handleSupabaseError } from '../common/utils/assetOperations';
import { debugAssetUpdate } from '../common/utils/assetDebug';
import { useUserRole } from '../../contexts/UserRoleContext';
import { getWorkflowStageConfigs } from '../common/utils/workflowStageUtils';
import { useIsFreelancer } from '../common/hooks/useIsFreelancer';
import { RetouchSubstage } from '../common/types/database.types';
import { getAllowedRetouchSubstages, getRetouchSubstageLabel, getRetouchSubstageConfigs } from '../common/utils/retouchSubstageUtils';
import { Eye, Edit, Edit2, MoreHorizontal } from 'lucide-react';

interface BulkWorkflowStageManagerProps {
  assetIds: string[];
  isOpen: boolean;
  onClose: () => void;
  onStagesUpdated?: () => void;
  collectionId?: string;
}

export function BulkWorkflowStageManager({ 
  assetIds, 
  isOpen, 
  onClose, 
  onStagesUpdated,
  collectionId
}: BulkWorkflowStageManagerProps) {
  const [selectedStage, setSelectedStage] = useState<WorkflowStage>('raw_ai_images');
  const [selectedRetouchSubstage, setSelectedRetouchSubstage] = useState<RetouchSubstage>('revision_1');
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  const { supabase } = useSupabase();
  const queryClient = useQueryClient();

  // Get user role for filtering
  const { userRole } = useUserRole();
  const isFreelancer = useIsFreelancer();

  // Get role-based workflow stage configurations, considering freelancer status
  const workflowStageConfigs = getWorkflowStageConfigs(userRole, isFreelancer);
  
  // Get allowed retouch substages based on user role
  const allowedRetouchSubstages = getAllowedRetouchSubstages(userRole, isFreelancer);

  // Apply workflow stage change to all selected assets
  const applyStageChange = async () => {
    if (assetIds.length === 0) return;
    
    setIsSaving(true);
    try {
      console.log('[BulkWorkflowStageManager] Starting update...', {
        assetIds,
        newStage: selectedStage,
        collectionId
      });

      // Prepare updates based on selected stage
      const updates: any = { workflow_stage: selectedStage };
      
      // If updating to retouch stage, include the substage
      if (selectedStage === 'retouch') {
        updates.retouch_substage = selectedRetouchSubstage;
      } else {
        // Clear substage when moving to a different stage
        updates.retouch_substage = null;
      }

      // Use the new standardized update function with verification
      const result = await bulkUpdateAssetsWithVerification(supabase, {
        assetIds,
        updates,
        verifyUpdate: true,
        replicationDelay: 1500,
        maxRetries: 3
      });

      if (!result.success) {
        const errorInfo = handleSupabaseError({ message: result.error });
        throw new Error(errorInfo.message);
      }

      toast({
        title: 'Workflow Stages Updated',
        description: `Updated ${assetIds.length} assets to ${selectedStage} stage`,
      });
      
      console.log('[BulkWorkflowStageManager] Update verified successfully');
      
      // Invalidate queries to ensure UI updates
      await queryClient.invalidateQueries({ 
        predicate: (query) => {
          return Array.isArray(query.queryKey) && query.queryKey[0] === 'assets';
        }
      });
      
      // Also invalidate related queries
      await queryClient.invalidateQueries({ queryKey: ['assetCounts'] });
      await queryClient.invalidateQueries({ queryKey: ['assetTags'] });
      
      // Notify parent component
      if (onStagesUpdated) onStagesUpdated();
      
      // Close dialog
      onClose();
    } catch (error: any) {
      console.error('[BulkWorkflowStageManager] Error updating workflow stages:', error);
      
      const errorInfo = handleSupabaseError(error);
      toast({
        title: 'Update Failed',
        description: errorInfo.message,
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Update Workflow Stage for {assetIds.length} Assets</DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          <Label className="mb-3 block">Select New Workflow Stage</Label>
          <RadioGroup
            value={selectedStage}
            onValueChange={(value) => setSelectedStage(value as WorkflowStage)}
            className="space-y-3"
          >
            {workflowStageConfigs.map(config => {
              const isRetouch = config.id === 'retouch';
              
              return (
                <div key={config.id}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value={config.id} id={config.id} />
                    <Label htmlFor={config.id}>{config.label}</Label>
                  </div>
                  
                  {/* Show substage options when retouch is selected */}
                  {isRetouch && selectedStage === 'retouch' && (
                    <div className="ml-6 mt-3 space-y-2">
                      <Label className="text-sm text-muted-foreground">Select Retouch Stage:</Label>
                      <RadioGroup
                        value={selectedRetouchSubstage}
                        onValueChange={(value) => setSelectedRetouchSubstage(value as RetouchSubstage)}
                        className="space-y-2"
                      >
                        {allowedRetouchSubstages.map(substage => {
                          const substageConfig = getRetouchSubstageConfigs().find(c => c.id === substage);
                          return (
                            <div key={substage} className="flex items-center space-x-2">
                              <RadioGroupItem value={substage} id={`substage-${substage}`} className="h-3 w-3" />
                              <Label htmlFor={`substage-${substage}`} className="text-sm font-normal flex items-center gap-2">
                                {substageConfig?.icon === 'Eye' && <Eye size={12} className="text-gray-500" />}
                                {substageConfig?.icon === 'Edit' && <Edit size={12} className="text-blue-500" />}
                                {substageConfig?.icon === 'Edit2' && <Edit2 size={12} className="text-purple-500" />}
                                {substageConfig?.icon === 'MoreHorizontal' && <MoreHorizontal size={12} className="text-amber-500" />}
                                {getRetouchSubstageLabel(substage)}
                              </Label>
                            </div>
                          );
                        })}
                      </RadioGroup>
                    </div>
                  )}
                </div>
              );
            })}
          </RadioGroup>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSaving}>
            Cancel
          </Button>
          <Button 
            onClick={applyStageChange} 
            disabled={isSaving}
          >
            {isSaving ? 'Updating...' : 'Update Workflow Stage'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default BulkWorkflowStageManager;
