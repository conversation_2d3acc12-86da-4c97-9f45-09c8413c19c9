import React, { useCallback } from 'react';
import { Upload, Plus } from 'lucide-react';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { cn } from '../common/utils/utils';
import { CAMPAIGN_MODELS, ANGLE_BLOCKS, SETTING_BLOCKS } from '../../config/imageGeneration.config';

interface UploadedGarment {
  id: string;
  name: string;
  promptText: string;
  image: string;
  uploading?: boolean;
}

interface ImageSelectionPanelProps {
  selectedModels: string[];
  selectedAngles: number[];
  selectedGarments: string[];
  selectedSettings: Record<string, number>;
  uploadedGarments: UploadedGarment[];
  onModelsChange: (models: string[]) => void;
  onAnglesChange: (angles: number[]) => void;
  onGarmentsChange: (garments: string[]) => void;
  onSettingsChange: (settings: Record<string, number>) => void;
  onGarmentsUpload: (garments: UploadedGarment[]) => void;
}

export function ImageSelectionPanel({
  selectedModels,
  selectedAngles,
  selectedGarments,
  selectedSettings,
  uploadedGarments,
  onModelsChange,
  onAnglesChange,
  onGarmentsChange,
  onSettingsChange,
  onGarmentsUpload,
}: ImageSelectionPanelProps) {
  const toggleModel = useCallback((modelId: string) => {
    if (selectedModels.includes(modelId)) {
      onModelsChange(selectedModels.filter(id => id !== modelId));
    } else {
      onModelsChange([...selectedModels, modelId]);
    }
  }, [selectedModels, onModelsChange]);

  const toggleAngle = useCallback((angleId: number) => {
    if (selectedAngles.includes(angleId)) {
      onAnglesChange(selectedAngles.filter(id => id !== angleId));
    } else {
      onAnglesChange([...selectedAngles, angleId]);
    }
  }, [selectedAngles, onAnglesChange]);

  const toggleGarment = useCallback((garmentId: string) => {
    if (selectedGarments.includes(garmentId)) {
      onGarmentsChange(selectedGarments.filter(id => id !== garmentId));
    } else {
      onGarmentsChange([...selectedGarments, garmentId]);
    }
  }, [selectedGarments, onGarmentsChange]);

  const toggleSetting = useCallback((categoryId: string, optionId: number) => {
    onSettingsChange({ ...selectedSettings, [categoryId]: optionId });
  }, [selectedSettings, onSettingsChange]);

  const handleGarmentUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const tempId = `temp-${Date.now()}`;
    const imageUrl = URL.createObjectURL(file);
    
    const tempGarment: UploadedGarment = {
      id: tempId,
      name: file.name.split('.')[0],
      promptText: 'Analyzing garment...',
      image: imageUrl,
      uploading: true
    };
    
    onGarmentsUpload([...uploadedGarments, tempGarment]);
    
    // Simulate AI description generation
    setTimeout(() => {
      const descriptions = [
        'sleek black leather jacket with silver zippers',
        'flowing silk scarf with abstract pattern',
        'vintage denim vest with brass buttons',
        'chunky knit sweater in cream color',
        'elegant silk blouse with pearl buttons'
      ];
      const aiDescription = descriptions[Math.floor(Math.random() * descriptions.length)];
      
      onGarmentsUpload(
        uploadedGarments.map(g => 
          g.id === tempId 
            ? { ...g, promptText: aiDescription, uploading: false }
            : g
        )
      );
    }, 2000);
  }, [uploadedGarments, onGarmentsUpload]);

  return (
    <div className="w-80 bg-white border-r flex flex-col">
      <div className="p-4 border-b">
        <h3 className="font-medium text-sm">Selection</h3>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-6">
          {/* Garments */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Garments</Label>
            <div className="grid grid-cols-3 gap-2">
              {uploadedGarments.map((garment) => (
                <div
                  key={garment.id}
                  className={cn(
                    "border rounded-lg p-2 cursor-pointer transition-all text-center relative overflow-hidden",
                    selectedGarments.includes(garment.id)
                      ? "border-purple-600 bg-purple-50"
                      : "border-gray-200 hover:border-gray-300"
                  )}
                  onClick={() => !garment.uploading && toggleGarment(garment.id)}
                >
                  {garment.uploading && (
                    <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-10">
                      <div className="flex flex-col items-center">
                        <div className="w-5 h-5 border-2 border-purple-600 border-t-transparent rounded-full animate-spin mb-1" />
                        <p className="text-xs text-purple-600">AI analyzing...</p>
                      </div>
                    </div>
                  )}
                  <div className="w-full h-16 rounded mb-1 overflow-hidden bg-gray-100">
                    <img 
                      src={garment.image} 
                      alt={garment.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <p className="text-xs font-medium line-clamp-1">{garment.name}</p>
                </div>
              ))}
              
              <label className="border border-dashed border-gray-300 rounded-lg p-2 cursor-pointer hover:border-gray-400 transition-colors">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleGarmentUpload}
                  className="hidden"
                />
                <div className="w-full h-16 flex items-center justify-center">
                  <Upload className="w-5 h-5 text-gray-400" />
                </div>
                <p className="text-xs text-gray-400 text-center">Upload</p>
              </label>
            </div>
          </div>

          {/* Models */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Models</Label>
            <div className="grid grid-cols-2 gap-2">
              {CAMPAIGN_MODELS.map((model) => (
                <div
                  key={model.id}
                  className={cn(
                    "border rounded-lg cursor-pointer transition-all overflow-hidden",
                    selectedModels.includes(model.id)
                      ? "border-purple-600 bg-purple-50"
                      : "border-gray-200 hover:border-gray-300"
                  )}
                  onClick={() => toggleModel(model.id)}
                >
                  <img src={model.image} className="w-full h-24 object-cover" alt={model.name} />
                  <div className="p-2">
                    <p className="text-xs font-medium text-center">{model.shortName}</p>
                  </div>
                </div>
              ))}
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full mt-2 text-xs"
              onClick={() => onModelsChange(CAMPAIGN_MODELS.map(m => m.id))}
            >
              Select All Models
            </Button>
          </div>

          {/* Angles */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Angles</Label>
            <div className="grid grid-cols-2 gap-1">
              {ANGLE_BLOCKS.map((angle) => (
                <Button
                  key={angle.id}
                  variant={selectedAngles.includes(angle.id) ? "default" : "secondary"}
                  size="sm"
                  className={cn(
                    "text-xs h-10",
                    selectedAngles.includes(angle.id) 
                      ? "bg-gray-900 hover:bg-gray-800 text-white" 
                      : "bg-gray-100 hover:bg-gray-200 text-gray-900"
                  )}
                  onClick={() => toggleAngle(angle.id)}
                >
                  {angle.name}
                </Button>
              ))}
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full mt-2 text-xs"
              onClick={() => onAnglesChange(ANGLE_BLOCKS.map(a => a.id))}
            >
              Select All Angles
            </Button>
          </div>

          {/* Settings */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Settings</Label>
            <div className="space-y-3">
              {SETTING_BLOCKS.map((category) => (
                <div key={category.id}>
                  <Label className="text-xs text-muted-foreground mb-1 flex items-center gap-1">
                    <span>{category.icon}</span>
                    {category.name}
                  </Label>
                  <div className="grid grid-cols-2 gap-1">
                    {category.options.map((option) => (
                      <Button
                        key={option.id}
                        variant={selectedSettings[category.id] === option.id ? "secondary" : "outline"}
                        size="sm"
                        className={cn(
                          "text-xs h-8",
                          selectedSettings[category.id] === option.id && "bg-gray-200"
                        )}
                        onClick={() => toggleSetting(category.id, option.id)}
                      >
                        {option.name}
                      </Button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}