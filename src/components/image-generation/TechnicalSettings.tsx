import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { Label } from '../ui/label';
import { Input } from '../ui/input';
import { Slider } from '../ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Button } from '../ui/button';
import { cn } from '../common/utils/utils';
import { 
  ASPECT_RATIOS, 
  IMAGE_FORMATS, 
  NUM_IMAGES_OPTIONS,
  DEFAULT_GENERATION_SETTINGS 
} from '../../config/imageGeneration.config';

interface TechnicalSettingsProps {
  settings: typeof DEFAULT_GENERATION_SETTINGS;
  onSettingsChange: (settings: Partial<typeof DEFAULT_GENERATION_SETTINGS>) => void;
  totalImages: number;
  className?: string;
}

export function TechnicalSettings({
  settings,
  onSettingsChange,
  totalImages,
  className
}: TechnicalSettingsProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleSettingChange = <K extends keyof typeof DEFAULT_GENERATION_SETTINGS>(
    key: K,
    value: typeof DEFAULT_GENERATION_SETTINGS[K]
  ) => {
    onSettingsChange({ [key]: value });
  };

  return (
    <div className={cn("space-y-3", className)}>
      <Label className="text-sm font-medium mb-2 block">Technical Settings</Label>
      
      {/* Number of Images */}
      <div>
        <Label className="text-xs text-muted-foreground">Number of Images</Label>
        <Select 
          value={settings.numImages.toString()} 
          onValueChange={(v) => handleSettingChange('numImages', parseInt(v) as typeof settings.numImages)}
        >
          <SelectTrigger className="h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {NUM_IMAGES_OPTIONS.map(num => (
              <SelectItem key={num} value={num.toString()}>
                {num} {num === 1 ? 'image' : 'images'} per combination
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      {/* Aspect Ratio */}
      <div>
        <Label className="text-xs text-muted-foreground">Aspect Ratio</Label>
        <Select 
          value={settings.ratio} 
          onValueChange={(v) => handleSettingChange('ratio', v as typeof settings.ratio)}
        >
          <SelectTrigger className="h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {ASPECT_RATIOS.map(ratio => (
              <SelectItem key={ratio.value} value={ratio.value}>
                {ratio.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      {/* Image Format */}
      <div>
        <Label className="text-xs text-muted-foreground">Image Format</Label>
        <Select 
          value={settings.imageFormat} 
          onValueChange={(v) => handleSettingChange('imageFormat', v as typeof settings.imageFormat)}
        >
          <SelectTrigger className="h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {IMAGE_FORMATS.map(format => (
              <SelectItem key={format.value} value={format.value}>
                {format.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      {/* Advanced Settings Toggle */}
      <Button
        variant="ghost"
        size="sm"
        className="w-full text-xs"
        onClick={() => setShowAdvanced(!showAdvanced)}
      >
        <ChevronDown className={cn("w-3 h-3 mr-1 transition-transform", showAdvanced && "rotate-180")} />
        Advanced Settings
      </Button>
      
      {showAdvanced && (
        <div className="space-y-3 pt-2">
          {/* Seed */}
          <div>
            <Label className="text-xs text-muted-foreground">Seed (leave empty for random)</Label>
            <Input
              type="number"
              placeholder="Random"
              className="h-8 text-sm"
              value={settings.seed || ''}
              onChange={(e) => {
                const seed = e.target.value ? parseInt(e.target.value) : 0;
                handleSettingChange('seed', seed);
              }}
            />
          </div>
          
          {/* CFG Scale */}
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label className="text-xs text-muted-foreground">CFG Scale</Label>
              <span className="text-xs font-medium">{settings.cfg}</span>
            </div>
            <Slider
              value={[settings.cfg]}
              onValueChange={([value]) => handleSettingChange('cfg', value)}
              min={1}
              max={20}
              step={0.5}
              className="h-1"
            />
          </div>
          
          {/* Flux Guidance */}
          <div>
            <div className="flex items-center justify-between mb-1">
              <Label className="text-xs text-muted-foreground">Flux Guidance</Label>
              <span className="text-xs font-medium">{settings.fluxGuidance}</span>
            </div>
            <Slider
              value={[settings.fluxGuidance]}
              onValueChange={([value]) => handleSettingChange('fluxGuidance', value)}
              min={0}
              max={1}
              step={0.1}
              className="h-1"
            />
          </div>
        </div>
      )}
      
      <div className="pt-2 border-t flex items-center justify-between text-xs text-muted-foreground">
        <span>Total to generate:</span>
        <span className="font-medium">{totalImages} images</span>
      </div>
    </div>
  );
}