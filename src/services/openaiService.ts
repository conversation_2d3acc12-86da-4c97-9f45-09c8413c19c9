interface GarmentAnalysis {
  description: string;
  colors: string[];
  style: string;
  category: string;
}

class OpenAIService {
  private apiKey: string | null = null;

  constructor() {
    // API key will be fetched from environment or Supabase secrets
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY || null;
    console.log('OpenAI API key configured:', this.apiKey ? 'Yes' : 'No');
    if (!this.apiKey) {
      console.log('VITE_OPENAI_API_KEY not found in environment variables');
    }
  }

  async analyzeGarmentImage(imageBase64: string): Promise<GarmentAnalysis> {
    if (!this.apiKey) {
      console.warn('OpenAI API key not configured, using mock data');
      return this.getMockAnalysis();
    }

    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini', // Using gpt-4o-mini which supports vision
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: 'Analyze this garment image and provide a concise description (max 15 words) for use in fashion AI image generation. Focus on: color, material/texture, style/cut, and distinctive features. Format your response as: "[color] [material] [garment type] with [distinctive features]". Example: "black leather biker jacket with silver zippers and cropped fit"'
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: imageBase64,
                    detail: 'low' // Using low detail for faster response
                  }
                }
              ]
            }
          ],
          max_tokens: 100,
          temperature: 0.3
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('OpenAI API error:', errorData);
        throw new Error(`OpenAI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      console.log('OpenAI API response:', data);
      
      const description = data.choices[0]?.message?.content?.trim() || '';
      console.log('Extracted description:', description);

      // Parse the description to extract structured data
      const analysis: GarmentAnalysis = {
        description,
        colors: this.extractColors(description),
        style: this.extractStyle(description),
        category: this.extractCategory(description)
      };

      return analysis;
    } catch (error) {
      console.error('Error analyzing garment with OpenAI:', error);
      // Fallback to mock data
      return this.getMockAnalysis();
    }
  }

  private extractColors(description: string): string[] {
    const colorKeywords = [
      'black', 'white', 'red', 'blue', 'green', 'yellow', 'purple', 'pink', 
      'orange', 'brown', 'gray', 'grey', 'beige', 'cream', 'navy', 'teal',
      'burgundy', 'olive', 'coral', 'gold', 'silver', 'denim', 'khaki'
    ];
    
    const foundColors = colorKeywords.filter(color => 
      description.toLowerCase().includes(color)
    );
    
    return foundColors.length > 0 ? foundColors : ['neutral'];
  }

  private extractStyle(description: string): string {
    const styleKeywords = {
      casual: ['casual', 'relaxed', 'comfortable', 'everyday'],
      formal: ['formal', 'elegant', 'sophisticated', 'business'],
      sporty: ['sporty', 'athletic', 'active', 'performance'],
      vintage: ['vintage', 'retro', 'classic', 'antique'],
      modern: ['modern', 'contemporary', 'sleek', 'minimalist'],
      bohemian: ['bohemian', 'boho', 'flowing', 'artistic']
    };

    for (const [style, keywords] of Object.entries(styleKeywords)) {
      if (keywords.some(keyword => description.toLowerCase().includes(keyword))) {
        return style;
      }
    }
    
    return 'contemporary';
  }

  private extractCategory(description: string): string {
    const categories = {
      'jacket': ['jacket', 'blazer', 'coat', 'bomber', 'parka'],
      'shirt': ['shirt', 'blouse', 'top', 'tee', 't-shirt'],
      'pants': ['pants', 'trousers', 'jeans', 'leggings', 'chinos'],
      'dress': ['dress', 'gown', 'frock'],
      'skirt': ['skirt', 'midi', 'mini', 'maxi'],
      'sweater': ['sweater', 'jumper', 'pullover', 'cardigan', 'knit'],
      'accessory': ['scarf', 'belt', 'hat', 'bag', 'shoes']
    };

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => description.toLowerCase().includes(keyword))) {
        return category;
      }
    }
    
    return 'garment';
  }

  private getMockAnalysis(): GarmentAnalysis {
    const mockDescriptions = [
      {
        description: 'black leather biker jacket with silver zippers and fitted silhouette',
        colors: ['black'],
        style: 'modern',
        category: 'jacket'
      },
      {
        description: 'flowing silk scarf with vibrant abstract pattern in multicolor',
        colors: ['multicolor'],
        style: 'bohemian',
        category: 'accessory'
      },
      {
        description: 'vintage denim vest with brass buttons and distressed finish',
        colors: ['blue', 'denim'],
        style: 'vintage',
        category: 'jacket'
      },
      {
        description: 'cream cable knit sweater with oversized fit and chunky texture',
        colors: ['cream'],
        style: 'casual',
        category: 'sweater'
      },
      {
        description: 'elegant champagne silk blouse with pearl buttons and flowing sleeves',
        colors: ['champagne', 'beige'],
        style: 'formal',
        category: 'shirt'
      }
    ];

    return mockDescriptions[Math.floor(Math.random() * mockDescriptions.length)];
  }
}

export const openAIService = new OpenAIService();