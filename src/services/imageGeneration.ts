import { supabase } from '../components/common/utils/supabase';

export interface GenerateImagesParams {
  prompt: string;
  modelId: string;
  loraName: string;
  loraWeight?: number;
  angle: string;
  seed?: number;
  cfg?: number;
  fluxGuidance?: number;
  numImages?: number;
  aspectRatio?: string;
  format?: string;
  collectionId: string;
  productId?: string;
  metadata?: Record<string, unknown>;
  // V2 API - base64 encoded images
  faceImage?: string;
  image2?: string;
  image3?: string;
  image4?: string;
}

export interface GenerateImagesResponse {
  queue_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  images?: string[];
  error?: string;
}

export interface QueueStatusResponse {
  queue_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  images?: string[];
  error?: string;
}

/**
 * Generate images using the Fashion Lab API
 */
export async function generateImages(params: GenerateImagesParams): Promise<GenerateImagesResponse> {
  try {
    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      throw new Error('You must be logged in to generate images');
    }

    // Call the edge function
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-images`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: params.prompt,
        model_id: params.modelId,
        lora_name: params.loraName,
        lora_weight: params.loraWeight,
        angle: params.angle,
        seed: params.seed,
        cfg: params.cfg,
        flux_guidance: params.fluxGuidance,
        num_images: params.numImages,
        aspect_ratio: params.aspectRatio,
        format: params.format,
        collection_id: params.collectionId,
        product_id: params.productId,
        metadata: params.metadata,
        // V2 API fields
        face_image: params.faceImage,
        image_2: params.image2,
        image_3: params.image3,
        image_4: params.image4,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to generate images');
    }

    const result: GenerateImagesResponse = await response.json();
    return result;
  } catch (error) {
    console.error('Error generating images:', error);
    throw error;
  }
}

/**
 * Check the status of a generation queue
 */
export async function checkQueueStatus(queueId: string): Promise<QueueStatusResponse> {
  try {
    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      throw new Error('You must be logged in to check queue status');
    }

    // Call the queue-status edge function
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/queue-status`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        queue_id: queueId,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to check queue status');
    }

    const result: QueueStatusResponse = await response.json();
    return result;
  } catch (error) {
    console.error('Error checking queue status:', error);
    throw error;
  }
}

/**
 * Save generated images to the database
 */
export async function saveGeneratedImages(
  images: string[],
  params: GenerateImagesParams,
  queueId: string
): Promise<void> {
  try {
    // Create asset records for each generated image
    const assets = await Promise.all(images.map(async (imageUrl, index) => {
      // Extract filename from URL if possible
      let fileName = `generated_${params.modelId}_${params.angle}_${Date.now()}_${index}.jpg`;
      try {
        const url = new URL(imageUrl);
        const pathParts = url.pathname.split('/');
        const urlFileName = pathParts[pathParts.length - 1];
        if (urlFileName && urlFileName.includes('.')) {
          fileName = urlFileName;
        }
      } catch (e) {
        // If URL parsing fails, use the default filename
      }

      return {
        collection_id: params.collectionId,
        product_id: params.productId,
        file_name: fileName,
        file_path: imageUrl,
        file_type: params.format === 'png' ? 'image/png' : 'image/jpeg',
        file_size: 0, // Will be updated when we process the image
        workflow_stage: 'raw_ai_images' as const,
        metadata: {
          external_url: imageUrl, // Store the Fashion Lab URL in metadata instead
          generation_params: {
            prompt: params.prompt,
            model_id: params.modelId,
            lora_name: params.loraName,
            lora_weight: params.loraWeight,
            angle: params.angle,
            seed: params.seed,
            cfg: params.cfg,
            flux_guidance: params.fluxGuidance,
            aspect_ratio: params.aspectRatio,
            format: params.format,
          },
          queue_id: queueId,
          generated_at: new Date().toISOString(),
          source: 'fashion_lab_api',
          ...params.metadata,
        },
      };
    }));

    const { error } = await supabase
      .from('assets')
      .insert(assets);

    if (error) {
      console.error('Error saving generated images:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error saving generated images:', error);
    throw error;
  }
}