import { supabase } from '../components/common/utils/supabase';
import { generateImages as v1GenerateImages, checkQueueStatus as v1CheckStatus, GenerateImagesParams } from './imageGeneration';
import { FashionLabImageService } from './fashionLabImageService';

export interface UnifiedGenerationParams {
  // Common params
  prompt: string;
  collectionId: string;
  metadata?: Record<string, any>;
  
  // V1 API params
  modelId?: string;
  loraName?: string;
  loraWeight?: number;
  angle?: string;
  seed?: number;
  cfg?: number;
  fluxGuidance?: number;
  numImages?: number;
  aspectRatio?: string;
  format?: string;
  productId?: string;
  
  // V2 API params (base64 encoded images)
  faceImage?: string;
  image2?: string;
  image3?: string;
  image4?: string;
}

export interface UnifiedGenerationResult {
  queueId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  apiVersion: 'v1' | 'v2';
  images?: string[];
  error?: string;
}

export class UnifiedImageGenerationService {
  /**
   * Determines which API to use based on provided parameters
   */
  private static determineApiVersion(params: UnifiedGenerationParams): 'v1' | 'v2' {
    // If all V2 images are provided, use V2
    if (params.faceImage && params.image2 && params.image3 && params.image4) {
      return 'v2';
    }
    // Otherwise default to V1
    return 'v1';
  }

  /**
   * Generate images using either V1 or V2 API based on parameters
   */
  static async generateImages(params: UnifiedGenerationParams): Promise<UnifiedGenerationResult> {
    const apiVersion = this.determineApiVersion(params);
    
    try {
      if (apiVersion === 'v2') {
        // Use V2 API
        const result = await FashionLabImageService.generateImages({
          prompt: params.prompt,
          faceImage: params.faceImage!,
          image2: params.image2!,
          image3: params.image3!,
          image4: params.image4!,
          collectionId: params.collectionId,
          storeOnCompletion: true,
          metadata: params.metadata,
        });
        
        return {
          queueId: result.queue_id,
          status: 'processing',
          apiVersion: 'v2',
        };
      } else {
        // Use V1 API
        const v1Params: GenerateImagesParams = {
          prompt: params.prompt,
          modelId: params.modelId!,
          loraName: params.loraName!,
          loraWeight: params.loraWeight,
          angle: params.angle!,
          seed: params.seed,
          cfg: params.cfg,
          fluxGuidance: params.fluxGuidance,
          numImages: params.numImages,
          aspectRatio: params.aspectRatio,
          format: params.format,
          collectionId: params.collectionId,
          productId: params.productId,
          metadata: params.metadata,
        };
        
        const result = await v1GenerateImages(v1Params);
        
        return {
          queueId: result.queue_id,
          status: result.status,
          apiVersion: 'v1',
          images: result.images,
          error: result.error,
        };
      }
    } catch (error) {
      console.error('Error generating images:', error);
      throw error;
    }
  }

  /**
   * Check status of image generation (works with both V1 and V2)
   */
  static async checkStatus(
    queueId: string, 
    apiVersion: 'v1' | 'v2',
    collectionId?: string,
    prompt?: string
  ): Promise<UnifiedGenerationResult> {
    try {
      if (apiVersion === 'v2') {
        const result = await FashionLabImageService.checkQueueStatus(
          queueId,
          collectionId,
          true,
          prompt
        );
        
        return {
          queueId: result.queue_id,
          status: result.status,
          apiVersion: 'v2',
          images: result.images,
          error: result.error,
        };
      } else {
        const result = await v1CheckStatus(queueId);
        
        return {
          queueId: result.queue_id,
          status: result.status,
          apiVersion: 'v1',
          images: result.images,
          error: result.error,
        };
      }
    } catch (error) {
      console.error('Error checking status:', error);
      throw error;
    }
  }

  /**
   * Wait for completion with progress updates
   */
  static async waitForCompletion(
    queueId: string,
    apiVersion: 'v1' | 'v2',
    options: {
      collectionId?: string;
      prompt?: string;
      onProgress?: (progress: number) => void;
      maxAttempts?: number;
      pollInterval?: number;
    } = {}
  ): Promise<UnifiedGenerationResult> {
    const maxAttempts = options.maxAttempts ?? 60;
    const pollInterval = options.pollInterval ?? 5000;
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      const result = await this.checkStatus(
        queueId, 
        apiVersion, 
        options.collectionId,
        options.prompt
      );
      
      // Calculate progress for V1 (V2 already has progress)
      if (apiVersion === 'v1' && options.onProgress) {
        const progress = result.status === 'completed' ? 100 : 
                        result.status === 'processing' ? 50 : 10;
        options.onProgress(progress);
      }
      
      if (result.status === 'completed' || result.status === 'failed') {
        return result;
      }
      
      attempts++;
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }
    
    throw new Error('Timeout waiting for image generation');
  }

  /**
   * Get generated images from storage
   */
  static async getGeneratedImages(collectionId: string, apiVersion?: 'v1' | 'v2') {
    if (apiVersion === 'v2') {
      return FashionLabImageService.getGeneratedImages(collectionId);
    }
    
    // For V1, query assets table
    const { data, error } = await supabase
      .from('assets')
      .select('*')
      .eq('collection_id', collectionId)
      .eq('workflow_stage', 'raw_ai_images')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }
}