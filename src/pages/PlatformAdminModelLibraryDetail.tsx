import React, { useState } from 'react';
import { useParams, Navigate, useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit2, Upload } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Badge } from '../components/ui/badge';
import { Skeleton } from '../components/ui/skeleton';
import { useUserRole } from '../contexts/UserRoleContext';
import { ModelPreviewGrid } from '../components/model-library/ModelPreviewGrid';
import { EditModelDialog } from '../components/model-library/EditModelDialog';
import { useModel, useModelImages } from '../hooks/useModelLibrary';

export default function PlatformAdminModelLibraryDetail() {
  const { modelId } = useParams<{ modelId: string }>();
  const navigate = useNavigate();
  const { isPlatformUser, isLoadingRole } = useUserRole();
  const [showEditDialog, setShowEditDialog] = useState(false);

  // Fetch model details
  const { data: model, isLoading: modelLoading, refetch: refetchModel } = useModel(modelId || null);
  
  // Fetch model images
  const { data: images = [], isLoading: imagesLoading, refetch: refetchImages } = useModelImages(modelId || null);

  // Redirect if not platform admin
  if (!isLoadingRole && !isPlatformUser) {
    return <Navigate to="/dashboard" replace />;
  }

  if (isLoadingRole || modelLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <Skeleton className="h-12 w-64" />
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    );
  }

  if (!model) {
    return (
      <div className="container mx-auto p-6">
        <div className="max-w-7xl mx-auto text-center py-12">
          <h2 className="text-2xl font-bold mb-4">Model Not Found</h2>
          <p className="text-muted-foreground mb-6">The requested model could not be found.</p>
          <Button onClick={() => navigate('/admin/model-library')}>
            Back to Model Library
          </Button>
        </div>
      </div>
    );
  }

  const handleImageUpdate = () => {
    refetchImages();
  };

  const handleEditSuccess = () => {
    setShowEditDialog(false);
    refetchModel();
  };

  const uploadedCount = images.filter(img => img.storage_path).length;
  const completionPercentage = (uploadedCount / 11) * 100;

  return (
    <div className="container mx-auto p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin/model-library')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold">{model.name}</h1>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline">{model.code}</Badge>
                <Badge variant={model.is_active ? 'default' : 'secondary'}>
                  {model.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          </div>
          <Button onClick={() => setShowEditDialog(true)}>
            <Edit2 className="h-4 w-4 mr-2" />
            Edit Details
          </Button>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Model Info */}
          <div className="lg:col-span-1 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Model Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Display Order</p>
                  <p className="text-lg">{model.display_order}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Created</p>
                  <p className="text-lg">{new Date(model.created_at).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                  <p className="text-lg">{new Date(model.updated_at).toLocaleDateString()}</p>
                </div>
                {model.description && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Description</p>
                    <p className="text-sm mt-1">{model.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Upload Progress</CardTitle>
                <CardDescription>
                  {uploadedCount} of 11 images uploaded
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className="bg-green-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${completionPercentage}%` }}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground text-center">
                    {completionPercentage.toFixed(0)}% Complete
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Images Grid */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Model Images</CardTitle>
                <CardDescription>
                  Upload images for each angle. Click on any image placeholder to upload.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="all" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="all">All Angles</TabsTrigger>
                    <TabsTrigger value="face">Face</TabsTrigger>
                    <TabsTrigger value="half-body">Half Body</TabsTrigger>
                    <TabsTrigger value="full-body">Full Body</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="all" className="mt-6">
                    <ModelPreviewGrid
                      modelId={model.id}
                      modelCode={model.code}
                      modelName={model.name}
                      images={images}
                      isLoading={imagesLoading}
                      canEdit={true}
                      onImageUpdate={handleImageUpdate}
                    />
                  </TabsContent>
                  
                  <TabsContent value="face" className="mt-6">
                    <div className="grid grid-cols-1 gap-4">
                      <ModelPreviewGrid
                        modelId={model.id}
                        modelCode={model.code}
                        modelName={model.name}
                        images={images.filter(img => img.angle_type === 'face')}
                        isLoading={imagesLoading}
                        canEdit={true}
                        onImageUpdate={handleImageUpdate}
                      />
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="half-body" className="mt-6">
                    <ModelPreviewGrid
                      modelId={model.id}
                      modelCode={model.code}
                      modelName={model.name}
                      images={images.filter(img => img.angle_type.startsWith('half-body'))}
                      isLoading={imagesLoading}
                      canEdit={true}
                      onImageUpdate={handleImageUpdate}
                    />
                  </TabsContent>
                  
                  <TabsContent value="full-body" className="mt-6">
                    <ModelPreviewGrid
                      modelId={model.id}
                      modelCode={model.code}
                      modelName={model.name}
                      images={images.filter(img => img.angle_type.startsWith('full-body'))}
                      isLoading={imagesLoading}
                      canEdit={true}
                      onImageUpdate={handleImageUpdate}
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Edit Dialog */}
        {model && (
          <EditModelDialog
            model={model}
            isOpen={showEditDialog}
            onClose={() => setShowEditDialog(false)}
            onSuccess={handleEditSuccess}
          />
        )}
      </div>
    </div>
  );
}