import React, { useState } from 'react';
import { useUserRole } from '../contexts/UserRoleContext';
import { Navigate, useNavigate } from 'react-router-dom';
import { PageTitle } from '../components/ui/PageTitle';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Plus, Package, ImageIcon } from 'lucide-react';
import { ModelLibraryTable, Model } from '../components/model-library/ModelLibraryTable';
import { CreateModelDialog } from '../components/model-library/CreateModelDialog';
import { EditModelDialog } from '../components/model-library/EditModelDialog';
import { useModelLibrary, useUpdateModel, useDeleteModel } from '../hooks/useModelLibrary';

export default function PlatformAdminModelLibrary() {
  const { isPlatformUser, isLoadingRole } = useUserRole();
  const navigate = useNavigate();
  const { models, isLoading, refetch } = useModelLibrary();
  const updateModelMutation = useUpdateModel();
  const deleteModelMutation = useDeleteModel();
  
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingModel, setEditingModel] = useState<Model | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Redirect if not platform admin
  if (!isLoadingRole && !isPlatformUser) {
    return <Navigate to="/dashboard" replace />;
  }

  if (isLoadingRole || isLoading) {
    return (
      <div className="container mx-auto p-6 text-center">
        <p>Loading...</p>
      </div>
    );
  }

  const handleRowClick = (model: Model) => {
    // Navigate to model detail page
    navigate(`/admin/model-library/${model.id}`);
  };

  const handleEdit = (model: Model) => {
    setEditingModel(model);
    setIsEditDialogOpen(true);
  };

  const handleDelete = async (model: Model) => {
    if (!confirm(`Are you sure you want to delete "${model.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteModelMutation.mutateAsync(model.id);
      refetch();
    } catch (error) {
      console.error('Error deleting model:', error);
    }
  };

  const handleToggleActive = async (model: Model) => {
    try {
      await updateModelMutation.mutateAsync({
        id: model.id,
        is_active: !model.is_active
      });
      refetch();
    } catch (error) {
      console.error('Error toggling model status:', error);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="flex justify-between items-start mb-6">
        <PageTitle 
          title="Model Library" 
          subtitle="Manage model images for the AI image generator"
        />
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Model
        </Button>
      </div>

      {/* Statistics Card */}
      <Card className="mb-6">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Models</CardTitle>
          <Package className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{models?.length || 0}</div>
          <p className="text-xs text-muted-foreground">
            {models?.filter(m => m.is_active).length || 0} active models
          </p>
        </CardContent>
      </Card>

      {/* Models Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Models</CardTitle>
          <CardDescription>
            Click on a model to manage its images for different angles
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ModelLibraryTable 
            models={models || []} 
            isLoading={isLoading}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onToggleActive={handleToggleActive}
          />
          <div className="mt-4 text-sm text-muted-foreground">
            <p>Click on a model to manage its images</p>
          </div>
        </CardContent>
      </Card>

      {/* Angle Reference */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Angle Reference</CardTitle>
          <CardDescription>
            Each model should have images for all these angles
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">Half-body Angles</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>Half-body Front</li>
                <li>Half-body Back</li>
                <li>Half-body 3/4 Left</li>
                <li>Half-body 3/4 Right</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Full-height Angles</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>Full-height Front</li>
                <li>Full-height Back</li>
                <li>Full-height Side Left</li>
                <li>Full-height Side Right</li>
                <li>Full-height 3/4 Left</li>
                <li>Full-height 3/4 Right</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <CreateModelDialog 
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSuccess={() => {
          setIsCreateDialogOpen(false);
          refetch();
        }}
      />

      {editingModel && (
        <EditModelDialog
          model={editingModel}
          isOpen={isEditDialogOpen}
          onClose={() => {
            setIsEditDialogOpen(false);
            setEditingModel(null);
          }}
          onSuccess={() => {
            setIsEditDialogOpen(false);
            setEditingModel(null);
            refetch();
          }}
        />
      )}
    </div>
  );
}