import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Textarea } from '../components/ui/textarea';
import { Label } from '../components/ui/label';
import { Alert, AlertDescription } from '../components/ui/alert';
import { Progress } from '../components/ui/progress';
import { 
  Upload, ArrowLeft, Sparkles, Loader2, Image as ImageIcon, CheckCircle, AlertCircle
} from 'lucide-react';
import { cn } from '../components/common/utils/utils';
import { useFashionLabImages } from '../hooks/useFashionLabImages';
import { supabase } from '../components/common/utils/supabase';

interface ImageUpload {
  file: File;
  preview: string;
  base64?: string;
}

export default function CollectionGenerateV2() {
  const navigate = useNavigate();
  const { orgId, collectionId } = useParams<{ orgId: string; collectionId: string }>();
  
  // V2 API State
  const [prompt, setPrompt] = useState('Professional model wearing elegant outfit in studio setting');
  const [images, setImages] = useState<{
    face?: ImageUpload;
    image_2?: ImageUpload;
    image_3?: ImageUpload;
    image_4?: ImageUpload;
  }>({});
  
  const [isProcessingImages, setIsProcessingImages] = useState(false);
  const [generationStatus, setGenerationStatus] = useState<string>('');
  
  // Use the Fashion Lab images hook
  const {
    generate,
    isGenerating,
    progress,
    activeQueueId,
    generatedImages,
    isLoadingImages,
    refetchImages
  } = useFashionLabImages({
    collectionId: collectionId!,
    onSuccess: (queueId) => {
      setGenerationStatus(`Generation started! Queue ID: ${queueId}`);
    },
    onComplete: (images) => {
      setGenerationStatus(`Generation complete! ${images.length} images created.`);
      refetchImages();
    }
  });

  // Handle image upload
  const handleImageUpload = async (type: 'face' | 'image_2' | 'image_3' | 'image_4', file: File) => {
    // Check file size (Fashion Lab API has ~1MB limit)
    const maxSize = 1024 * 1024; // 1MB
    if (file.size > maxSize) {
      const sizeMB = (file.size / (1024 * 1024)).toFixed(1);
      setGenerationStatus(`Warning: ${file.name} is ${sizeMB}MB. Fashion Lab API works best with images under 1MB. Consider compressing the image.`);
    }

    const preview = URL.createObjectURL(file);

    setIsProcessingImages(true);
    const reader = new FileReader();
    reader.onloadend = () => {
      const base64 = reader.result as string;
      setImages(prev => ({
        ...prev,
        [type]: { file, preview, base64 }
      }));
      setIsProcessingImages(false);
    };
    reader.readAsDataURL(file);
  };

  // Handle generation
  const handleGenerate = async () => {
    if (!images.face?.base64 || !images.image_2?.base64 || !images.image_3?.base64 || !images.image_4?.base64) {
      setGenerationStatus('Please upload all 4 required images');
      return;
    }

    try {
      await generate({
        prompt,
        faceImage: images.face.base64,
        image2: images.image_2.base64,
        image3: images.image_3.base64,
        image4: images.image_4.base64,
        metadata: {
          source: 'collection-generate-v2'
        }
      });
    } catch (error) {
      setGenerationStatus(`Generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Clean up preview URLs
  useEffect(() => {
    return () => {
      Object.values(images).forEach(img => {
        if (img?.preview && img.preview.startsWith('blob:')) {
          URL.revokeObjectURL(img.preview);
        }
      });
    };
  }, [images]);

  const imageTypes = [
    { id: 'face', label: 'Face Image', description: 'Clear frontal face photo' },
    { id: 'image_2', label: 'Reference Image 1', description: 'First style reference' },
    { id: 'image_3', label: 'Reference Image 2', description: 'Second style reference' },
    { id: 'image_4', label: 'Reference Image 3', description: 'Third style reference' }
  ] as const;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-8">
        {/* Header */}
        <div className="mb-8">
          <Button 
            variant="ghost" 
            onClick={() => navigate(`/organizations/${orgId}/collections/${collectionId}`)}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Collection
          </Button>
          <h1 className="text-3xl font-bold mb-2">Generate AI Fashion Images</h1>
          <p className="text-gray-600">Upload a face image and 3 reference images to generate new fashion photography</p>
          <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700">
              💡 <strong>Tip:</strong> For best results, use images under 1MB each. Larger images may fail during processing.
            </p>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Inputs */}
          <div className="space-y-6">
            {/* Prompt Card */}
            <Card>
              <CardHeader>
                <CardTitle>Prompt</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Describe the fashion look you want to generate..."
                  className="min-h-[100px]"
                />
              </CardContent>
            </Card>

            {/* Image Upload Card */}
            <Card>
              <CardHeader>
                <CardTitle>Upload Images</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {imageTypes.map((type) => (
                  <div key={type.id} className="space-y-2">
                    <Label className="text-sm font-medium">{type.label}</Label>
                    <p className="text-xs text-gray-500">{type.description}</p>
                    
                    {images[type.id] ? (
                      <div className="relative group">
                        <img 
                          src={images[type.id]!.preview} 
                          alt={type.label}
                          className="w-full h-48 object-cover rounded-lg border"
                        />
                        <button
                          onClick={() => setImages(prev => ({ ...prev, [type.id]: undefined }))}
                          className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-4 h-4" />
                        </button>
                        <Badge className="absolute bottom-2 left-2 bg-green-500">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Uploaded
                        </Badge>
                      </div>
                    ) : (
                      <label className="flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-lg cursor-pointer hover:border-gray-400 transition-colors">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageUpload(type.id, file);
                          }}
                          className="hidden"
                        />
                        <Upload className="w-8 h-8 text-gray-400 mb-2" />
                        <span className="text-sm text-gray-500">Click to upload</span>
                      </label>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Generate Button */}
            <Button 
              onClick={handleGenerate}
              disabled={
                isGenerating || 
                isProcessingImages ||
                !images.face || 
                !images.image_2 || 
                !images.image_3 || 
                !images.image_4
              }
              className="w-full h-12"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Generating... {progress}%
                </>
              ) : (
                <>
                  <Sparkles className="w-5 h-5 mr-2" />
                  Generate Fashion Images
                </>
              )}
            </Button>

            {/* Status Alert */}
            {generationStatus && (
              <Alert className={isGenerating ? 'border-blue-200' : generatedImages.length > 0 ? 'border-green-200' : 'border-yellow-200'}>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{generationStatus}</AlertDescription>
              </Alert>
            )}
          </div>

          {/* Right Column - Results */}
          <div className="space-y-6">
            <Card className="h-full">
              <CardHeader>
                <CardTitle>Generated Images</CardTitle>
                <p className="text-sm text-gray-500">
                  Images will appear here after generation. They are stored separately and can be selected for your collection later.
                </p>
              </CardHeader>
              <CardContent>
                {isLoadingImages ? (
                  <div className="flex items-center justify-center h-96">
                    <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
                  </div>
                ) : generatedImages.length > 0 ? (
                  <div className="grid grid-cols-2 gap-4">
                    {generatedImages.map((image: any) => (
                      <div key={image.id} className="space-y-2">
                        <div className="relative group">
                          <img 
                            src={`${import.meta.env.VITE_SUPABASE_URL}/storage/v1/object/public/ai-generated/${image.storage_path}`}
                            alt="Generated"
                            className="w-full h-48 object-cover rounded-lg border"
                          />
                          {image.selected && (
                            <Badge className="absolute top-2 right-2 bg-green-500">
                              Selected
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 truncate">
                          {new Date(image.created_at).toLocaleString()}
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-96 text-gray-400">
                    <ImageIcon className="w-16 h-16 mb-4" />
                    <p className="text-lg font-medium">No images generated yet</p>
                    <p className="text-sm mt-2">Upload your images and click generate to start</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Progress Bar */}
        {isGenerating && (
          <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
            <div className="max-w-7xl mx-auto">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Generating images...</span>
                <span className="text-sm text-gray-500">{progress}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}