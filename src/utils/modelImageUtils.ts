import { supabase } from '../components/common/utils/supabase';

/**
 * Get the best available preview image for a model
 * Priority: face > half-body-front > full-body-front > any available image
 */
export function getModelPreviewImage(
  modelImageMapping: Record<string, string> | undefined,
  modelCode: string
): string {
  if (!modelImageMapping) {
    return getDefaultModelImage(modelCode);
  }

  // Priority order for preview images
  const priorityOrder = [
    'face',
    'half-body-front', 
    'Half-body Front', // Legacy format
    'full-body-front',
    'Full-height Front', // Legacy format
    'half-body-back',
    'Half-body Back', // Legacy format
    'full-body-back',
    'Full-height Back' // Legacy format
  ];

  // Try each priority option
  for (const angleType of priorityOrder) {
    const imageUrl = modelImageMapping[angleType];
    if (imageUrl) {
      return imageUrl;
    }
  }

  // If no priority images found, use any available image
  const availableImages = Object.values(modelImageMapping);
  if (availableImages.length > 0) {
    return availableImages[0];
  }

  // Final fallback to default image
  return getDefaultModelImage(modelCode);
}

/**
 * Get a default placeholder image for a model
 */
function getDefaultModelImage(modelCode: string): string {
  // Use a generic placeholder or model-specific default
  const defaultImages: Record<string, string> = {
    'S': 'Final/5713732546603_M_front.jpg',
    'M': 'Final/5715674861424_M_front.jpg',
    'L': 'Final/5715677190187_M_front.jpg',
    'XL': 'Final/5715678691409_M_front.jpg'
  };

  return defaultImages[modelCode] || 'placeholder.svg';
}

/**
 * Get model image URL from storage path
 */
export function getModelImageUrl(storagePath: string | null): string | null {
  if (!storagePath) return null;
  
  // Remove duplicate prefix if it exists
  const cleanPath = storagePath.replace(/^model-library\//, '');
  const { data } = supabase.storage.from('model-library').getPublicUrl(cleanPath);
  return data?.publicUrl || null;
}

/**
 * Check if a model has a face image available
 */
export function modelHasFaceImage(
  modelImageMapping: Record<string, string> | undefined
): boolean {
  if (!modelImageMapping) return false;
  
  return !!(modelImageMapping['face'] || modelImageMapping['Face']);
}

/**
 * Get face image URL for a model
 */
export function getModelFaceImage(
  modelImageMapping: Record<string, string> | undefined
): string | null {
  if (!modelImageMapping) return null;
  
  return modelImageMapping['face'] || modelImageMapping['Face'] || null;
}
