import { useState, useCallback } from 'react';
import { generateImages, checkQueueStatus, saveGeneratedImages } from '../services/imageGeneration';
import type { GenerateImagesParams, GenerateImagesResponse, QueueStatusResponse } from '../services/imageGeneration';

export interface UseImageGenerationReturn {
  isGenerating: boolean;
  progress: number;
  error: string | null;
  generateImages: (params: GenerateImagesParams) => Promise<GenerateImagesResponse>;
  checkStatus: (queueId: string) => Promise<QueueStatusResponse>;
}

export function useImageGeneration(): UseImageGenerationReturn {
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const handleGenerateImages = useCallback(async (params: GenerateImagesParams) => {
    setIsGenerating(true);
    setProgress(0);
    setError(null);

    try {
      // Call the API
      const result = await generateImages(params);
      
      // If the API returns a queue ID, we need to poll for results
      if (result.queue_id && result.status === 'processing') {
        // Return a promise that resolves when polling is complete
        return new Promise<GenerateImagesResponse>((resolve, reject) => {
          const pollInterval = setInterval(async () => {
            try {
              const status = await checkQueueStatus(result.queue_id);
              
              // Update progress
              if (status.progress) {
                setProgress(status.progress);
              }
              
              // Check if completed
              if (status.status === 'completed' && status.images) {
                clearInterval(pollInterval);
                setIsGenerating(false);
                setProgress(100);
                
                // Save images to database
                await saveGeneratedImages(status.images, params, result.queue_id);
                
                // Resolve with the completed status
                resolve({
                  queue_id: result.queue_id,
                  status: 'completed',
                  images: status.images
                });
              }
              
              // Check if failed
              if (status.status === 'failed') {
                clearInterval(pollInterval);
                setIsGenerating(false);
                const errorMsg = status.error || 'Generation failed';
                setError(errorMsg);
                reject(new Error(errorMsg));
              }
            } catch (err) {
              clearInterval(pollInterval);
              setIsGenerating(false);
              const errorMsg = err instanceof Error ? err.message : 'Failed to check status';
              setError(errorMsg);
              reject(new Error(errorMsg));
            }
          }, 2000); // Poll every 2 seconds
        });
      }
      
      return result;
    } catch (err) {
      setIsGenerating(false);
      setError(err instanceof Error ? err.message : 'Failed to generate images');
      throw err;
    }
  }, []);

  const handleCheckStatus = useCallback(async (queueId: string) => {
    try {
      const status = await checkQueueStatus(queueId);
      return status;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check status');
      throw err;
    }
  }, []);

  return {
    isGenerating,
    progress,
    error,
    generateImages: handleGenerateImages,
    checkStatus: handleCheckStatus,
  };
}