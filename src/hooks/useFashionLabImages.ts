import { useState, useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { FashionLabImageService } from '../services/fashionLabImageService';
import { toast } from 'react-hot-toast';

interface UseGenerateImagesOptions {
  collectionId: string;
  onSuccess?: (queueId: string) => void;
  onComplete?: (images: string[]) => void;
}

interface GenerateParams {
  prompt: string;
  faceImage: string;
  image2: string;
  image3: string;
  image4: string;
  metadata?: any;
}

export function useFashionLabImages(options: UseGenerateImagesOptions) {
  const queryClient = useQueryClient();
  const [activeQueueId, setActiveQueueId] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [currentPrompt, setCurrentPrompt] = useState<string>('');

  // Generate images mutation (V2 API)
  const generateMutation = useMutation({
    mutationFn: async (params: GenerateParams) => {
      setCurrentPrompt(params.prompt);
      const result = await FashionLabImageService.generateImages({
        ...params,
        collectionId: options.collectionId,
        storeOnCompletion: true,
      });
      return result.queue_id;
    },
    onSuccess: (queueId) => {
      setActiveQueueId(queueId);
      setProgress(0);
      toast.success('Image generation started');
      options.onSuccess?.(queueId);
      
      // Start polling for completion
      pollForCompletion(queueId);
    },
    onError: (error: Error) => {
      toast.error(`Failed to generate images: ${error.message}`);
    },
  });

  // Poll for completion
  const pollForCompletion = useCallback(async (queueId: string) => {
    try {
      const result = await FashionLabImageService.waitForCompletion(
        queueId,
        options.collectionId,
        {
          onProgress: setProgress,
          prompt: currentPrompt,
        }
      );

      if (result.status === 'completed' && result.stored) {
        toast.success('Images generated and stored successfully');
        
        // Invalidate queries to refresh image lists
        queryClient.invalidateQueries(['ai-generated-images', options.collectionId]);
        queryClient.invalidateQueries(['generated-images', options.collectionId]);
        
        if (result.images) {
          options.onComplete?.(result.images);
        }
      } else if (result.status === 'failed') {
        toast.error('Image generation failed');
      }
      
      setActiveQueueId(null);
      setProgress(0);
    } catch (error) {
      toast.error('Failed to complete image generation');
      setActiveQueueId(null);
      setProgress(0);
    }
  }, [options, queryClient, currentPrompt]);

  // Query for generated images
  const generatedImagesQuery = useQuery({
    queryKey: ['generated-images', options.collectionId],
    queryFn: () => FashionLabImageService.getGeneratedImages(options.collectionId),
    enabled: !!options.collectionId,
  });

  // Check status manually
  const checkStatus = useCallback(async (queueId: string) => {
    const result = await FashionLabImageService.checkQueueStatus(
      queueId,
      options.collectionId
    );
    return result;
  }, [options.collectionId]);

  return {
    generate: generateMutation.mutate,
    isGenerating: generateMutation.isPending || activeQueueId !== null,
    progress,
    activeQueueId,
    generatedImages: generatedImagesQuery.data || [],
    isLoadingImages: generatedImagesQuery.isLoading,
    checkStatus,
    refetchImages: generatedImagesQuery.refetch,
  };
}