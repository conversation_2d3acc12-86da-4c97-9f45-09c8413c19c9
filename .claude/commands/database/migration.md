# Create Database Migration: $ARGUMENTS

I'll create a new database migration for: $ARGUMENTS

## 1. Analyze Requirements

Understanding "$ARGUMENTS":
- Parse the description to understand the change
- Identify affected tables and columns
- Determine if this requires RLS policy updates

## 2. Check Current Schema

Using mcp__supabase__list_tables:
- Get current table structure
- Check existing columns and constraints
- Review related RLS policies

## 3. Generate Migration

### Create Migration File
```bash
supabase migration new $ARGUMENTS
```

### Write SQL Migration
Based on "$ARGUMENTS", generate:
- CREATE TABLE statements
- ALTER TABLE statements
- Index creation
- RLS policies

### Migration Best Practices
- Use IF NOT EXISTS/IF EXISTS
- Make migrations idempotent
- Handle existing data gracefully
- Include rollback considerations

## 4. RLS Policy Updates

For new tables or columns:
- Create appropriate SELECT policies
- Add INSERT/UPDATE/DELETE policies
- Consider organization-based access
- Test with different user roles

## 5. Test Migration Locally

### Reset Local Database
```bash
supabase db reset
```

### Verify Changes
- Check table structure
- Test RLS policies
- Verify data integrity
- Run application tests

## 6. Document Changes

Update documentation:
- Add to database schema docs
- Update type definitions
- Document any breaking changes
- Add migration notes

## 7. Prepare for Deployment

- Commit migration file
- Update any affected code
- Plan staging deployment
- Consider data migration needs

Creating migration for "$ARGUMENTS"...