# Deploy Database Migrations to Production

I'll deploy pending database migrations to production using the Supabase MCP.

## Pre-Deployment Checks

1. **Confirm Deployment**
   - Show warning about production deployment
   - List migrations to be applied
   - Require explicit confirmation

2. **Verify Staging**
   - Confirm migrations work on staging
   - Check for any reported issues

## Process

1. **Check Local Migrations**
   - List all migration files in `supabase/migrations/`
   - Sort them by timestamp

2. **Check Applied Migrations**
   - Connect to production project: `cpelxqvcjnbpnphttzsn`
   - List already applied migrations

3. **Apply Pending Migrations**
   For each unapplied migration:
   - Read the migration file content
   - Apply using `mcp__supabase__apply_migration`
   - Show success/failure status

4. **Verify Migration Status**
   - List all migrations again to confirm
   - Report any failures

## Error Handling
- If a migration fails, stop immediately
- DO NOT attempt to continue
- Provide specific error details
- Suggest contacting senior developer

## Post-Deployment
- Verify production is working
- Update Linear issues
- Monitor for any issues

Starting migration deployment to production...