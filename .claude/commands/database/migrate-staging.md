# Deploy Database Migrations to Staging

I'll deploy pending database migrations to staging using the Supabase MCP.

## Process

1. **Check Local Migrations**
   - List all migration files in `supabase/migrations/`
   - Sort them by timestamp

2. **Check Applied Migrations**
   - Connect to staging project: `qnfmiotatmkoumlymynq`
   - List already applied migrations

3. **Apply Pending Migrations**
   For each unapplied migration:
   - Read the migration file content
   - Apply using `mcp__supabase__apply_migration`
   - Show success/failure status

4. **Verify Migration Status**
   - List all migrations again to confirm
   - Report any failures

## Error Handling
- If a migration fails, stop and report the error
- Provide rollback instructions if needed
- Suggest manual intervention steps

Starting migration deployment to staging...