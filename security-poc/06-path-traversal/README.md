# Vulnerability: Potential Path Traversal in File Upload/Storage

## Summary
The file storage system constructs paths using user-controlled input (collection IDs, asset IDs) which could potentially be exploited for path traversal attacks if not properly sanitized.

## Risk Level: CRITICAL

## Impact
- Access to files outside intended directories
- Read sensitive system files
- Overwrite critical files
- Access other organizations' files
- Potential for remote code execution

## Technical Details

### Vulnerable Code Pattern
```typescript
// From assetStorage.ts
export function generateAssetPath(
  collectionId: string,
  assetId: string,
  type: AssetType,
  fileExtension: string = 'webp'
): string {
  const fileName = `${assetId}.${fileExtension}`;
  return `collections/${collectionId}/${fileName}`;
}
```

### Attack Vectors
1. Malicious collection ID: `../../../etc/passwd`
2. Malicious asset ID: `../../other-org/sensitive`
3. File extension manipulation: `.webp/../../../evil.js`
4. Null byte injection: `file.jpg%00.php`

## Exploitation Scenarios

### 1. Directory Traversal
- Use `../` sequences to escape storage bucket
- Access files from other organizations
- Read system configuration files

### 2. File Overwrite
- Target specific files with crafted paths
- Overwrite legitimate assets
- Plant malicious files

### 3. Information Disclosure
- List directory contents
- Access backup files
- Read configuration files

## Evidence
- Successful path traversal demonstration
- Unauthorized file access
- Cross-organization file access

## Remediation
1. Strict input validation for all IDs
2. Use path.normalize() and check for traversal
3. Whitelist allowed characters
4. Use UUIDs without user control
5. Implement proper sandboxing
6. Add file access audit logs
7. Use separate storage contexts per org