# Vulnerability: Insecure Direct Object Reference (IDOR) in Asset Access

## Summary
The application allows direct access to assets by ID without proper authorization checks in the frontend. While backend RLS policies may provide some protection, the frontend code fetches assets directly by ID without verifying organization membership.

## Risk Level: HIGH

## Impact
- Unauthorized access to assets from other organizations
- Information disclosure of sensitive fashion designs
- Potential intellectual property theft
- Privacy violations for unreleased collections

## Technical Details

### Location
- File: `/src/pages/AssetDetail.tsx` (lines 87-181)
- Vulnerable endpoint: Direct asset fetching by ID
- No organization validation in frontend

### Vulnerable Code
```typescript
const { data, error } = await supabase
  .from('assets')
  .select('*')
  .eq('id', assetId)
  .single();
```

## Preconditions
1. Valid user account (any role)
2. Knowledge of target asset IDs
3. Access to the application

## Exploitation Steps

### Attack Scenario
1. Attacker logs in with their account (Organization A)
2. Attacker discovers or guesses asset IDs from Organization B
3. Attacker directly navigates to `/assets/{targetAssetId}`
4. Frontend fetches and displays the asset without organization check

### ID Discovery Methods
- Sequential ID enumeration
- ID patterns from legitimate access
- Information leakage in API responses
- Shared links or screenshots

## Evidence
- Screenshots of unauthorized asset access
- API request/response logs
- Cross-organization data access proof

## Remediation
1. Add organization validation in frontend before fetching
2. Include organization_id in all asset queries
3. Implement proper access control checks
4. Use UUIDs to prevent ID enumeration
5. Add audit logging for asset access
6. Validate collection ownership in asset queries