#!/bin/bash
# Run all security PoC exploits
# WARNING: Only run against test/development environments!

echo "=========================================="
echo "FashionLab Security PoC Exploit Runner"
echo "=========================================="
echo ""
echo "⚠️  WARNING: These exploits should only be run against"
echo "test/development environments with proper authorization!"
echo ""
read -p "Are you sure you want to continue? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "Aborted."
    exit 1
fi

# Create evidence directory if it doesn't exist
mkdir -p evidence

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo ""
echo "Starting security tests..."
echo ""

# Function to run exploit
run_exploit() {
    local dir=$1
    local name=$2
    
    echo -e "${YELLOW}[*] Running: $name${NC}"
    echo "----------------------------------------"
    
    if [ -f "$dir/exploit.py" ]; then
        cd "$dir"
        python3 exploit.py
        cd - > /dev/null
        echo ""
    else
        echo -e "${RED}[-] Exploit script not found: $dir/exploit.py${NC}"
    fi
}

# Run each exploit
run_exploit "01-hardcoded-credentials" "Hardcoded Credentials"
run_exploit "02-idor-asset-access" "IDOR in Asset Access"
run_exploit "03-cors-misconfiguration" "CORS Misconfiguration"
run_exploit "04-missing-rate-limiting" "Missing Rate Limiting"
run_exploit "05-tag-privilege-escalation" "Tag Privilege Escalation"
run_exploit "06-path-traversal" "Path Traversal"

echo ""
echo "=========================================="
echo -e "${GREEN}[+] All tests completed!${NC}"
echo "=========================================="
echo ""
echo "Evidence files have been saved in each exploit's evidence/ directory"
echo "Review the JSON reports for detailed findings and recommendations"
echo ""

# Generate summary report
echo "Generating summary report..."
python3 - << 'EOF'
import json
import os
from datetime import datetime

summary = {
    "report_date": datetime.now().isoformat(),
    "vulnerabilities": [],
    "total_critical": 0,
    "total_high": 0
}

# Define vulnerability severity
vuln_severity = {
    "01-hardcoded-credentials": "CRITICAL",
    "02-idor-asset-access": "HIGH",
    "03-cors-misconfiguration": "HIGH",
    "04-missing-rate-limiting": "HIGH",
    "05-tag-privilege-escalation": "HIGH",
    "06-path-traversal": "CRITICAL"
}

# Collect results from each exploit
for dir_name, severity in vuln_severity.items():
    report_path = os.path.join(dir_name, "evidence", "*.json")
    
    vuln_info = {
        "name": dir_name.replace("-", " ").title(),
        "severity": severity,
        "tested": os.path.exists(dir_name)
    }
    
    if severity == "CRITICAL":
        summary["total_critical"] += 1
    elif severity == "HIGH":
        summary["total_high"] += 1
    
    summary["vulnerabilities"].append(vuln_info)

# Save summary
with open("summary_report.json", "w") as f:
    json.dump(summary, f, indent=2)

print(f"\nSummary: {summary['total_critical']} CRITICAL and {summary['total_high']} HIGH vulnerabilities tested")
print("Full summary saved to: summary_report.json")
EOF