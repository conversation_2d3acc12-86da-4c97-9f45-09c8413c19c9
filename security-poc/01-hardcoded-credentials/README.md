# Vulnerability: Hardcoded Test Credentials in Development Mode

## Summary
The application exposes hardcoded test credentials in the login page when running in development mode. These credentials include high-privilege accounts like Platform Super Admin.

## Risk Level: CRITICAL

## Impact
- Unauthorized access to any development/staging environment
- Full platform control if Platform Super Admin credentials are used
- Data breach potential if development database contains real data
- Lateral movement to production if same passwords are reused

## Technical Details

### Location
- File: `/src/pages/Login.tsx` (lines 63-71)
- Exposed when: `isDevelopmentEnvironment()` returns true
- Detection: Checks if hostname is 'localhost' or '127.0.0.1'

### Vulnerable Code
```typescript
const testCredentials = [
  { role: 'Platform Super Admin', email: '<EMAIL>', password: 'test123456' },
  { role: 'Platform Admin', email: '<EMAIL>', password: 'test123456' },
  // ... more credentials
];
```

## Preconditions
1. Application running in development mode
2. Access to the login page
3. No additional authentication required

## Exploitation Steps

### Manual Exploitation
1. Navigate to the login page
2. If in development mode, test credential buttons appear
3. Click any button to auto-fill credentials
4. Click "Sign In" to authenticate

### Automated Exploitation
Run: `python exploit.py`

## Evidence
- Screenshot of exposed credentials UI
- Successful authentication logs
- Access to privileged resources

## Remediation
1. Remove hardcoded credentials from production code
2. Use environment variables for test accounts
3. Implement proper detection for development mode
4. Add warning banners for test environments
5. Use different passwords for each test account
6. Rotate all test account passwords regularly