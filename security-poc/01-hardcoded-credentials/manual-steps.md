# Manual Exploitation Steps: Hardcoded Credentials

## Prerequisites
- Web browser (Chrome, Firefox, etc.)
- Access to a FashionLab development instance

## Step-by-Step Manual Exploitation

### 1. Identify Development Environment
Open your browser and navigate to:
- `http://localhost:8080` or
- `http://127.0.0.1:8080` or
- Any development/staging URL

### 2. Access Login Page
Navigate to `/login` or click the login button.

### 3. Verify Development Mode
Look for a section titled "Development Quick Login" below the login form. This section only appears in development mode.

### 4. View Available Credentials
You should see buttons for:
- Platform Super Admin
- Platform Admin
- Brand Admin (Vero Moda)
- Brand Member (Vero Moda)
- External Retoucher
- External Prompter
- Brand Admin (H&M)

Note: All accounts use password: `test123456`

### 5. Select Target Account
Click on any button to auto-fill the credentials. For maximum impact, select "Platform Super Admin".

### 6. Login
Click the "Sign In" button. You should be authenticated and redirected to the dashboard.

### 7. Verify Access Level
Once logged in, verify your access by:
- Navigating to `/organizations` (Platform admins only)
- Viewing all organizations and users
- Creating new organizations
- Modifying any user accounts

### 8. Document Evidence
Take screenshots of:
1. The login page showing test credential buttons
2. The auto-filled credentials
3. Successful login confirmation
4. Access to privileged areas (e.g., platform admin panel)

## Using curl (Command Line)

```bash
# 1. Authenticate with hardcoded credentials
curl -X POST http://localhost:54321/auth/v1/token?grant_type=password \
  -H "Content-Type: application/json" \
  -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0" \
  -d '{
    "email": "<EMAIL>",
    "password": "test123456"
  }'

# 2. Save the access_token from the response

# 3. Use the token to access protected resources
curl -X GET http://localhost:54321/rest/v1/organizations \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
```

## Expected Results
- Successful authentication with any test account
- Full access based on the role (Platform Super Admin has complete control)
- Ability to view/modify any data in the system

## Notes
- These credentials work on any instance where `isDevelopmentEnvironment()` returns true
- The detection is client-side only (checks hostname)
- Credentials are visible in the source code on GitHub