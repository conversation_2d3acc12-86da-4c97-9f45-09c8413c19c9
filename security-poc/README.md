# FashionLab Security Proof-of-Concept Exploits

This directory contains proof-of-concept (PoC) exploits for vulnerabilities identified in the FashionLab platform. These are for testing and validation purposes only.

## ⚠️ IMPORTANT DISCLAIMER

These exploits are provided for security testing purposes only. They should:
- Only be used on test/development environments
- Never be used on production systems without explicit authorization
- Be handled with care as they demonstrate real vulnerabilities

## Vulnerabilities Covered

1. **Hardcoded Test Credentials** (Critical)
2. **IDOR in Asset/Collection Access** (High)
3. **Overly Permissive CORS Configuration** (High)
4. **Missing Rate Limiting on Authentication** (High)
5. **Horizontal Privilege Escalation via Tags** (High)
6. **Potential Path Traversal in File Uploads** (Critical)

## Structure

Each vulnerability has its own subdirectory containing:
- `README.md` - Detailed vulnerability description
- `exploit.py` or `exploit.sh` - Automated exploitation script
- `manual-steps.md` - Manual exploitation steps
- `evidence/` - Screenshots and logs demonstrating the vulnerability

## Prerequisites

- Python 3.8+
- `requests` library: `pip install requests`
- `curl` command-line tool
- A test FashionLab instance

## Usage

1. Configure your test environment URL in `config.py`
2. Run individual exploits from their respective directories
3. Review the evidence generated

## Responsible Disclosure

If you discover any vulnerabilities not covered here, please follow responsible disclosure practices and report them to the FashionLab security team.