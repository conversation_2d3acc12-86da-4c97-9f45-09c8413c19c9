# Vulnerability: Missing Rate Limiting on Authentication

## Summary
The authentication endpoints lack rate limiting, allowing unlimited login attempts. This enables brute force attacks on user accounts, potentially compromising any account with a weak password.

## Risk Level: HIGH

## Impact
- Account takeover through password brute forcing
- Credential stuffing attacks
- Resource exhaustion (DoS)
- Enumeration of valid email addresses
- Bypassing account lockout mechanisms

## Technical Details

### Vulnerable Endpoints
- `/auth/v1/token?grant_type=password` - Login endpoint
- `/auth/v1/recover` - Password reset endpoint
- `/auth/v1/signup` - Registration endpoint

### Missing Protections
- No request rate limiting
- No account lockout after failed attempts
- No CAPTCHA or proof-of-work
- No progressive delays
- No IP-based blocking

## Attack Scenarios

### 1. Targeted Brute Force
- Attacker targets specific high-value account
- Tries common passwords and variants
- No limit on attempts

### 2. Credential Stuffing
- Use leaked password databases
- Test email/password combinations
- Automate at high speed

### 3. User Enumeration
- Different responses for valid/invalid emails
- Build list of valid platform users

## Evidence
- Successful brute force demonstration
- Hundreds of requests without blocking
- Account compromise proof
- Response time analysis

## Remediation
1. Implement rate limiting (e.g., 5 attempts per 15 minutes)
2. Add exponential backoff for failed attempts
3. Implement CAPTCHA after 3 failed attempts
4. Add account lockout mechanisms
5. Monitor and alert on suspicious patterns
6. Use fail2ban or similar IP blocking
7. Implement proper logging of auth attempts