# Vulnerability: Horizontal Privilege Escalation via Tag System

## Summary
The tag system uses collection-scoped tags but may allow users to create or access tags from other collections within the same organization, potentially leading to information disclosure or unauthorized data categorization.

## Risk Level: HIGH

## Impact
- Access to tags from other collections
- Information leakage about other projects
- Ability to pollute tag namespace
- Cross-collection data correlation
- Potential for social engineering

## Technical Details

### Vulnerable Component
- Tag filtering: `collection_id.eq.${collectionId}`
- Organization-scoped tags without proper collection isolation
- Shared tag namespace within organizations

### Attack Vector
1. User in Collection A creates tags
2. User discovers Collection B's ID
3. User queries tags with Collection B's ID
4. System may return tags from Collection B

## Exploitation Scenario

### Information Gathering
- Discover naming conventions
- Learn about unreleased products
- Map organizational structure
- Identify sensitive projects

### Data Pollution
- Create misleading tags
- Interfere with other teams
- Cause confusion in workflows

## Evidence
- Cross-collection tag access proof
- Tag creation in unauthorized collections
- Information leakage demonstration

## Remediation
1. Enforce strict collection boundaries for tags
2. Add collection ownership validation
3. Implement tag access audit logs
4. Use collection-specific tag namespaces
5. Add permission checks for tag operations
6. Consider role-based tag management