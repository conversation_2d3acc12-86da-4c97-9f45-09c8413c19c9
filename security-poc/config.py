"""
Configuration for security PoC exploits
"""

# Target environment (ONLY USE TEST/DEVELOPMENT ENVIRONMENTS)
TARGET_URL = "http://localhost:8080"  # Change to your test instance
SUPABASE_URL = "http://127.0.0.1:54321"  # Local Supabase instance
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"

# Test credentials (from development environment)
TEST_CREDENTIALS = [
    {"email": "<EMAIL>", "password": "test123456", "role": "Platform Super Admin"},
    {"email": "<EMAIL>", "password": "test123456", "role": "Platform Admin"},
    {"email": "<EMAIL>", "password": "test123456", "role": "Brand Admin (Vero <PERSON>da)"},
    {"email": "<EMAIL>", "password": "test123456", "role": "Brand Member (Vero Moda)"},
    {"email": "<EMAIL>", "password": "test123456", "role": "External Retoucher"},
    {"email": "<EMAIL>", "password": "test123456", "role": "External Prompter"},
    {"email": "<EMAIL>", "password": "test123456", "role": "Brand Admin (H&M)"},
]

# Headers
DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "apikey": SUPABASE_ANON_KEY,
}

# Timeouts
REQUEST_TIMEOUT = 30

# Output settings
EVIDENCE_DIR = "evidence"
VERBOSE = True