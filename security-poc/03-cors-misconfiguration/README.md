# Vulnerability: Overly Permissive CORS Configuration

## Summary
The Supabase Edge Functions are configured with `Access-Control-Allow-Origin: *`, allowing any website to make authenticated requests to the API. This enables cross-origin attacks where malicious websites can access user data.

## Risk Level: HIGH

## Impact
- Cross-site data theft
- Unauthorized API access from malicious domains
- Session hijacking potential
- Data exfiltration to attacker-controlled servers

## Technical Details

### Location
- File: `/supabase/functions/_shared/cors.ts`
- Configuration: `'Access-Control-Allow-Origin': '*'`

### Vulnerable Code
```typescript
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Allows ALL origins
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
};
```

## Attack Scenario
1. User logs into FashionLab
2. User visits attacker's website (while still logged in)
3. Attacker's site makes API requests using user's credentials
4. Due to CORS wildcard, browser allows the requests
5. Attacker steals user's data

## Exploitation Requirements
1. <PERSON>ti<PERSON> must be logged into FashionLab
2. <PERSON>ti<PERSON> must visit attacker's website
3. Attacker needs to know API endpoints

## Evidence
- Malicious website successfully accessing API
- Cross-origin requests being allowed
- Data exfiltration proof

## Remediation
1. Replace wildcard with specific allowed origins
2. Implement origin validation
3. Use credentials with CORS properly
4. Add CSRF protection
5. Implement proper session management