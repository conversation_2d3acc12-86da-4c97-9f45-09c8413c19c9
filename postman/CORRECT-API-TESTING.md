# Fashion Lab API Testing - Correct Approach

## Understanding the Authentication

The token `2ms4LQBtkbvJ8RwFmBht` is the **actual API token** for Fashion Lab API. You don't need to generate a JWT - this token IS your authentication.

## Testing in Postman

### For V2 Endpoint (Image-based Generation)

1. **URL**: `https://fashionlab.notfirst.rodeo/generate-image-v2`
2. **Method**: POST
3. **Headers**:
   ```
   Authorization: Bearer 2ms4LQBtkbvJ8RwFmBht
   ```
4. **Body**: Form-data with:
   - `model` (file) - Model photo
   - `garment` (file) - Garment photo
   - `pose` (file) - Pose reference
   - `background` (file) - Background image
   - `prompt` (text) - Your description

### Complete cURL Example:

```bash
curl --request POST \
  --url https://fashionlab.notfirst.rodeo/generate-image-v2 \
  --header 'Authorization: Bearer 2ms4LQBtkbvJ8RwFmBht' \
  --header 'content-type: multipart/form-data' \
  --form model=@model.jpg \
  --form garment=@garment.jpg \
  --form pose=@pose.jpg \
  --form background=@background.jpg \
  --form 'prompt=place this brunette small size thin female model wearing these grey pants with a sweater paired with black sandals in this beautiful beach background. full height, 3/4 angle'
```

### For V1 Endpoint (Text-based Generation)

```bash
curl -X POST https://fashionlab.notfirst.rodeo/generate-image \
  -H "Authorization: Bearer 2ms4LQBtkbvJ8RwFmBht" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "elegant black dress with lace details",
    "loras": "fashion_model_12345",
    "loraweights": ".9",
    "model": "Laila",
    "images": "3"
  }'
```

## Check Queue Status

After getting a `queue_id`:

```bash
curl -X GET https://fashionlab.notfirst.rodeo/queue/YOUR_QUEUE_ID \
  -H "Authorization: Bearer 2ms4LQBtkbvJ8RwFmBht"
```

## The JWT Edge Function

The JWT edge function in the demo (`/supabase/functions/jwt/`) is likely for a different purpose - possibly for internal authentication between services, NOT for calling the Fashion Lab API.

## Summary

- **API Token**: `2ms4LQBtkbvJ8RwFmBht` (use this directly)
- **No JWT generation needed** for Fashion Lab API calls
- **Just add the token** to your Authorization header as `Bearer 2ms4LQBtkbvJ8RwFmBht`