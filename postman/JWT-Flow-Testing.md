# Testing JWT Authentication Flow in Postman

## Overview

This guide shows how to test the secure JWT flow where clients get temporary tokens instead of using the master API key.

## Setup Environment Variables

1. Create a new Postman Environment with:
   - `supabase_url`: `http://localhost:54321`
   - `supabase_anon_key`: Your Supabase anon key
   - `jwt_token`: (will be set automatically)
   - `jwt_secret`: `your-secret-key-here`

## Step-by-Step Testing

### 1. Get JWT Token

**Request:**
- Method: `POST`
- URL: `{{supabase_url}}/functions/v1/fashionlab-jwt`
- Headers:
  - `Authorization`: `Bearer {{supabase_anon_key}}`

**Tests Script (to save token):**
```javascript
const response = pm.response.json();
pm.environment.set("jwt_token", response.token);
console.log("JWT saved:", response.token.substring(0, 20) + "...");
```

**Expected Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 3600,
  "token_type": "Bearer"
}
```

### 2. Test V2 Image Generation with JWT

**Request:**
- Method: `POST`  
- URL: `{{supabase_url}}/functions/v1/fashion-lab-proxy/generate-image-v2`
- Headers:
  - `Authorization`: `Bearer {{jwt_token}}`
- Body (form-data):
  - `model`: (file)
  - `garment`: (file)
  - `pose`: (file)
  - `background`: (file)
  - `prompt`: (text)

### 3. Check Queue Status with JWT

**Request:**
- Method: `GET`
- URL: `{{supabase_url}}/functions/v1/fashion-lab-proxy/queue/{{queue_id}}`
- Headers:
  - `Authorization`: `Bearer {{jwt_token}}`

## Complete Postman Collection

Import this collection:

```json
{
  "info": {
    "name": "Fashion Lab JWT Flow",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "1. Get JWT Token",
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "const response = pm.response.json();",
              "pm.environment.set('jwt_token', response.token);",
              "pm.environment.set('jwt_expires_at', new Date(Date.now() + response.expires_in * 1000).toISOString());",
              "",
              "pm.test('JWT token received', function() {",
              "    pm.expect(response).to.have.property('token');",
              "    pm.expect(response.token).to.be.a('string');",
              "});",
              "",
              "console.log('JWT Token saved to environment');",
              "console.log('Expires at:', pm.environment.get('jwt_expires_at'));"
            ]
          }
        }
      ],
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{supabase_anon_key}}"
          }
        ],
        "url": "{{supabase_url}}/functions/v1/fashionlab-jwt"
      }
    },
    {
      "name": "2. Generate Image V2 (with JWT)",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{jwt_token}}"
          }
        ],
        "body": {
          "mode": "formdata",
          "formdata": [
            {
              "key": "model",
              "type": "file",
              "src": "model.jpg"
            },
            {
              "key": "garment",
              "type": "file",
              "src": "garment.jpg"
            },
            {
              "key": "pose",
              "type": "file",
              "src": "pose.jpg"
            },
            {
              "key": "background",
              "type": "file",
              "src": "background.jpg"
            },
            {
              "key": "prompt",
              "value": "brunette model wearing grey pants",
              "type": "text"
            }
          ]
        },
        "url": "{{supabase_url}}/functions/v1/fashion-lab-proxy/generate-image-v2"
      }
    },
    {
      "name": "3. Check Queue Status (with JWT)",
      "request": {
        "method": "GET",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{jwt_token}}"
          }
        ],
        "url": "{{supabase_url}}/functions/v1/fashion-lab-proxy/queue/{{queue_id}}"
      }
    },
    {
      "name": "Test JWT Expiration",
      "event": [
        {
          "listen": "prerequest",
          "script": {
            "exec": [
              "// Check if JWT is expired",
              "const expiresAt = pm.environment.get('jwt_expires_at');",
              "if (expiresAt && new Date(expiresAt) < new Date()) {",
              "    console.log('JWT is expired, please get a new token');",
              "    pm.execution.skipRequest();",
              "}"
            ]
          }
        }
      ],
      "request": {
        "method": "GET",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{jwt_token}}"
          }
        ],
        "url": "{{supabase_url}}/functions/v1/fashion-lab-proxy/health"
      }
    }
  ]
}
```

## Direct API Testing (Comparison)

To understand the difference, here's how you would call the API directly with the master token:

**NOT RECOMMENDED - Direct API call:**
```
POST https://fashionlab.notfirst.rodeo/generate-image-v2
Authorization: Bearer 2ms4LQBtkbvJ8RwFmBht
```

**RECOMMENDED - Through JWT proxy:**
```
POST {{supabase_url}}/functions/v1/fashion-lab-proxy/generate-image-v2
Authorization: Bearer {{jwt_token}}
```

## Benefits of JWT Flow

1. **Security**: Master token stays on server
2. **Tracking**: Know which user made each request
3. **Control**: Can revoke access per user
4. **Limits**: Can implement rate limiting
5. **Expiration**: Tokens auto-expire

## Troubleshooting

### JWT Expired
- Get a new token from step 1
- Tokens expire after 1 hour

### 401 Unauthorized
- Check JWT is in Authorization header
- Verify format: `Bearer YOUR_JWT_TOKEN`
- Ensure JWT secret matches on server

### 500 Server Error
- Check edge function logs
- Verify FASHIONLAB_API_TOKEN is set
- Ensure FASHIONLAB_JWT_SECRET is set