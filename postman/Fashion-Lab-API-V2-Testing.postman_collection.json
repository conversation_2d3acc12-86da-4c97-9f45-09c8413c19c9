{"info": {"_postman_id": "fashion-lab-v2-testing", "name": "Fashion Lab API V2 Testing", "description": "Complete collection for testing Fashion Lab API v2 with JWT authentication", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "item": [{"name": "Setup & Authentication", "item": [{"name": "1. Get JWT from Supabase Edge Function", "event": [{"listen": "test", "script": {"exec": ["// Extract token from response", "const response = pm.response.json();", "if (response.token) {", "    pm.collectionVariables.set('jwt_token', response.token);", "    console.log('JWT Token saved to collection variables');", "} else {", "    console.error('No token in response');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{supabase_anon_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{supabase_url}}/functions/v1/fashionlab-jwt", "host": ["{{supabase_url}}"], "path": ["functions", "v1", "fashionlab-jwt"]}}}, {"name": "2. Use Hardcoded Token", "event": [{"listen": "prerequest", "script": {"exec": ["// Set the hardcoded token", "pm.collectionVariables.set('jwt_token', '2ms4LQBtkbvJ8RwFmBht');", "console.log('Using hardcoded token: 2ms4LQBtkbvJ8RwFmBht');"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://postman-echo.com/get", "protocol": "https", "host": ["postman-echo", "com"], "path": ["get"]}, "description": "This is a dummy request that sets the hardcoded token. Just run it to set the token variable."}}]}, {"name": "Image Generation", "item": [{"name": "Generate Image V2 (Multipart)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "model", "type": "file", "src": [], "description": "Model image file"}, {"key": "garment", "type": "file", "src": [], "description": "Garment/clothing image file"}, {"key": "pose", "type": "file", "src": [], "description": "Reference pose image file"}, {"key": "background", "type": "file", "src": [], "description": "Background scene image file"}, {"key": "prompt", "value": "place this brunette small size thin female model wearing these grey pants with a sweater paired with black sandals in this beautiful beach background. full height, 3/4 angle", "type": "text", "description": "Text prompt describing the desired output"}]}, "url": {"raw": "https://fashionlab.notfirst.rodeo/generate-image-v2", "protocol": "https", "host": ["fashionlab", "notfirst", "rodeo"], "path": ["generate-image-v2"]}}}, {"name": "Generate Image V1 (JSON)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"prompt\": \"elegant black dress with lace details\",\n  \"loras\": \"fashion_model_12345\",\n  \"loraweights\": \".9\",\n  \"model\": \"<PERSON><PERSON>\",\n  \"images\": \"3\"\n}"}, "url": {"raw": "https://fashionlab.notfirst.rodeo/generate-image", "protocol": "https", "host": ["fashionlab", "notfirst", "rodeo"], "path": ["generate-image"]}}}, {"name": "Check Queue Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "https://fashionlab.notfirst.rodeo/queue/{{queue_id}}", "protocol": "https", "host": ["fashionlab", "notfirst", "rodeo"], "path": ["queue", "{{queue_id}}"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "supabase_url", "value": "http://127.0.0.1:54321", "type": "string"}, {"key": "supabase_anon_key", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "queue_id", "value": "", "type": "string"}]}