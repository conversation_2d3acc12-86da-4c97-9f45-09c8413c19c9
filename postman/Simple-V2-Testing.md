# Simple Fashion Lab API V2 Testing Guide

## The Key Point

**The token `2ms4LQBtkbvJ8RwFmBht` is your API key.** Use it directly - no JWT generation needed!

## Test in Postman - Step by Step

### 1. Create a New Request
- Method: `POST`
- URL: `https://fashionlab.notfirst.rodeo/generate-image-v2`

### 2. Set Authorization
Go to the **Authorization** tab:
- Type: `Bearer Token`
- Token: `2ms4LQBtkbvJ8RwFmBht`

### 3. Set Body
Go to the **Body** tab:
- Select `form-data`
- Add these fields:

| Key | Type | Value |
|-----|------|-------|
| model | File | Select your model image |
| garment | File | Select your garment image |
| pose | File | Select your pose image |
| background | File | Select your background image |
| prompt | Text | Your description (e.g., "brunette model wearing grey pants...") |

### 4. Send Request
Click **Send** and you'll get a response like:
```json
{
  "queue_id": "abc123",
  "status": "pending"
}
```

### 5. Check Status
Create another request:
- Method: `GET`
- URL: `https://fashionlab.notfirst.rodeo/queue/abc123` (use your queue_id)
- Authorization: `Bearer 2ms4LQBtkbvJ8RwFmBht`

## Quick cURL Test

```bash
# Generate image
curl -X POST https://fashionlab.notfirst.rodeo/generate-image-v2 \
  -H "Authorization: Bearer 2ms4LQBtkbvJ8RwFmBht" \
  -F "model=@model.jpg" \
  -F "garment=@garment.jpg" \
  -F "pose=@pose.jpg" \
  -F "background=@background.jpg" \
  -F "prompt=your description here"

# Check status (replace YOUR_QUEUE_ID)
curl -X GET https://fashionlab.notfirst.rodeo/queue/YOUR_QUEUE_ID \
  -H "Authorization: Bearer 2ms4LQBtkbvJ8RwFmBht"
```

## That's It!

No JWT generation, no complex setup. Just use the token `2ms4LQBtkbvJ8RwFmBht` directly in your Authorization header.