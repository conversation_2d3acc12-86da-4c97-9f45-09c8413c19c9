# Fashion Lab API Testing with Postman

This guide explains how to generate JWT tokens and test the Fashion Lab API v2 endpoint using Postman.

## Quick Start

### Option 1: Use the Hardcoded Token (Simplest)

The Fashion Lab API accepts the token: `2ms4LQBtkbvJ8RwFmBht`

1. In Postman, add this header to your request:
   - **Key**: `Authorization`
   - **Value**: `Bearer 2ms4LQBtkbvJ8RwFmBht`

### Option 2: Generate a JWT Token Locally

#### Using Node.js:
```bash
cd scripts
node generate-jwt-token.js
```

#### Using Python:
```bash
cd scripts
python3 generate-jwt-token.py
```

#### With custom secret:
```bash
FASHIONLAB_JWT_SECRET=mysecret node generate-jwt-token.js
```

### Option 3: Use Supabase Edge Function

If you have the edge function deployed:
```bash
curl -X POST http://127.0.0.1:54321/functions/v1/fashionlab-jwt \
  -H "Authorization: Bearer YOUR_SUPABASE_ANON_KEY"
```

## Testing in Postman

### 1. Import the Collection

1. Open Postman
2. Click "Import" button
3. Select the file: `Fashion-Lab-API-V2-Testing.postman_collection.json`

### 2. Set Up Images

For the v2 endpoint, you need 4 image files:
- **model**: Photo of the model
- **garment**: Photo of the clothing item
- **pose**: Reference pose
- **background**: Background scene

### 3. Make the Request

#### Headers:
```
Authorization: Bearer 2ms4LQBtkbvJ8RwFmBht
Content-Type: multipart/form-data (auto-set by Postman)
```

#### Body (form-data):
- `model` - (file) Select model image
- `garment` - (file) Select garment image  
- `pose` - (file) Select pose image
- `background` - (file) Select background image
- `prompt` - (text) Your prompt text

#### Example Request:
```
POST https://fashionlab.notfirst.rodeo/generate-image-v2
```

### 4. Check Queue Status

After getting a `queue_id` from the generation request:

```
GET https://fashionlab.notfirst.rodeo/queue/{queue_id}
Authorization: Bearer 2ms4LQBtkbvJ8RwFmBht
```

## Sample Test Images

If you need test images, run:
```bash
cd scripts
./test-fashionlab-api-v2.sh
```

This creates sample 1x1 pixel test images:
- `test_model.jpg`
- `test_garment.jpg`
- `test_pose.jpg`
- `test_background.jpg`

## Response Format

### Generation Response:
```json
{
  "queue_id": "unique_identifier",
  "status": "pending"
}
```

### Queue Status Response:
```json
{
  "status": "completed",
  "data": {
    "images": ["/path/to/generated/image.jpg"]
  }
}
```

## Troubleshooting

### 401 Unauthorized
- Check that the Authorization header is properly formatted: `Bearer TOKEN`
- Ensure the token hasn't expired (if using JWT)

### 400 Bad Request
- Verify all 4 image files are attached
- Check that the prompt field is included

### Connection Issues
- Verify the API endpoint: `https://fashionlab.notfirst.rodeo`
- Check your internet connection

## Advanced Testing

### Using Variables in Postman

The collection includes variables:
- `{{jwt_token}}` - Your authentication token
- `{{queue_id}}` - For queue status checks

### Automated Testing

Use the collection runner to test multiple scenarios:
1. Different image combinations
2. Various prompt styles
3. Queue status polling

### Environment Setup

Create a Postman environment with:
```
jwt_token: 2ms4LQBtkbvJ8RwFmBht
api_base_url: https://fashionlab.notfirst.rodeo
```

## Security Note

The token `2ms4LQBtkbvJ8RwFmBht` is for testing purposes. In production, use proper JWT tokens with appropriate expiration times.