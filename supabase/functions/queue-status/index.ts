import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import { corsHeaders } from '../_shared/cors.ts'
import * as jose from 'https://deno.land/x/jose@v4.13.1/index.ts'

interface QueueStatusRequest {
  queue_id: string
  collection_id?: string // Optional: for storing images
  store_images?: boolean // Optional: whether to store images in Supabase Storage
  prompt?: string // Optional: the prompt used for generation
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // Get the user from the Authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('Missing Authorization header')
    }
    
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      throw new Error('Invalid authentication')
    }
    // Parse request body
    const body: QueueStatusRequest = await req.json()
    
    if (!body.queue_id) {
      throw new Error('queue_id is required')
    }
    
    // Handle mock/timeout queue IDs for testing
    if (body.queue_id.startsWith('mock-') || body.queue_id.startsWith('timeout-')) {
      console.log('Handling mock queue ID:', body.queue_id)
      return new Response(
        JSON.stringify({
          queue_id: body.queue_id,
          status: 'completed',
          progress: 100,
          images: [
            'https://placehold.co/1024x1024/purple/white?text=Mock+Image+1',
            'https://placehold.co/1024x1024/blue/white?text=Mock+Image+2'
          ],
          metadata: {
            model_name: 'Mock Model',
            prompt_used: 'Mock generation for testing',
            note: 'This is a mock response for testing purposes'
          },
          error: null
        }),
        { 
          headers: { 
            ...corsHeaders,
            'Content-Type': 'application/json'
          } 
        }
      )
    }
    
    // Generate JWT token for Fashion Lab API
    const jwtSecret = Deno.env.get('FASHIONLAB_JWT_SECRET')

    if (!jwtSecret) {
      console.error('FASHIONLAB_JWT_SECRET environment variable is not set')
      throw new Error('Fashion Lab API not configured')
    }

    // Create JWT token for Fashion Lab API
    const secret = new TextEncoder().encode(jwtSecret)
    const fashionLabToken = await new jose.SignJWT({
      iss: 'fashionlab-app',
      scope: 'image:generate image:refine',
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('1h')
      .sign(secret)
    
    // Call Fashion Lab API to check queue status
    const response = await fetch(`https://fashionlab.notfirst.rodeo/queue/${body.queue_id}`, {
      method: 'GET',
      headers: {
        'Authorization': `jwt ${fashionLabToken}`,
      },
    })
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('Fashion Lab API error:', errorText)
      throw new Error(`Fashion Lab API error: ${response.status} ${errorText}`)
    }
    
    const result = await response.json()
    console.log('Fashion Lab queue status:', JSON.stringify(result, null, 2))
    console.log('Request parameters:', { queue_id: body.queue_id, collection_id: body.collection_id, store_images: body.store_images })
    
    // Transform the response to match our expected format
    let transformedResponse: any = {
      queue_id: result.queue_id || body.queue_id,
      status: result.status,
      progress: result.progress || 0,
      error: result.error,
    }
    
    // Handle different status values
    if (result.status === 'completed') {
      transformedResponse.progress = 100
      
      // Extract and process images
      if (result.data?.images && Array.isArray(result.data.images)) {
        const processedImages = []
        
        // First, normalize all image URLs
        const imageUrls = result.data.images.map((imageData: any) => {
          if (typeof imageData === 'string') {
            if (imageData.startsWith('http')) {
              return imageData
            } else if (imageData.includes('/')) {
              return `https://fashionlab.notfirst.rodeo${imageData}`
            }
          }
          return null
        }).filter(Boolean)
        
        // If store_images is requested and we have a collection_id
        if (body.store_images && body.collection_id) {
          console.log('Storing images in Supabase Storage...')
          console.log('Image URLs to process:', imageUrls)
          
          // Verify user has access to the collection
          const { data: collection, error: collectionError } = await supabase
            .from('collections')
            .select('id, organization_id')
            .eq('id', body.collection_id)
            .single()
          
          if (collectionError || !collection) {
            throw new Error('Invalid collection ID or no access')
          }
          
          // Check if user is a platform admin first
          const { data: userData } = await supabase
            .from('users')
            .select('role')
            .eq('id', user.id)
            .single()
          
          const isPlatformAdmin = userData?.role === 'platform_admin' || userData?.role === 'platform_super'
          
          // Verify user has access (platform admins always have access)
          if (!isPlatformAdmin) {
            const { data: membership } = await supabase
              .from('organization_memberships')
              .select('id')
              .eq('user_id', user.id)
              .eq('organization_id', collection.organization_id)
              .single()
            
            if (!membership) {
              throw new Error('User is not a member of this organization')
            }
          }
          
          // Download and store each image
          for (let i = 0; i < imageUrls.length; i++) {
            const imageUrl = imageUrls[i]

            try {
              // Download image from Fashion Lab
              console.log(`Processing image ${i + 1}/${imageUrls.length}:`, imageUrl)
              const imageResponse = await fetch(imageUrl, {
                headers: {
                  'Authorization': `jwt ${fashionLabToken}`,
                },
              })
              
              if (!imageResponse.ok) {
                console.error(`Failed to download image ${i + 1}: HTTP ${imageResponse.status}`)
                console.error('Image URL:', imageUrl)
                const errorText = await imageResponse.text()
                console.error('Error response:', errorText)
                processedImages.push(imageUrl) // Fallback to original URL
                continue
              }

              console.log(`Successfully downloaded image ${i + 1}, size: ${imageResponse.headers.get('content-length')} bytes`)
              
              const imageBlob = await imageResponse.blob()
              const arrayBuffer = await imageBlob.arrayBuffer()
              const uint8Array = new Uint8Array(arrayBuffer)
              
              // Generate storage path
              const timestamp = Date.now()
              const fileName = `${timestamp}_${body.queue_id}_${i + 1}.png`
              const storagePath = `${collection.organization_id}/${body.collection_id}/generated/${fileName}`
              
              // Upload to Supabase Storage (ai-generated bucket)
              const { data: uploadData, error: uploadError } = await supabase.storage
                .from('ai-generated')
                .upload(storagePath, uint8Array, {
                  contentType: 'image/png',
                  upsert: false,
                })
              
              if (uploadError) {
                console.error(`Failed to upload image ${i + 1} to storage:`, uploadError)
                console.error('Storage path:', storagePath)
                console.error('Image size:', uint8Array.length, 'bytes')
                processedImages.push(imageUrl) // Fallback to original URL
                continue
              }

              console.log(`Successfully uploaded image ${i + 1} to storage:`, storagePath)
              
              // Create ai_generated_images record in database
              const { data: aiGenerated, error: aiGenError } = await supabase
                .from('ai_generated_images')
                .insert({
                  queue_id: body.queue_id,
                  collection_id: body.collection_id,
                  organization_id: collection.organization_id,
                  user_id: user.id,
                  image_url: imageUrl,
                  storage_path: storagePath,
                  prompt: body.prompt || 'Fashion Lab AI Generation',
                  metadata: {
                    file_name: fileName,
                    file_size: uint8Array.length,
                    generation_time: result.data?.executionTime,
                    fashion_lab_original_url: imageUrl,
                    image_index: i + 1,
                  },
                  selected: false,
                })
                .select()
                .single()
              
              if (aiGenError) {
                console.error('Failed to create ai_generated_images record:', aiGenError)
                console.error('Insert data was:', {
                  queue_id: body.queue_id,
                  collection_id: body.collection_id,
                  organization_id: collection.organization_id,
                  user_id: user.id,
                  image_url: imageUrl,
                  storage_path: storagePath,
                  prompt: body.prompt || 'Fashion Lab AI Generation'
                })
                // Still return the storage URL even if DB insert fails
              } else {
                console.log('Successfully created ai_generated_images record:', aiGenerated.id)
              }
              
              // Get public URL for the stored image
              const { data: { publicUrl } } = supabase.storage
                .from('ai-generated')
                .getPublicUrl(storagePath)
              
              processedImages.push(publicUrl)
              console.log('Successfully stored image:', publicUrl)
              
            } catch (error) {
              console.error(`Error processing image ${i + 1}:`, error)
              console.error('Image URL:', imageUrl)
              console.error('Error details:', error.message, error.stack)
              processedImages.push(imageUrl) // Fallback to original URL
            }
          }
          
          transformedResponse.images = processedImages
          transformedResponse.stored = true
          console.log(`Storage complete. Processed ${processedImages.length} images out of ${imageUrls.length} total.`)
        } else {
          // Return original URLs if not storing
          console.log('Not storing images. store_images:', body.store_images, 'collection_id:', body.collection_id)
          transformedResponse.images = imageUrls
          transformedResponse.stored = false
        }
      }
      
      // Include metadata from the response
      transformedResponse.metadata = {
        message: result.data?.message,
        executionTime: result.data?.executionTime,
        delayTime: result.data?.delayTime,
        jobType: result.jobType,
        position: result.position
      }
    } else if (result.status === 'processing' || result.status === 'pending') {
      // Use progress from API if available
      transformedResponse.progress = result.progress || (result.status === 'processing' ? 50 : 10)
    } else if (result.status === 'failed') {
      transformedResponse.progress = 0
    }
    
    return new Response(
      JSON.stringify(transformedResponse),
      { 
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        } 
      }
    )
  } catch (error: unknown) {
    console.error('Error in queue-status function:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to check queue status'
    
    return new Response(
      JSON.stringify({ error: errorMessage }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        } 
      }
    )
  }
})