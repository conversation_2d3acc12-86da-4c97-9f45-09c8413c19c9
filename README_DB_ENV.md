# Database Environment Setup

This project uses environment files to manage database connections securely.

## Quick Start

1. **Copy the example files:**
   ```bash
   cp .env.local.example .env.local
   cp .env.staging.example .env.staging
   ```

2. **Add your staging password:**
   - Get the password from Supabase Dashboard > Settings > Database
   - Edit `.env.staging` and replace `your_staging_password_here` with the actual password

3. **Use the connection scripts:**
   ```bash
   # Connect to local database
   ./scripts/db-connect.sh local
   
   # Connect to staging database
   ./scripts/db-connect.sh staging
   
   # Run queries
   ./scripts/db-query.sh staging "SELECT COUNT(*) FROM users;"
   ```

## Environment Variables

### Local Database (.env.local)
- `SUPABASE_DB_HOST` - localhost
- `SUPABASE_DB_PORT` - 54322
- `SUPABASE_DB_USER` - postgres
- `SUPABASE_DB_PASSWORD` - postgres

### Staging Database (2)
- `SUPABASE_DB_HOST` - db.qnfmiotatmkoumlymynq.supabase.co
- `SUPABASE_DB_PORT` - 5432
- `SUPABASE_DB_USER` - postgres
- `SUPABASE_DB_PASSWORD` - (get from Supabase dashboard)

## Security Notes

- `.env.staging` and `.env.local` are gitignored
- Never commit passwords to git
- Use the example files as templates
- Keep your `.env` files secure with proper permissions

## For Claude/AI Assistants

The database credentials are stored in environment files:
- `.env.local` - Local database (password: postgres)
- `.env.staging` - Staging database (password must be added manually)

To use them in scripts or commands, load the appropriate file first.